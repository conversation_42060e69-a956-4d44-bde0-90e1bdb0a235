<!DOCTYPE html>
<html lang="en">
  <head>
	<meta name="generator" content="Hugo 0.111.3">
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Eclipse Layout Kernel (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="./downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="./gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="./documentation.html">Documentation</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="./reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="./support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="./blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="container px-3 py-5">

  <div class="row mb-3"><div class="col"><div class="jumbotron">
    <h1 class="display-4 mb-2">
      <img src="img/elk.svg" width="80" class="mr-3" style="display: inline-block; vertical-align: baseline;">Eclipse Layout Kernel&trade;
    </h1>
    <p class="lead">
      Ever feel like you spend most of your time getting a diagram’s layout right instead of its content?
    </p>
    <a href="https://rtsys.informatik.uni-kiel.de/elklive/" class="btn btn-success">Try it!</a>
    <a href="./downloads.html" class="btn btn-info">Download</a>
    <a href="./documentation.html" class="btn btn-warning">Documentation</a>
    <a href="./support.html" class="btn btn-primary">Support</a>

    <hr class="my-4">
    <p class="lead">
      Automatic Layout for Diagrams
    </p>
    <p>
      The Eclipse Layout Kernel (or ELK) is two things:
      a collection of layout algorithms,
      and an infrastructure that bridges the gap
      between layout algorithms and diagram viewers and editors.
    </p>
    <p>
      The drawing below shows an example layout produced with ELK. 
      It also illustrates some of ELK's special features: 
      ports (edges' attachment points on the corresponding nodes' borders)
      and hierarchical nodes (nodes that contain further nodes as children).
      Note that ELK itself doesn't render the drawing 
      but only computes positions (and possibly dimensions) for the diagram elements.
    </p> 

    <img class="img-fluid d-block mx-auto" src="./img/example_layout_complexRouter.svg" style="max-width: 900px;" alt="Example Layout">

    <hr class="my-4">
    <p class="lead">
      Happy Users
    </p>

    <div class="row">
      <div class="col-4">
        <div class="card">
          <img class="card-img-top img-fluid" src="./img/reel_klassviz.jpg" alt="KlassViz">
          <div class="card-body">
            <h4 class="card-title">KlassViz</h4>
            <p class="card-text">
              <a href="https://github.com/kieler/klassviz">KlassViz</a>
              uses automatic layout to generate class diagrams in Eclipse on the fly.
            </p>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="card">
          <img class="card-img-top img-fluid" src="./img/reel_debukviz.jpg" alt="DebuKViz">
          <div class="card-body">
            <h4 class="card-title">DebuKViz</h4>
            <p class="card-text">
              <a href="https://github.com/kieler/debukviz">DebuKViz</a>
              uses automatic layout to generate visualizations of variables
              in your Java programs while debugging.
            </p>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="card">
          <img class="card-img-top img-fluid" src="./img/reel_ptolemy.jpg" alt="Ptolemy II">
          <div class="card-body">
            <h4 class="card-title">Ptolemy II</h4>
            <p class="card-text">
              <a href="http://ptolemy.eecs.berkeley.edu/ptolemyII/index.htm">Ptolemy II</a>
              uses automatic layout to make developing models graphically easier.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div></div></div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="./img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
