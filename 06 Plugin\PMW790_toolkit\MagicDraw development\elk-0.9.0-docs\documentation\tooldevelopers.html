<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Tool Developers (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../documentation.html">Documentation <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>Tool Developers</h1>

    <p>This section of the documentation is meant for tool developers who simply want to add automatic layout capabilities to their tool. Despite of its name, the Eclipse Layout Kernel is not limited to Eclipse-based applications, but can also be used in pure Java applications and even in JavaScript applications (although this is not yet available in ELK itself).</p>
<h2 id="three-layers-of-layout-goodness">Three Layers of Layout Goodness</h2>
<p>At its basic, ELK is structured in three layers, each adding more convenience functionality to lower layers:</p>
<p>
  <img
    class="img-fluid"
    
      src="../img/layout_layers.png"
    
     alt="ELK Layers"
    style = "max-height: 500px; display: block;
    margin-left: auto;
    margin-right: auto;"
  />
</p>

<p>The rest of this page tries to give you an idea of what each layer contains and when you would want to use it.</p>
<h3 id="basic-layer">Basic Layer</h3>
<div class="alert alert-simple">
  <h5>Use Cases</h5>
<ul>
<li>People who only want a data structure to represent graphs.</li>
<li>People who want to use a single layout algorithm on flat graphs in a plain Java application.</li>
<li>Actually&hellip; Use the plain Java layer.</li>
</ul>
</div>
<p>The basic layer contains the basic core functionality of the Eclipse Layout Kernel:</p>
<ol>
<li>
<p>The ElkGraph data structure. This is the data structure used by ELK to describe the graphs to compute layouts for, complete with nodes, edges, ports, and labels. Each graph element can be annotated with layout properties that influence the way the graph is laid out. Also, each graph element can be annotated with miscellaneous data that we don&rsquo;t care about, but you might.</p>
</li>
<li>
<p>Layout algorithms. This is what you came here for. Our layout algorithms are implemented on top of our graph data structure, and are pure Java implementations. These algorithms can be invoked directly, even though most applications won&rsquo;t want to do so.</p>
</li>
</ol>
<p>All of this is pure Java code, and the only library dependencies here are the <em>Eclipse Modeling Framework</em> (which also works in pure Java applications without Eclipse) and <em>Google Guava</em>. Layout configuration is done by annotating graph elements with layout properties, nothing else. All that other configuration magic is added by the upper layers.</p>
<h3 id="plain-java-layer">Plain Java Layer</h3>
<div class="alert alert-simple">
  <h5>Use Cases</h5>
<ul>
<li>People who want to use automatic layout in a Java application that is not built on Eclipse.</li>
</ul>
</div>
<p>The plain Java layer mostly adds functionality to execute layout algorithms more easily:</p>
<ol>
<li>
<p>The layout meta data service. This is where all available layout algorithms and layout properties are registered, to be queried later. The registration is either done manually (pure Java applications) or automatically (through Eclipse extension points).</p>
</li>
<li>
<p>A robust recursive graph layout engine. Given a ElkGraph, the recursive graph layout engine provides a more convenient entry point to graph layout by abstracting away from the concrete layout algorithm to be used. In theory, you are free to provide your own, custom graph layout engine, or to call layout algorithms directly. However, building upon the layout meta data service, the recursive graph layout engine knows how to handle the messy details of running automatic layout on graphs. In particular, it supports <em>hierarchy</em>: graphs where nodes can contain graphs themselves.</p>
</li>
</ol>
<p>Just like the basic layer, this is pure Java code and completely independent of Eclipse.</p>
<h3 id="eclipse-layer">Eclipse Layer</h3>
<div class="alert alert-simple">
  <h5>Use Cases</h5>
<ul>
<li>People who want to integrate automatic layout into their Eclipse-based application.</li>
</ul>
</div>
<p>The Eclipse layer integrates automatic layout with Eclipse:</p>
<ol>
<li>
<p>Layout setups. A layout setup provides the connection between a given editor or graph model and the layout kernel. This basically tells ELK, &ldquo;if the user invokes layout on my editor, here&rsquo;s how to obtain the graph to be laid out, and this is how it should be configured; now go and do your magic!&rdquo;</p>
</li>
<li>
<p>Diagram layout engine. Just like the graph layout engine provided an easy entry point to automatic layout on an ElkGraph, diagram layout engines provide an easy entry point to automatic layout given&hellip; well, <em>something you want laid out</em>, pretty much. The diagram layout engines uses the registered layout setups to find out if any of them knows how to turn that <em>something</em> into an ElkGraph and run automatic layout on it. This is the highest-level abstraction ELK provides.</p>
</li>
</ol>
<p>The advanced layer is where Eclipse dependencies come in. Everything in here is highly customisable through dependency injection.</p>

  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
        




  
  <a href="../documentation/tooldevelopers.html">
    <li class="navlevel-1 active">
      Tool Developers
    </li>
  </a>
  
    
    




  
  <a href="../documentation/tooldevelopers/graphdatastructure.html">
    <li class="navlevel-2">
      Graph Data Structure
    </li>
  </a>
  
    
    




  
  <a href="../documentation/tooldevelopers/graphdatastructure/coordinatesystem.html">
    <li class="navlevel-3">
      Coordinate System
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">
    <li class="navlevel-3">
      Layout Options
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html">
    <li class="navlevel-3">
      Spacing Options
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/graphdatastructure/jsonformat.html">
    <li class="navlevel-3">
      JSON Format
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/graphdatastructure/elktextformat.html">
    <li class="navlevel-3">
      ELK Text Format
    </li>
  </a>
  


  

  
  <a href="../documentation/tooldevelopers/usingalgorithmsdirectly.html">
    <li class="navlevel-2">
      Using Algorithms Directly
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/usingplainjavalayout.html">
    <li class="navlevel-2">
      Using Plain Java Layout
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/usingeclipselayout.html">
    <li class="navlevel-2">
      Using Eclipse Layout
    </li>
  </a>
  
    
    




  
  <a href="../documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html">
    <li class="navlevel-3">
      Connecting to ELK
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html">
    <li class="navlevel-3">
      Advanced Configuration
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html">
    <li class="navlevel-3">
      Layout View Support
    </li>
  </a>
  

  
  <a href="../documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html">
    <li class="navlevel-3">
      Dependency Injection
    </li>
  </a>
  


  


  

  
  <a href="../documentation/algorithmdevelopers.html">
    <li class="navlevel-1">
      Algorithm Developers
    </li>
  </a>
  
    
    




  
  <a href="../documentation/algorithmdevelopers/gettingeclipseready.html">
    <li class="navlevel-2">
      Getting Eclipse Ready
    </li>
  </a>
  

  
  <a href="../documentation/algorithmdevelopers/creatinganewproject.html">
    <li class="navlevel-2">
      Creating a New Project
    </li>
  </a>
  

  
  <a href="../documentation/algorithmdevelopers/metadatalanguage.html">
    <li class="navlevel-2">
      ELK Metadata Language
    </li>
  </a>
  
    
    




  
  <a href="../documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html">
    <li class="navlevel-3">
      Automatic Builds
    </li>
  </a>
  


  

  
  <a href="../documentation/algorithmdevelopers/algorithmimplementation.html">
    <li class="navlevel-2">
      Algorithm Implementation
    </li>
  </a>
  
    
    




  
  <a href="../documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html">
    <li class="navlevel-3">
      Structuring Algorithms
    </li>
  </a>
  


  

  
  <a href="../documentation/algorithmdevelopers/algorithmdebugging.html">
    <li class="navlevel-2">
      Algorithm Debugging
    </li>
  </a>
  

  
  <a href="../documentation/algorithmdevelopers/randomgraphs.html">
    <li class="navlevel-2">
      Random Graph Generation
    </li>
  </a>
  

  
  <a href="../documentation/algorithmdevelopers/unittesting.html">
    <li class="navlevel-2">
      Unit Tests
    </li>
  </a>
  


  

  
  <a href="../documentation/contributors.html">
    <li class="navlevel-1">
      ELK Contributors
    </li>
  </a>
  
    
    




  
  <a href="../documentation/contributors/developmentsetup.html">
    <li class="navlevel-2">
      Development Setup
    </li>
  </a>
  

  
  <a href="../documentation/contributors/developmentworkflow.html">
    <li class="navlevel-2">
      Development Workflow
    </li>
  </a>
  
    
    




  
  <a href="../documentation/contributors/developmentworkflow/installingwithoomph.html">
    <li class="navlevel-3">
      Installing With Oomph
    </li>
  </a>
  


  

  
  <a href="../documentation/contributors/buildingelk.html">
    <li class="navlevel-2">
      Building ELK
    </li>
  </a>
  


  


      
    
      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
