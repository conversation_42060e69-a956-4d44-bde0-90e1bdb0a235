<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Unit Tests (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../documentation.html">Documentation <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>Unit Tests</h1>

    <p>Layout algorithms are complex pieces of software and, thus, should probably be tested. Besides the usual plain JUnit tests, ELK provides a graph algorithm unit test framework based on JUnit 4. Tests written with that framework basically do three things:</p>
<ol>
<li>
<p>Load one or more graphs.</p>
</li>
<li>
<p>Optionally provide a number of graph configurations. If a test class doesn&rsquo;t specify configurations, a default configuration will be activated.</p>
</li>
<li>
<p>Run one ore more tests on each graph.</p>
</li>
</ol>
<p>For each test class, the framework then executes the following steps:</p>
<ol>
<li>
<p>Load all the graphs.</p>
</li>
<li>
<p>Produce one copy of each graph for each graph configuration. If no configuration was specified, the original graphs are left untouched.</p>
</li>
<li>
<p>Run layout on all graph copies for each active algorithm and execute the test methods on the results.</p>
</li>
</ol>
<p>Tests are thus basically run on test instances defined by three properties:</p>
<ul>
<li>
<p>A graph.</p>
</li>
<li>
<p>A configuration applied to the graph.</p>
</li>
<li>
<p>A layout algorithm run on the graph.</p>
</li>
</ul>
<p>The framework distinguishes <em>black box</em> and <em>white box</em> tests. Black box tests work as described above. White box tests do not execute once layout has finished, but while layout is still running. Layout algorithms need to explicitly support white box tests. A test class can mix black box and white box tests.</p>
<p>Writing unit tests isn&rsquo;t too hard. This page should walk you through writing and running them.</p>
<h2 id="adding-a-test-class">Adding a Test Class</h2>
<p>Unit tests should be placed in a plug-in inside the <code>test</code> folder that depends on our <code>org.eclipse.elk.alg.test</code> plug-in. All class names that end with <code>Test</code> are executed during the automatic build. A minimal test class then looks like this:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RunWith</span><span style="color:#f92672">(</span>LayoutTestRunner<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">class</span> <span style="color:#a6e22e">MyAlgorithmTest</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><p>The <code>@RunWith</code> annotation specifies that the test should be run with our layout test framework.</p>
<h2 id="specifying-graphs">Specifying Graphs</h2>
<p>There are several way to specify test graphs. There can be arbitrarily many sources for graphs, and all of the ways to specify test graphs can be combined.</p>
<h3 id="supply-graphs-directly">Supply Graphs Directly</h3>
<p>You can specify methods that supply graphs built directly in your test class, like this:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@GraphProvider</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> ElkNode <span style="color:#a6e22e">produceGraph</span><span style="color:#f92672">()</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// Build a graph here...
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h3 id="load-graphs-from-disk">Load Graphs From Disk</h3>
<p>Graphs stored in ELK&rsquo;s <a href="https://github.com/eclipse/elk-models">models repository</a> can be used directly in tests. You specify graphs to be loaded through lists of <code>ModelResourcePath</code>, which accepts paths relative to the models repository:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@GraphResourceProvider</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> List<span style="color:#f92672">&lt;</span>AbstractResourcePath<span style="color:#f92672">&gt;</span> <span style="color:#a6e22e">provideGraphs</span><span style="color:#f92672">()</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    List<span style="color:#f92672">&lt;</span>AbstractResourcePath<span style="color:#f92672">&gt;</span> paths <span style="color:#f92672">=</span> <span style="color:#66d9ef">new</span> ArrayList<span style="color:#f92672">&lt;&gt;();</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// A single file
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>    paths<span style="color:#f92672">.</span><span style="color:#a6e22e">add</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;realworld/ptolemy/hierarchical/continuous_cartracking_CarTracking.elkt&#34;</span><span style="color:#f92672">));</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// All files directly contained in a directory
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>    paths<span style="color:#f92672">.</span><span style="color:#a6e22e">add</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;realworld/ptolemy/hierarchical/**&#34;</span><span style="color:#f92672">));</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// All files contained in a directory and its subdirectories
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>    paths<span style="color:#f92672">.</span><span style="color:#a6e22e">add</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;realworld/ptolemy/**/&#34;</span><span style="color:#f92672">));</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// Only .elkt files contained in a directory and its subdirectories
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>    paths<span style="color:#f92672">.</span><span style="color:#a6e22e">add</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;realworld/ptolemy/**/&#34;</span><span style="color:#f92672">).</span><span style="color:#a6e22e">withFilter</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> FileExtensionFilter<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;elkt&#34;</span><span style="color:#f92672">)));</span>
</span></span><span style="display:flex;"><span>    
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// Only files with names starting with &#34;ci&#34; contained in a directory and its subdirectories
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>    paths<span style="color:#f92672">.</span><span style="color:#a6e22e">add</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;realworld/ptolemy/**/&#34;</span><span style="color:#f92672">).</span><span style="color:#a6e22e">withFilter</span><span style="color:#f92672">(</span><span style="color:#66d9ef">new</span> FileNameFilter<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;ci.+&#34;</span><span style="color:#f92672">)));</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">return</span> paths<span style="color:#f92672">;</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h3 id="generate-random-graphs">Generate Random Graphs</h3>
<p>ELK includes a <a href="../../documentation/algorithmdevelopers/randomgraphs.html">random graph generator</a> that can also be used to generate test graphs. There are two ways to do so.</p>
<ol>
<li>
<p>A method or a field can supply an instance of <code>GeneratorOptions</code> which is used to configure the random graph generator:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RandomGeneratorOptions</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> GeneratorOptions <span style="color:#a6e22e">generatorOptions</span><span style="color:#f92672">()</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    GeneratorOptions options <span style="color:#f92672">=</span> <span style="color:#66d9ef">new</span> GeneratorOptions<span style="color:#f92672">();</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    options<span style="color:#f92672">.</span><span style="color:#a6e22e">setProperty</span><span style="color:#f92672">(</span>options<span style="color:#f92672">.</span><span style="color:#a6e22e">GRAPH_TYPE</span><span style="color:#f92672">,</span> GeneratorOptions<span style="color:#f92672">.</span><span style="color:#a6e22e">GraphType</span><span style="color:#f92672">.</span><span style="color:#a6e22e">TREE</span><span style="color:#f92672">);</span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// Set more options...
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span>
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">return</span> options<span style="color:#f92672">;</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div></li>
<li>
<p>An <code>.elkr</code> file that specifies options can be loaded, similar to how graphs can be loaded:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RandomGeneratorFile</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> AbstractResourcePath <span style="color:#a6e22e">loadRandomGraph</span><span style="color:#f92672">()</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">return</span> <span style="color:#66d9ef">new</span> ModelResourcePath<span style="color:#f92672">(</span><span style="color:#e6db74">&#34;path/to/random/graph/file.elkr&#34;</span><span style="color:#f92672">);</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div></li>
</ol>
<h2 id="configuring-graphs">Configuring Graphs</h2>
<p>If you simply want to layout the graphs as specified and then run your tests on the results, you can skip this step. However, to test particular features and algorithms it is often necessary to customize layout properties. Again, there are several ways to do so.</p>
<h3 id="supply-layout-configurators">Supply Layout Configurators</h3>
<p>Layout configurators are objects that can apply properties to graph objects. You can supply them like this:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@ConfiguratorProvider</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> LayoutConfigurator <span style="color:#a6e22e">configurator</span><span style="color:#f92672">()</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    LayoutConfigurator layoutConfig <span style="color:#f92672">=</span> <span style="color:#66d9ef">new</span> LayoutConfigurator<span style="color:#f92672">();</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    layoutConfig<span style="color:#f92672">.</span><span style="color:#a6e22e">configure</span><span style="color:#f92672">(</span>ElkNode<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">.</span><span style="color:#a6e22e">setProperty</span><span style="color:#f92672">(</span>SOME_PROPERTY<span style="color:#f92672">,</span> SOME_PROPERTY_VALUE<span style="color:#f92672">);</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>    <span style="color:#66d9ef">return</span> layoutConfig<span style="color:#f92672">;</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h3 id="configure-graphs-dynamically">Configure Graphs Dynamically</h3>
<p>You can also define a method that expects a graph and configures that graph dynamically. That allows you to set your properties only if certain conditions are met, for example.</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@Configurator</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">void</span> <span style="color:#a6e22e">configureStuff</span><span style="color:#f92672">(</span><span style="color:#66d9ef">final</span> ElkNode graph<span style="color:#f92672">)</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    graph<span style="color:#f92672">.</span><span style="color:#a6e22e">setProperty</span><span style="color:#f92672">(</span>SOME_PROPERTY<span style="color:#f92672">,</span> SOME_PROPERTY_VALUE<span style="color:#f92672">);</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h3 id="specify-layout-algorithms">Specify Layout Algorithms</h3>
<p>Since specifying a layout algorithm is a common scenario, there are special annotations to do so that can be added to a class. To test one or more specific algorithms, use one or more <code>@Algorithm</code> annotations:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RunWith</span><span style="color:#f92672">(</span>LayoutTestRunner<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#a6e22e">@Algorithm</span><span style="color:#f92672">(</span>MyTestClassOptions<span style="color:#f92672">.</span><span style="color:#a6e22e">ALGORITHM_ID</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">class</span> <span style="color:#a6e22e">MyAlgorithmTest</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><p>To test all known algorithms, you can use the <code>@AllAlgorithms</code> annotation instead having to specify all algorithms explicitly:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RunWith</span><span style="color:#f92672">(</span>LayoutTestRunner<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#a6e22e">@AllAlgorithms</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">class</span> <span style="color:#a6e22e">MyAlgorithmTest</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><p>Each graph with each configuration is laid out once with each specified algorithm.</p>
<h3 id="default-configurations">Default Configurations</h3>
<p>Many graphs in our models repository refrain from specifying explicit sizes and labels for diagram elements. Often, however, tests need nodes to have a size and labels. Thus, your test class can specify to apply default configurations to all diagram elements:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@RunWith</span><span style="color:#f92672">(</span>LayoutTestRunner<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#a6e22e">@Algorithm</span><span style="color:#f92672">(</span>MyTestClassOptions<span style="color:#f92672">.</span><span style="color:#a6e22e">ALGORITHM_ID</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#a6e22e">@DefaultConfiguration</span><span style="color:#f92672">(</span>nodes <span style="color:#f92672">=</span> <span style="color:#66d9ef">true</span><span style="color:#f92672">,</span> ports <span style="color:#f92672">=</span> <span style="color:#66d9ef">true</span><span style="color:#f92672">,</span> edges <span style="color:#f92672">=</span> <span style="color:#66d9ef">true</span><span style="color:#f92672">,</span> edgeLabels <span style="color:#f92672">=</span> <span style="color:#66d9ef">false</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">class</span> <span style="color:#a6e22e">MyAlgorithmTest</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><p>The defaults are applied after any configuration methods. In more detail, this is what they do:</p>
<ul>
<li>Nodes
<ul>
<li>Supply a default size if size constraints are fixed and no size has been set.</li>
<li>Add a label if none exists.</li>
<li>Configure the label to be centered inside unless another node label configuration was supplied.</li>
</ul>
</li>
<li>Ports
<ul>
<li>Supply a default size if none has been set.</li>
<li>Add a label if non exists.</li>
</ul>
</li>
<li>Edges
<ul>
<li>Set edge label placement to <code>CENTER</code> if it has not been set.</li>
</ul>
</li>
<li>Edge Labels
<ul>
<li>Add a label to each edge if non exists.</li>
</ul>
</li>
</ul>
<p>The options default to the values shown in the code fragment.</p>
<h2 id="black-box-tests">Black Box Tests</h2>
<p>Now that graphs are loaded and configured, black box test methods can examine the layout result:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@Test</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">void</span> <span style="color:#a6e22e">testGraphSizeSet</span><span style="color:#f92672">(</span><span style="color:#66d9ef">final</span> ElkNode graph<span style="color:#f92672">)</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    Assert<span style="color:#f92672">.</span><span style="color:#a6e22e">assertTrue</span><span style="color:#f92672">(</span>
</span></span><span style="display:flex;"><span>        <span style="color:#e6db74">&#34;The graph size has not been set by the layout algorithm.&#34;</span><span style="color:#f92672">,</span>
</span></span><span style="display:flex;"><span>        graph<span style="color:#f92672">.</span><span style="color:#a6e22e">getWidth</span><span style="color:#f92672">()</span> <span style="color:#f92672">&gt;</span> <span style="color:#ae81ff">0</span> <span style="color:#f92672">&amp;&amp;</span> graph<span style="color:#f92672">.</span><span style="color:#a6e22e">getHeight</span><span style="color:#f92672">()</span> <span style="color:#f92672">&gt;</span> <span style="color:#ae81ff">0</span><span style="color:#f92672">);</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h2 id="white-box-tests">White Box Tests</h2>
<p>White box tests ensure that an algorithm&rsquo;s internal state matches the developer&rsquo;s expectations. Here we not only examine the overall layout result, but can look inside intermediate results produced as the algorithm progresses.</p>
<p>A white box test method needs to specify which layout processor(s) it wants to run before or after. It does so like this:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#a6e22e">@TestBeforeProcessor</span><span style="color:#f92672">(</span>NetworkSimplexLayerer<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#a6e22e">@TestAfterProcessor</span><span style="color:#f92672">(</span>NetworkSimplexLayerer<span style="color:#f92672">.</span><span style="color:#a6e22e">class</span><span style="color:#f92672">)</span>
</span></span><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">void</span> <span style="color:#a6e22e">testNetworkSimplexLayerer</span><span style="color:#f92672">(</span>Object graph<span style="color:#f92672">)</span> <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    LGraph lGraph <span style="color:#f92672">=</span> <span style="color:#f92672">(</span>LGraph<span style="color:#f92672">)</span> graph<span style="color:#f92672">;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">// Test things...
</span></span></span><span style="display:flex;"><span><span style="color:#75715e"></span><span style="color:#f92672">}</span>
</span></span></code></pre></div><p>A test class containing white box tests must be configured such that all layout algorithms it runs with are white box testable. Test setup will fail otherwise.</p>
<p>Depending on the algorithm and the input graph, it may happen that the layout processor a white box test is configured to run with never executes. By default, the framework treats such tests as having succeeded. If such a test should fail instead, simply add the <code>@FailIfNotExecuted</code> annotation.</p>
<p>A white box test will be executed upon every invocation of one of its processors. If a layout algorithm supports hierarchy, this may mean being executed several times, for example once per hierarchy leven which is being laid out. To change this, add the <code>@OnlyOnRootNode</code> annotation. Then, the test will only run if its processors are executed on what the layout algorithm considers the root node.</p>
<h3 id="supporting-white-box-tests">Supporting White Box Tests</h3>
<p>For a layout algorithm to support white box tests, its <code>AbstractLayoutProvider</code> subclass needs to implement <code>IWhiteBoxTestable</code>. The interface adds a single method: <code>setTestController(TestController controller)</code>. This supplies the test controller that controls the white box test run. The layout algorithm must then be sure to call the controller&rsquo;s <code>notifiyX(...)</code> methods as its processors are about to run or have just finished running so that tests have a chance to examine the result.</p>
<h2 id="running-tests">Running Tests</h2>
<h3 id="inside-eclipse">Inside Eclipse</h3>
<p>From Eclipse, tests can be run as plain Java JUnit tests (not plug-in tests). Three environment or system variables have to be set for the test framework to work:</p>
<ul>
<li><code>ELK_REPO</code>: Absolute path to the directory where the main ELK repository is checked out. With our default Oomph setup, the value <code>${workspace_loc:org.eclipse.elk.core}/../..</code> always works.</li>
<li><code>MODELS_REPO</code>: Absolute path to the directory where the <code>elk-models</code> repository is checked out. With our default Oomph setup, the value <code>${workspace_loc:org.eclipse.elk.core}/../../../elk-models</code> always works.</li>
</ul>
<h3 id="as-part-of-automatic-builds">As Part of Automatic Builds</h3>
<p><a href="../../documentation/contributors/buildingelk.html">This page</a> describes how to run runit tests as part of automatic builds.</p>
<h2 id="differences-to-usual-junit-tests">Differences to Usual JUnit Tests</h2>
<p>To keep the implementation simple, we currently don&rsquo;t support the following JUnit features:</p>
<ul>
<li><code>@Before</code> methods</li>
<li><code>@After</code> methods</li>
<li>Timeouts and expected exceptions</li>
</ul>
<p>Feel free to add support for these and file a pull request. :)</p>

  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
        




  
  <a href="../../documentation/tooldevelopers.html">
    <li class="navlevel-1">
      Tool Developers
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/graphdatastructure.html">
    <li class="navlevel-2">
      Graph Data Structure
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/graphdatastructure/coordinatesystem.html">
    <li class="navlevel-3">
      Coordinate System
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">
    <li class="navlevel-3">
      Layout Options
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html">
    <li class="navlevel-3">
      Spacing Options
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/jsonformat.html">
    <li class="navlevel-3">
      JSON Format
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/elktextformat.html">
    <li class="navlevel-3">
      ELK Text Format
    </li>
  </a>
  


  

  
  <a href="../../documentation/tooldevelopers/usingalgorithmsdirectly.html">
    <li class="navlevel-2">
      Using Algorithms Directly
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingplainjavalayout.html">
    <li class="navlevel-2">
      Using Plain Java Layout
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout.html">
    <li class="navlevel-2">
      Using Eclipse Layout
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html">
    <li class="navlevel-3">
      Connecting to ELK
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html">
    <li class="navlevel-3">
      Advanced Configuration
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html">
    <li class="navlevel-3">
      Layout View Support
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html">
    <li class="navlevel-3">
      Dependency Injection
    </li>
  </a>
  


  


  

  
  <a href="../../documentation/algorithmdevelopers.html">
    <li class="navlevel-1">
      Algorithm Developers
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/gettingeclipseready.html">
    <li class="navlevel-2">
      Getting Eclipse Ready
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/creatinganewproject.html">
    <li class="navlevel-2">
      Creating a New Project
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/metadatalanguage.html">
    <li class="navlevel-2">
      ELK Metadata Language
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html">
    <li class="navlevel-3">
      Automatic Builds
    </li>
  </a>
  


  

  
  <a href="../../documentation/algorithmdevelopers/algorithmimplementation.html">
    <li class="navlevel-2">
      Algorithm Implementation
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html">
    <li class="navlevel-3">
      Structuring Algorithms
    </li>
  </a>
  


  

  
  <a href="../../documentation/algorithmdevelopers/algorithmdebugging.html">
    <li class="navlevel-2">
      Algorithm Debugging
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/randomgraphs.html">
    <li class="navlevel-2">
      Random Graph Generation
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/unittesting.html">
    <li class="navlevel-2 active">
      Unit Tests
    </li>
  </a>
  


  

  
  <a href="../../documentation/contributors.html">
    <li class="navlevel-1">
      ELK Contributors
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/contributors/developmentsetup.html">
    <li class="navlevel-2">
      Development Setup
    </li>
  </a>
  

  
  <a href="../../documentation/contributors/developmentworkflow.html">
    <li class="navlevel-2">
      Development Workflow
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/contributors/developmentworkflow/installingwithoomph.html">
    <li class="navlevel-3">
      Installing With Oomph
    </li>
  </a>
  


  

  
  <a href="../../documentation/contributors/buildingelk.html">
    <li class="navlevel-2">
      Building ELK
    </li>
  </a>
  


  


      
    
      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
