<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Blog Posts on Eclipse Layout Kernel</title>
    <link>https://www.eclipse.org/elk/blog.html</link>
    <description>Recent content in Blog Posts on Eclipse Layout Kernel</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>en-us</language><atom:link href="https://www.eclipse.org/elk/blog/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>2022</title>
      <link>https://www.eclipse.org/elk/blog/2022.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/2022.html</guid>
      <description></description>
    </item>
    
    <item>
      <title>2023</title>
      <link>https://www.eclipse.org/elk/blog/2023.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/2023.html</guid>
      <description></description>
    </item>
    
    <item>
      <title>Edge Routing with Libavoid</title>
      <link>https://www.eclipse.org/elk/blog/posts/2022/22-11-17-libavoid.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2022/22-11-17-libavoid.html</guid>
      <description>By Miro Spönemann, November 17, 2022
I&amp;rsquo;m happy to announce the availability of a new Maven module / Eclipse plug-in org.eclipse.elk.alg.libavoid that offers orthogonal edge routing with fixed nodes. Credits for this work go to Ulf Rüegg, a former employee of the Real-Time and Embedded Systems group, where most of the development on ELK is happening. My contribution is merely to revive this code and include it in the building and publishing process of the ELK project.</description>
    </item>
    
    <item>
      <title>Layered: Constraining the Model</title>
      <link>https://www.eclipse.org/elk/blog/posts/2023/23-01-09-constraining-the-model.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2023/23-01-09-constraining-the-model.html</guid>
      <description>By Sören Domrös, January 9, 2023
Since it is often desired to constrain the layout in a specific way to achieve a desired layout or to increase layout stability, this post should summarize all current (ELK 0.8.1) and future (planned for 0.9.0) ways to do that. The following will focus primarily on constraining the node order. If you wish to constrain the ports have a look at the portConstraints property and this example.</description>
    </item>
    
    <item>
      <title>Rectpacking</title>
      <link>https://www.eclipse.org/elk/blog/posts/2022/22-08-31-rectpacking.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2022/22-08-31-rectpacking.html</guid>
      <description>By Sören Domrös, August 31, 2022
The rectpacking algorithm was introduced to solved common problems with the box algorithm, which cannot stack boxes in a row. The idea is to form stacks with subrows inside rows, while the size of a row is always dominated by a highest rectangle to provide a visual anchor point to &amp;ldquo;read&amp;rdquo; the rows from left to right.
Since it was a common use case of the box algorithm to add a priority to order the rectangles rectpacking uses the model order (which corresponds to the input order of the rectangles) as a criterion.</description>
    </item>
    
    <item>
      <title>Top-down Layout: Zoom in the Layout Process</title>
      <link>https://www.eclipse.org/elk/blog/posts/2023/23-04-11-topdown-layout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2023/23-04-11-topdown-layout.html</guid>
      <description>By Maximilian Kasperowski, June 9, 2023
The coming update (ELK 0.9.0) introduces a new approach to layout hierarchical graphs. Instead of increasing the size of parent nodes to fit their content we apply scaling to the content to make it fit the parent. In this post I will go over the new properties provided to achieve this and what kinds of output can be produced using top-down layout.
Scaling In addition to the existing data assigned to graph elements during layout such as positions, nodes can now also have the additional property org.</description>
    </item>
    
  </channel>
</rss>
