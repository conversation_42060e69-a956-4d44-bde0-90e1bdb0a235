from fid_utils.md_utils import *
from fid_services.finders import find_sys_ports, get_property_values, find_stereotype_by_name_in_package, find_nested_connectors, find_common_el, get_name
from fid_services.datatools import combine_el_dicts, combine_part_dicts
from fid_services.graphnav import traverse_connections
from fid_core.imports import *
from fid_utils.display_tools import fidCreator_v1, rearrange_items, create_connectors_after_rearrangement

from com.nomagic.uml2.ext.magicdraw.compositestructures.mdports import Port
from com.nomagic.uml2.ext.jmi.helpers import CoreHelper

def createSimpleNestedConnections(connectionOwner,sourcePart,targetPart):
    connector = Application.getInstance().getProject().getElementsFactory().createConnectorInstance()
    conEndStereotype = SysMLProfile.getInstance(connector).getNestedConnectorEnd()

    if isinstance(sourcePart, tuple):
        sourceElement = sourcePart[-1]
        parentParts = sourcePart[:-1]
        mh.setClientElement(connector, sourceElement)
        end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
        end1 = mh.getFirstEnd(connector)
        sh.addStereotype(end1,end)
        sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parentParts)
    else:
        mh.setClientElement(connector,sourcePart)

    if isinstance(targetPart, tuple):
        targetElement = targetPart[-1]
        parentParts = targetPart[:-1]
        mh.setSupplierElement(connector, targetElement)
        end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
        end2 = mh.getSecondEnd(connector)
        sh.addStereotype(end2,end)
        sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parentParts)
    else:
        mh.setSupplierElement(connector,targetPart)
    
    connector.setOwner(connectionOwner)
    return connector

def populate_connector_stereotype_values(connector, cable_part_property, stereotype):
    prop_names = ["cable__cable_type__name", "cable__length", "cable__length_unit__name", "cable__network_speed"]
    prop_values = get_property_values(cable_part_property.getType(), prop_names)
    #this part could use some work
    if prop_values["cable__cable_type__name"] is not None and prop_values["cable__cable_type__name"] != "-":
        sh.setStereotypePropertyValue(connector, stereotype, "cableType", prop_values["cable__cable_type__name"])
    if prop_values["cable__length"] is not None and prop_values["cable__length"] != "-" and prop_values["cable__length_unit__name"] is not None and prop_values["cable__length_unit__name"] != "-":
        cable_length = prop_values["cable__length"] + "_" + prop_values["cable__length_unit__name"]
        sh.setStereotypePropertyValue(connector, stereotype, "cableLength", cable_length)
    if prop_values["cable__network_speed"] is not None and prop_values["cable__network_speed"] != "-":
        sh.setStereotypePropertyValue(connector, stereotype, "transmissionSpeed", prop_values["cable__network_speed"])

def find_or_create_fidl2_port(element, port_name):
    owned_ports = element.getOwnedPort() or []
    p_n_fidl2 = "FIDL2: " + port_name
    port_ret = None
    for p_ in owned_ports:
        if p_n_fidl2 == p_.getName():
            port_ret = p_
            break

    if port_ret is None:
        port_ret = create_fidl2_port(element,port_name)

    return port_ret

def create_fidl2_port(element, port_name):
    new_port = Application.getInstance().getProject().getElementsFactory().createPortInstance()
    new_port.setOwner(element)
    new_port.setName("FIDL2: " + port_name)
    return new_port

def create_normalized_connector_name(name1, name2):
    """Create a normalized connector name where the lexicographically smaller name comes first.
    This ensures that A->B and B->A connections have the same connector name, preventing duplicates.
    """
    if name1 <= name2:
        return "FIDL2_" + name1 + "__" + name2
    else:
        return "FIDL2_" + name2 + "__" + name1

def normalize_existing_metaname(metaname):
    """Normalize an existing metaname to match the new naming convention.
    Handles metanames like 'FIDL2_A__B' and ensures they match 'FIDL2_A__B' regardless of original order.
    """
    if not metaname or not metaname.startswith("FIDL2_"):
        return metaname

    # Remove the FIDL2_ prefix and split on __
    parts = metaname[6:].split("__")
    if len(parts) != 2:
        return metaname

    # Recreate with normalized order
    return create_normalized_connector_name(parts[0], parts[1])

def html_table_builder_1_col(items):
    html = ['<html>', '<body>',
        '<table border="1" cellpadding="4" cellspacing="0" '
        'style="border-collapse:collapse;">',
        # header row
        '<tr><th>Removed Items</th></tr>']
    for it in items:
        html.append('<tr><td>%s</td></tr>' % str(it))
    html.extend(['</table>', '</body>', '</html>'])
    return '\n'.join(html)

def build_FIDL2_diagram(element, depth, initial_elements, disp_cons, disp_parts, site_level, all_part_ps, systemCabinetMap=None):
        safe_session_end()
        project = Application.getInstance().getProject()
        removed_elements = initial_elements - disp_parts
        removed_elements_named = [get_name(e) for e in removed_elements]
        table = html_table_builder_1_col(removed_elements_named)

        rel_parts = disp_parts.union(all_part_ps)

        if site_level:
            fid, connector_data = fidCreator_v1(element, depth, disp_cons, rel_parts, True)
        else:
            fid, connector_data = fidCreator_v1(element, depth, disp_cons, rel_parts, False)

        # Apply rearrangement after IBD creation
        if fid:
            sm.getInstance().createSession("Rearranging IBD Elements")
            try:
                # Use systemCabinetMap parameter passed from main script
                rearrange_items(fid, systemCabinetMap)
                sm.getInstance().closeSession()

                # Now create connectors after rearrangement is complete
                create_connectors_after_rearrangement(fid, connector_data)

            except Exception as e:
                printer("Error during rearrangement: " + str(e))
                sm.getInstance().closeSession()

        comments = fid.getModelElement().getOwnedComment()
        if comments:
            comm = comments[0]
        else:
            nc = project.getElementsFactory().createCommentInstance()
            nc.setBody("Removed Elements")
            fid.getModelElement().getOwnedComment().add(nc)
            comm = nc
        CoreHelper.setComment(comm, table)

class FIDL2Creator:

    def __init__(self, project):
        self.project = project
        self.fid_connector_stereotype = find_stereotype_by_name_in_package("FIDConnectorInfo","FID Connector Metadata", self.project)
        self.classifiers_list = ["Cable", "Connection", "Transceiver", "Connector"]
        self.nc_list = ["Patch Panel","Chassis", "Shout"]
        self.connectors = []
        self.model_parts = set()
        self.new_con_names = []
        self.port_key = {}
        self.display_cons = []
        self.initial_elements = {}
        self.relevant_part_properties = set()

    def create_fid_l2_main(self, element, site_connections, nested, disp_cons, disp_parts, site_level, build_diagram=True, systemCabinetMap=None, depth = 10):
        
        safe_session_start("FID L2 " + element.getName())
        initial_cons = find_nested_connectors(element, self.connectors, nested)
        data = self.extract_connection_data_port(initial_cons)
        existing_fid_names = set(data['fid_connections'])

        if site_level:
            self.build_site_level_fid_l2_data(element, data['results'], data['cables'], data['fid_connections'], initial_cons)
        if not site_level:
            #element, results, cables, fid_connections, initial_cons, element_dicts, boundary_ports, site_connections
            self.build_system_level_fid_l2_data(element, data['results'], data['cables'], data['fid_connections'], initial_cons, data['element_dict'], data['boundary_ports'], site_connections)

        data['fid_connections'] = list(existing_fid_names) + self.new_con_names
        disp_cons.extend(self.display_cons)
        disp_parts.update(self.model_parts)

        if build_diagram:
            self.build_diagram(element, depth, data['initial_elements'], disp_cons, disp_parts, site_level, systemCabinetMap)

        result = {
            "display_cons": disp_cons,
            "display_parts": disp_parts,
            "initial_elements": data['initial_elements']
        }
        return result

        #self.build_diagram(element, depth, data['initial_elements'], disp_cons, disp_parts, site_level)

    def extract_connection_data(self, initial_cons):
        connected_pairs = [con['connection_info']['connected_elements'] for con in initial_cons]
        # Filter out None values from existing connectors that don't have FIDConnectorInfo stereotypes
        fid_connections = []
        for con in initial_cons:
            if con.get('connection_stereotypes') and "FIDConnectorInfo" in [st.getName() for st in con['connection_stereotypes']]:
                metaname = con['connection_metaname']
                if metaname is not None:
                    # Normalize existing metanames to match new naming convention
                    normalized_metaname = normalize_existing_metaname(metaname)
                    fid_connections.append(normalized_metaname)
        initial_elements = {elem for pair in connected_pairs for elem in pair}

        element_dict = combine_el_dicts(initial_cons)
        boundary_ports = [el for el in element_dict.values() if el['sys_owner']]
        results, cables = traverse_connections(connected_pairs, element_dict, self.classifiers_list, self.nc_list)

        return {
            "connected_pairs": connected_pairs,
            "fid_connections": fid_connections,
            "initial_elements": initial_elements,
            "element_dict": element_dict,
            "boundary_ports": boundary_ports,
            "results": results,
            "cables": cables
        }

    def extract_connection_data_port(self, initial_cons):
        connected_pairs = [con['connection_info']['connected_elements'] for con in initial_cons]

        fid_connections = [con['connection_metaname'] for con in initial_cons if con.get('connection_stereotypes') and "FIDConnectorInfo" in [st.getName() for st in con['connection_stereotypes']]]
        initial_elements = {elem for pair in connected_pairs for elem in pair}

        connected_parts = [con['connection_info']['connected_parts'] for con in initial_cons]
        ed2 = combine_part_dicts(initial_cons)
        r1,c2 = traverse_connections(connected_parts, ed2, self.classifiers_list, self.nc_list)

        element_dict = combine_el_dicts(initial_cons)
        boundary_ports = [el for el in element_dict.values() if el['sys_owner']]

        return {
            "connected_pairs": connected_parts,
            "fid_connections": fid_connections,
            "initial_elements": initial_elements,
            "element_dict": ed2,
            "boundary_ports": boundary_ports,
            "results": r1,
            "cables": c2
        }


    def build_site_level_fid_l2_data(self, element, results, cables, fid_connections, initial_cons):
        for pair in results:
            sys1_name = pair[0].getType().getName()
            sys2_name = pair[1].getType().getName()
            sys1_port_path = None
            sys2_port_path = None
            sys1 = pair[0].getType()
            sys2 = pair[1].getType()
            p1 = None
            p2 = None

            con_name = None
            cable_node = cables.get(pair, None)
            if cable_node:
                con_name = cable_node.getName()

            bp_ret = find_sys_ports(element)

            for x in bp_ret:
                if bp_ret[x]['owning_sys'] == sys1 and bp_ret[x]['name'] == ("FIDL2: " + sys2_name):
                    sys1_port_path = bp_ret[x]['prop_path']
                    p1 = x
                if bp_ret[x]['owning_sys'] == sys2 and bp_ret[x]['name'] == ("FIDL2: " + sys1_name):
                    sys2_port_path = bp_ret[x]['prop_path']
                    p2 = x

            if sys1_port_path and sys2_port_path:
                meta_con_name = create_normalized_connector_name(sys1_name, sys2_name)
                if meta_con_name not in fid_connections and meta_con_name not in self.new_con_names:
                    newCon = createSimpleNestedConnections(element,sys1_port_path,sys2_port_path)
                    sh.addStereotype(newCon, self.fid_connector_stereotype)
                    sh.setStereotypePropertyValue(newCon, self.fid_connector_stereotype, "connectorMetaName", meta_con_name)
                    if cable_node:
                        populate_connector_stereotype_values(newCon, cable_node, self.fid_connector_stereotype)
                        newCon.setName(con_name)
                    self.display_cons.append(newCon)
                    self.new_con_names.append(meta_con_name)
                elif meta_con_name in self.new_con_names:
                    exist_con = next(c['connector_obj'] for c in initial_cons if normalize_existing_metaname(c.get('connection_metaname')) == meta_con_name)
                    self.display_cons.append(exist_con)
                self.disp_parts.add(p1)
                self.disp_parts.add(p2)

    def build_system_level_fid_l2_data(self, element, results, cables, fid_connections, initial_cons, element_dicts, boundary_ports, site_connections):
        if boundary_ports and site_connections:
            sys_con_count = {}

            for pair in site_connections:
                # Check if pair is iterable and has the expected structure
                if hasattr(pair, '__getitem__'):
                    try:
                        node1 = pair[0]
                        node2 = pair[1]
                        # Skip processing if either node is a tuple
                        if isinstance(node1, tuple) or isinstance(node2, tuple):
                            continue
                    except (KeyError, IndexError):
                        continue
                else:
                    continue
                if isinstance(node1, Port):
                    node1_owner = node1.getOwner().getName()
                else:
                    node1_owner = node1.getName()
                if isinstance(node2, Port):
                    node2_owner = node2.getOwner().getName()
                else:
                    node2_owner = node2.getName()
                
                if node1_owner != element.getName() and node2_owner != element.getName():
                    continue

                if node1.getOwner().getName() == element.getName():
                    _port = node1
                    sys_end = node2_owner
                else:
                    _port = node2
                    sys_end = node1_owner

                self.port_key[_port] = sys_end
                if sys_end not in sys_con_count and sys_end != element.getName():
                    sys_con_count[sys_end] = {'count': 0}
                if sys_end != element.getName():
                    sys_con_count[sys_end]['count'] += 1

            for sys in sys_con_count.keys():
                sys_con_count[sys]['port'] = find_or_create_fidl2_port(element, sys)

        for pair in results:

            con_name = None
            cable_node = cables.get(pair, None)
            if cable_node:
                con_name = cable_node.getName()

            p1_name = element_dicts[pair[0]]['full_name']
            p2_name = element_dicts[pair[1]]['full_name']

            if self.port_key and pair[0] in self.port_key:
                    self.model_parts.add(sys_con_count[self.port_key[pair[0]]]['port'])
                    path1 = sys_con_count[self.port_key[pair[0]]]['port']
                    p1_name = self.port_key[pair[0]]

            elif len(element_dicts[pair[0]]['prop_path']) > 1: 
                path1 = tuple(element_dicts[pair[0]]['prop_path'])
                for el in element_dicts[pair[0]]['prop_path']:
                    self.model_parts.add(el)
            else:
                path1 = element_dicts[pair[0]]['prop_path'][0]
                self.model_parts.add(path1)

            if self.port_key and pair[1] in self.port_key:
                    self.model_parts.add(sys_con_count[self.port_key[pair[1]]]['port'])
                    path2 = sys_con_count[self.port_key[pair[1]]]['port']
                    p2_name = self.port_key[pair[1]]

            elif len(element_dicts[pair[1]]['prop_path']) > 1: 
                path2 = tuple(element_dicts[pair[1]]['prop_path'])
                for el in element_dicts[pair[1]]['prop_path']:
                    self.model_parts.add(el)
            else:
                path2 = element_dicts[pair[1]]['prop_path'][0]
                self.model_parts.add(path2)

            common_el = find_common_el(path1, path2, element)
            if not common_el: 
                common_el = element

            #con_name = p2_name
            meta_con_name = create_normalized_connector_name(p1_name, p2_name)
            if meta_con_name not in fid_connections and meta_con_name not in self.new_con_names:
                newCon = createSimpleNestedConnections(common_el,path1,path2)
                sh.addStereotype(newCon, self.fid_connector_stereotype)
                sh.setStereotypePropertyValue(newCon, self.fid_connector_stereotype, "connectorMetaName", meta_con_name)
                if cable_node:
                    populate_connector_stereotype_values(newCon, cable_node, self.fid_connector_stereotype)
                    newCon.setName(con_name)
                self.display_cons.append(newCon)
                self.new_con_names.append(meta_con_name)
            elif meta_con_name not in self.new_con_names:
                exist_con = next(c['connector_obj'] for c in initial_cons if normalize_existing_metaname(c.get('connection_metaname')) == meta_con_name)
                self.display_cons.append(exist_con)  

    def build_diagram(self, element, depth, initial_elements, disp_cons, disp_parts, site_level, systemCabinetMap=None):
        safe_session_end()
        removed_elements = initial_elements - disp_parts
        removed_elements_named = [get_name(e) for e in removed_elements]
        table = html_table_builder_1_col(removed_elements_named)
        if site_level:
            fid, connector_data = fidCreator_v1(element, depth, disp_cons, disp_parts, True)
        else:
            fid, connector_data = fidCreator_v1(element, depth, disp_cons, disp_parts, False)

        # Apply rearrangement after IBD creation
        if fid:
            sm.getInstance().createSession("Rearranging IBD Elements")
            try:
                # Use systemCabinetMap parameter passed from main script
                rearrange_items(fid, systemCabinetMap)
                sm.getInstance().closeSession()

                # Now create connectors after rearrangement is complete
                create_connectors_after_rearrangement(fid, connector_data)

            except Exception as e:
                printer("Error during rearrangement: " + str(e))
                sm.getInstance().closeSession()
        
        comments = fid.getModelElement().getOwnedComment()
        if comments:
            comm = comments[0]
        else:
            nc = self.project.getElementsFactory().createCommentInstance()
            nc.setBody("Removed Elements")
            fid.getModelElement().getOwnedComment().add(nc)
            comm = nc
        CoreHelper.setComment(comm, table)
