package com.pmw790.layout;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.openapi.uml.SessionManager;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.pmw790.functions.Utilities;

import javax.swing.*;
import java.awt.Point;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static com.pmw790.functions.Utilities.Log;

/**
 * Main service for Eclipse Layout Kernel (ELK) integration with MagicDraw
 * Provides automatic graph layout for FID (Functional Interface Diagram) creation
 * with proper transaction management and threading
 */
public class ELKLayoutService {

    private static Boolean elkAvailable = null;
    private static final Object initLock = new Object();
    
    /**
     * Lazy initialization of ELK availability check
     */
    private static boolean initializeELKIfNeeded() {
        // ELK loading is always successful, so always return true
        return true;
    }

    /**
     * Check if ELK libraries are available in the classpath
     * @return true if ELK can be used, false otherwise
     */
    public static boolean isELKAvailable() {
        return true;
    }

    /**
     * Apply ELK layout to a diagram asynchronously with proper transaction management
     * Falls back to standard MagicDraw layout if ELK is not available or fails
     * 
     * @param diagram The diagram to layout
     * @param algorithmType The ELK algorithm to use ("layered", "force", "orthogonal")
     */
    public static void layoutDiagramAsync(DiagramPresentationElement diagram, String algorithmType) {
        if (!isELKAvailable()) {
            Log("ELK not available - using standard layout");
            applyFallbackLayout(diagram);
            return;
        }

        // Create background worker for ELK layout processing
        SwingWorker<Map<PresentationElement, Point>, String> layoutWorker = 
            new SwingWorker<Map<PresentationElement, Point>, String>() {
            
            @Override
            protected Map<PresentationElement, Point> doInBackground() throws Exception {
                publish("Converting diagram to ELK graph...");
                
                // Convert MagicDraw diagram to ELK graph structure
                FIDDiagramConverter converter = new FIDDiagramConverter();
                Object elkGraph = converter.convertFIDToElkGraph(diagram, null);
                
                publish("Running ELK layout algorithm...");
                
                // Apply ELK layout algorithm with specified algorithm type
                FIDLayouter layouter = new FIDLayouter();
                Object layoutResult = layouter.layoutFID(elkGraph, algorithmType);
                
                publish("Converting layout results back to MagicDraw coordinates...");
                
                // Convert ELK results back to MagicDraw coordinate system
                return converter.extractLayoutCoordinates(layoutResult, diagram);
            }
            
            @Override
            protected void process(java.util.List<String> chunks) {
                // Update progress if needed
                for (String message : chunks) {
                    Log("ELK Layout: " + message);
                }
            }
            
            @Override
            protected void done() {
                try {
                    Map<PresentationElement, Point> coordinates = get();
                    
                    // Apply results in MagicDraw transaction on EDT
                    SwingUtilities.invokeLater(() -> {
                        applyLayoutResults(diagram, coordinates);
                    });
                    
                } catch (InterruptedException | ExecutionException e) {
                    Log("ELK layout failed: " + e.getMessage());
                    SwingUtilities.invokeLater(() -> {
                        applyFallbackLayout(diagram);
                    });
                }
            }
        };
        
        layoutWorker.execute();
    }

    /**
     * Apply ELK layout specifically optimized for FID (Functional Interface Diagrams)
     * Uses hierarchical layered algorithm with cabinet grouping preservation
     *
     * @param diagram The FID diagram to layout
     */
    public static void layoutFIDAsync(DiagramPresentationElement diagram) {
        layoutDiagramAsyncWithFallbacks(diagram);
    }

    /**
     * Apply ELK layout with multiple algorithm fallbacks for better reliability
     */
    public static void layoutDiagramAsyncWithFallbacks(DiagramPresentationElement diagram) {
        String[] algorithms = {"force", "layered", "tree"};

        for (String algorithm : algorithms) {
            try {
                Log("Trying ELK algorithm: " + algorithm);
                layoutDiagramAsync(diagram, algorithm);
                Log("Successfully applied ELK algorithm: " + algorithm);
                return; // Success, exit
            } catch (Exception e) {
                Log("ELK algorithm '" + algorithm + "' failed: " + e.getMessage());
                // Continue to next algorithm
            }
        }

        // All ELK algorithms failed, use fallback
        Log("All ELK algorithms failed, using standard MagicDraw layout");
        applyFallbackLayout(diagram);
    }

    /**
     * Apply ELK layout for FID with custom cabinet grouping data
     * Integrates with display_tools.py cabinet grouping logic
     * 
     * @param diagram The FID diagram to layout
     * @param systemCabinetMap Cabinet grouping data from Jython
     */
    public static void layoutFIDWithGroupingAsync(DiagramPresentationElement diagram, Object systemCabinetMap) {
        if (!isELKAvailable()) {
            Log("ELK not available for FID layout - using standard layout");
            applyFallbackLayout(diagram);
            return;
        }

        // Create background worker for FID layout with cabinet grouping
        SwingWorker<Map<PresentationElement, Point>, String> layoutWorker = 
            new SwingWorker<Map<PresentationElement, Point>, String>() {
            
            @Override
            protected Map<PresentationElement, Point> doInBackground() throws Exception {
                publish("Converting FID diagram to ELK graph with cabinet grouping...");
                
                // Convert MagicDraw FID to ELK graph with cabinet data
                FIDDiagramConverter converter = new FIDDiagramConverter();
                Object elkGraph = converter.convertFIDToElkGraph(diagram, systemCabinetMap);
                
                publish("Running ELK layout algorithm for FID...");
                
                // Apply ELK layout algorithm optimized for FID
                FIDLayouter layouter = new FIDLayouter();
                Object layoutResult = layouter.layoutFID(elkGraph);
                
                publish("Converting layout results back to MagicDraw FID coordinates...");
                
                // Convert ELK results back to MagicDraw coordinate system
                return converter.extractLayoutCoordinates(layoutResult, diagram);
            }
            
            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    Log("ELK FID Layout: " + message);
                }
            }
            
            @Override
            protected void done() {
                try {
                    Map<PresentationElement, Point> coordinates = get();
                    
                    // Apply results in MagicDraw transaction on EDT
                    SwingUtilities.invokeLater(() -> {
                        applyLayoutResults(diagram, coordinates);
                    });
                    
                } catch (InterruptedException | ExecutionException e) {
                    Log("ELK FID layout failed: " + e.getMessage());
                    SwingUtilities.invokeLater(() -> {
                        applyFallbackLayout(diagram);
                    });
                }
            }
        };
        
        layoutWorker.execute();
    }

    /**
     * Apply layout coordinates to presentation elements within a MagicDraw transaction
     * 
     * @param diagram The target diagram
     * @param coordinates Map of presentation elements to their new coordinates
     */
    private static void applyLayoutResults(DiagramPresentationElement diagram, 
                                          Map<PresentationElement, Point> coordinates) {
        SessionManager sessionManager = SessionManager.getInstance();
        sessionManager.createSession("ELK Diagram Layout");
        
        try {
            // Apply new coordinates to each presentation element
            for (Map.Entry<PresentationElement, Point> entry : coordinates.entrySet()) {
                PresentationElement element = entry.getKey();
                Point newPosition = entry.getValue();
                
                if (element.isEditable()) {
                    try {
                        // Create new bounds with updated position
                        java.awt.Rectangle currentBounds = element.getBounds();
                        java.awt.Rectangle newBounds = new java.awt.Rectangle(
                            newPosition.x, newPosition.y,
                            currentBounds.width, currentBounds.height
                        );
                        
                        // Apply the new bounds
                        element.setBounds(newBounds);
                        
                    } catch (Exception e) {
                        Log("Warning: Could not update position for element: " + e.getMessage());
                    }
                }
            }
            
            sessionManager.closeSession();
            Log("ELK layout applied successfully");
            
        } catch (Exception e) {
            sessionManager.cancelSession();
            Log("Error applying ELK layout results: " + e.getMessage());
            applyFallbackLayout(diagram);
        }
    }

    /**
     * Fallback to standard MagicDraw layout when ELK is not available or fails
     * 
     * @param diagram The diagram to layout
     */
    private static void applyFallbackLayout(DiagramPresentationElement diagram) {
        try {
            diagram.layout(true);
            
            // Schedule a second layout pass on the UI thread after a short delay
            SwingUtilities.invokeLater(() -> {
                try {
                    diagram.layout(true, new com.nomagic.magicdraw.uml.symbols.layout.ClassDiagramLayouter());
                } catch (Exception e) {
                    // Silently handle layout exceptions
                }
            });
        } catch (Exception e) {
            Log("Warning: Could not apply fallback layout: " + e.getMessage());
        }
    }
}