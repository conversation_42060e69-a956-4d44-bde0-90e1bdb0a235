package com.pmw790.layout;

import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.magicdraw.uml.symbols.paths.PathElement;
import com.nomagic.magicdraw.uml.symbols.shapes.PartView;
import com.nomagic.magicdraw.uml.symbols.shapes.PortView;

import java.awt.Point;
import java.awt.Rectangle;
import java.util.*;

import static com.pmw790.functions.Utilities.Log;

/**
 * Handles conversion between MagicDraw FID presentation elements and ELK graph structures
 * Specialized for Internal Block Diagrams with parts, ports, and connectors
 * Preserves cabinet-based grouping and hierarchical relationships
 */
public class FIDDiagramConverter {

    // Cache for ELK reflection access - loaded dynamically if ELK is available
    private static Class<?> elkNodeClass;
    private static Class<?> elkEdgeClass;
    private static Class<?> elkPortClass;
    private static Class<?> elkGraphUtilClass;
    private static boolean initialized = false;
    private static final Object initLock = new Object();
    
    private Map<PresentationElement, Object> elementToNodeMap;
    private Map<Object, PresentationElement> nodeToElementMap;
    private Map<String, Object> cabinetGroupNodes; // Track cabinet group nodes
    
    /**
     * Lazy initialization of ELK classes
     */
    private static void initializeELKClasses() {
        if (initialized) return;
        
        synchronized (initLock) {
            if (initialized) return;
            
            try {
                // Use custom ELK classloader with fallback to standard loading
                ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
                
                elkNodeClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode");
                elkEdgeClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkEdge");
                elkPortClass = elkClassLoader.loadClass("org.eclipse.elk.graph.ElkPort");
                elkGraphUtilClass = elkClassLoader.loadClass("org.eclipse.elk.graph.util.ElkGraphUtil");
                
                initialized = true;
                Log("FIDDiagramConverter: ELK classes loaded via classloader");
            } catch (ClassNotFoundException e) {
                Log("FIDDiagramConverter: ELK classes not available - " + e.getMessage());
            } catch (Exception e) {
                Log("FIDDiagramConverter: Error loading ELK classes - " + e.getMessage());
            }
        }
    }

    public FIDDiagramConverter() {
        this.elementToNodeMap = new HashMap<>();
        this.nodeToElementMap = new HashMap<>();
        this.cabinetGroupNodes = new HashMap<>();
    }

    /**
     * Convert a MagicDraw FID diagram to an ELK graph structure
     * Specialized for Internal Block Diagrams created by fidCreator_v1
     * 
     * @param diagram The MagicDraw FID diagram to convert
     * @param systemCabinetMap Optional cabinet grouping data from display_tools.py
     * @return ELK graph root node
     */
    public Object convertFIDToElkGraph(DiagramPresentationElement diagram, Object systemCabinetMap) throws Exception {
        initializeELKClasses();
        if (elkNodeClass == null) {
            throw new RuntimeException("ELK classes not available");
        }

        // Create root graph node
        Object rootGraph = elkGraphUtilClass.getMethod("createGraph").invoke(null);
        
        // Set root graph dimensions based on diagram bounds
        Rectangle diagramBounds = diagram.getBounds();
        setElkNodeDimensions(rootGraph, Math.max(diagramBounds.width, 1200), Math.max(diagramBounds.height, 800));
        
        // If cabinet data is available, create cabinet group structure
        if (systemCabinetMap != null) {
            createCabinetGroupStructure(rootGraph, systemCabinetMap);
        }
        
        // Process all parts (components) in the FID
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PartView && pe.isVisible()) {
                processPartForFID((PartView) pe, rootGraph, systemCabinetMap, 0);
            }
        }
        
        // Process ports after parts are created
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PortView && pe.isVisible()) {
                processPortForFID((PortView) pe, rootGraph);
            }
        }
        
        // Process connectors (edges) after all nodes and ports are created
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe instanceof PathElement && pe.isVisible()) {
                processConnectorForFID((PathElement) pe, rootGraph);
            }
        }
        
        Log("Converted MagicDraw FID to ELK graph: " + elementToNodeMap.size() + " nodes");
        return rootGraph;
    }

    /**
     * Create cabinet group structure in ELK graph if cabinet data is available
     * This preserves the grouping logic from display_tools.py rearrange_items()
     */
    private void createCabinetGroupStructure(Object rootGraph, Object systemCabinetMap) throws Exception {
        // This would integrate with the cabinet grouping data structure
        // For now, create a simple structure - could be enhanced with actual cabinet data
        Log("Cabinet grouping integration placeholder - could be enhanced with actual systemCabinetMap data");
    }

    /**
     * Process a PartView (component/part) for FID and create corresponding ELK node
     * Handles the hierarchical structure created by addToFID() in display_tools.py
     */
    private void processPartForFID(PartView partView, Object parentNode, Object systemCabinetMap, int depth) throws Exception {
        // Create ELK node for this part
        Object elkNode = elkGraphUtilClass.getMethod("createNode", elkNodeClass).invoke(null, parentNode);
        
        // Store bidirectional mapping
        elementToNodeMap.put(partView, elkNode);
        nodeToElementMap.put(elkNode, partView);
        
        // Set node dimensions from presentation element bounds
        Rectangle bounds = partView.getBounds();
        // Ensure minimum size for FID components
        double width = Math.max(bounds.width, 120);
        double height = Math.max(bounds.height, 80);
        setElkNodeDimensions(elkNode, width, height);
        
        // Set node position (relative to parent)
        setElkNodePosition(elkNode, bounds.x, bounds.y);
        
        // Set identifier for debugging
        String elementName = partView.getElement() != null ? partView.getElement().getHumanName() : "Unknown";
        elkNode.getClass().getMethod("setIdentifier", String.class)
                .invoke(elkNode, "FID_Part_" + elementName);
        
        // Process child elements recursively (nested parts in FID)
        for (PresentationElement child : partView.getPresentationElements()) {
            if (child instanceof PartView && child.isVisible()) {
                processPartForFID((PartView) child, elkNode, systemCabinetMap, depth + 1);
            }
        }
    }

    /**
     * Process a PortView for FID and create corresponding ELK port
     * Handles ports created by addToFID() for connector attachment
     */
    private void processPortForFID(PortView portView, Object rootGraph) throws Exception {
        // Find the parent part node
        PresentationElement parentPE = portView.getParent();
        Object parentNode = elementToNodeMap.get(parentPE);
        
        if (parentNode != null) {
            // Create ELK port for this port view
            Object elkPort = elkGraphUtilClass.getMethod("createPort", elkNodeClass).invoke(null, parentNode);
            
            // Store mapping
            elementToNodeMap.put(portView, elkPort);
            nodeToElementMap.put(elkPort, portView);
            
            // Set port dimensions and position
            Rectangle bounds = portView.getBounds();
            setElkPortDimensions(elkPort, Math.max(bounds.width, 10), Math.max(bounds.height, 10));
            setElkPortPosition(elkPort, bounds.x, bounds.y);
            
            // Set identifier
            String portName = portView.getElement() != null ? portView.getElement().getHumanName() : "Port";
            elkPort.getClass().getMethod("setIdentifier", String.class)
                    .invoke(elkPort, "FID_Port_" + portName);
        }
    }

    /**
     * Process a connector (PathElement) for FID and create corresponding ELK edge
     * Handles connectors created by create_connectors_after_rearrangement()
     */
    private void processConnectorForFID(PathElement connector, Object rootGraph) throws Exception {
        // Get connector endpoints using MagicDraw connector API
        PresentationElement source = getConnectorSource(connector);
        PresentationElement target = getConnectorTarget(connector);
        
        if (source != null && target != null) {
            Object sourceNode = elementToNodeMap.get(source);
            Object targetNode = elementToNodeMap.get(target);
            
            if (sourceNode != null && targetNode != null) {
                // Create ELK edge between the connected elements
                Object elkEdge = createElkEdge(sourceNode, targetNode);
                
                // Set edge identifier
                String connectorName = connector.getElement() != null ? connector.getElement().getHumanName() : "Connector";
                elkEdge.getClass().getMethod("setIdentifier", String.class)
                        .invoke(elkEdge, "FID_Conn_" + connectorName);
            }
        }
    }

    /**
     * Get the source presentation element of a connector
     * Uses MagicDraw PathElement API
     */
    private PresentationElement getConnectorSource(PathElement connector) {
        try {
            // Get the client (source) of the connector
            return connector.getClient();
        } catch (Exception e) {
            Log("Warning: Could not find connector source: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get the target presentation element of a connector
     * Uses MagicDraw PathElement API  
     */
    private PresentationElement getConnectorTarget(PathElement connector) {
        try {
            // Get the supplier (target) of the connector
            return connector.getSupplier();
        } catch (Exception e) {
            Log("Warning: Could not find connector target: " + e.getMessage());
            return null;
        }
    }

    /**
     * Create an ELK edge between two nodes using reflection
     */
    private Object createElkEdge(Object sourceNode, Object targetNode) throws Exception {
        return elkGraphUtilClass.getMethod("createSimpleEdge", elkNodeClass, elkNodeClass)
                .invoke(null, sourceNode, targetNode);
    }

    /**
     * Extract layout coordinates from ELK graph result and map back to MagicDraw elements
     * 
     * @param elkGraph The laid-out ELK graph
     * @param diagram The original MagicDraw FID diagram
     * @return Map of presentation elements to their new coordinates
     */
    public Map<PresentationElement, Point> extractLayoutCoordinates(Object elkGraph, 
                                                                   DiagramPresentationElement diagram) throws Exception {
        Map<PresentationElement, Point> coordinates = new HashMap<>();
        
        // Recursively extract coordinates from ELK nodes
        extractNodeCoordinates(elkGraph, coordinates, 0, 0);
        
        Log("Extracted " + coordinates.size() + " FID element coordinates from ELK layout");
        return coordinates;
    }

    /**
     * Recursively extract coordinates from ELK nodes
     */
    private void extractNodeCoordinates(Object elkNode, Map<PresentationElement, Point> coordinates, 
                                      double parentX, double parentY) throws Exception {
        // Get the corresponding MagicDraw presentation element
        PresentationElement pe = nodeToElementMap.get(elkNode);
        
        if (pe != null) {
            // Get ELK node position
            double x = (Double) elkNode.getClass().getMethod("getX").invoke(elkNode);
            double y = (Double) elkNode.getClass().getMethod("getY").invoke(elkNode);
            
            // Convert to absolute coordinates with proper rounding
            Point absolutePosition = new Point(
                (int) Math.round(parentX + x), 
                (int) Math.round(parentY + y)
            );
            coordinates.put(pe, absolutePosition);
            
            // Process children with updated parent coordinates
            Collection<?> children = (Collection<?>) elkNode.getClass().getMethod("getChildren").invoke(elkNode);
            for (Object child : children) {
                extractNodeCoordinates(child, coordinates, parentX + x, parentY + y);
            }
        }
    }

    // Helper methods for setting ELK properties using reflection
    
    private void setElkNodeDimensions(Object elkNode, double width, double height) throws Exception {
        elkNode.getClass().getMethod("setDimensions", double.class, double.class)
                .invoke(elkNode, width, height);
    }

    private void setElkNodePosition(Object elkNode, double x, double y) throws Exception {
        elkNode.getClass().getMethod("setLocation", double.class, double.class)
                .invoke(elkNode, x, y);
    }

    private void setElkPortDimensions(Object elkPort, double width, double height) throws Exception {
        elkPort.getClass().getMethod("setDimensions", double.class, double.class)
                .invoke(elkPort, width, height);
    }

    private void setElkPortPosition(Object elkPort, double x, double y) throws Exception {
        elkPort.getClass().getMethod("setLocation", double.class, double.class)
                .invoke(elkPort, x, y);
    }
}