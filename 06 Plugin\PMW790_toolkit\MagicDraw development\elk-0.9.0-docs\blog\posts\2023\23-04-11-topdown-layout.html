<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Top-down Layout: Zoom in the Layout Process (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../documentation.html">Documentation</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../../blog.html">Blog Posts <span class="sr-only">(current)</span></a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>Top-down Layout: Zoom in the Layout Process</h1>

    <p><em>By Maximilian Kasperowski, June 9, 2023</em></p>
<p>The coming update (ELK 0.9.0) introduces a new approach to layout hierarchical graphs. Instead of increasing the size of parent nodes to fit their content we apply scaling to the content to make it fit the parent. In this post I will go over the new properties provided to achieve this and what kinds of output can be produced using top-down layout.</p>
<h2 id="scaling">Scaling</h2>
<p>In addition to the existing data assigned to graph elements during layout such as positions, nodes can now also have the additional property <code>org.eclipse.elk.topdown.scaleFactor</code>. The scale factor tells the renderer what the relative scale of the sub-layout of the node should be. The renderer needs to apply the scaling as illustrated in the following figure.
<p>
  <img
    class="img-fluid"
    
      src="../../../img/top-down-scaling.png"
    
     alt="application of top-down scale factor."
    style = "max-height: 500px; display: block;
    margin-left: auto;
    margin-right: auto;"
  />
</p>
</p>
<p>The following graph demonstrates the scale factor. The top-level red node contains a layered layout that consists of two nodes. We have assigned it a fixed width and height and in order to fit the children the computed scale factor is applied around all the children as a group. In the svg this is realized by wrapping the children in a <code>&lt;g&gt;</code> tag with a <code>transform=&quot;scale(scaleFactor)</code> attribute.
<p>
  <img
    class="img-fluid"
    
      src="../../../img/topdown-example.svg"
    
     alt="top-down layout example."
    style = "max-height: 500px; display: block;
    margin-left: auto;
    margin-right: auto;"
  />
</p>
</p>
<h2 id="usage">Usage</h2>
<p>In general there are many ways that top-down layout can be configured. I will demonstrate two main approaches that showcase all possible options in some typical configurations.</p>
<h3 id="scale-all-hierarchy-levels">Scale all hierarchy levels</h3>
<p>The first simple method is to apply some scale factor for each sublayout. This means that for each node we first set its dimensions (independent of the sublayout) and then, after layouting the children, scale the sublayout so that it fits the parent. To do this we set the following properties:</p>
<p>For all nodes:</p>
<ul>
<li><code>org.eclipse.elk.topdownLayout=true</code></li>
</ul>
<p>For the root node:</p>
<ul>
<li><code>org.eclipse.elk.topdown.nodeType=ROOT_NODE</code></li>
</ul>
<p>For all other nodes:</p>
<ul>
<li><code>org.eclipse.elk.topdown.nodeType=HIERARCHICAL_NODE</code></li>
<li><code>org.eclipse.elk.nodeSize.fixedGraphSize=true</code></li>
<li><code>org.eclipse.elk.algorithm</code> must be set to an algorithm that supports <code>org.eclipse.elk.nodeSize.fixedGraphSize</code></li>
</ul>
<h3 id="mixing-scaling-approach">Mixing scaling approach</h3>
<p>Another approach that can be used for certain graph types utilizes the <code>PARALLEL_NODE</code> node type. Using this prevents scaling on certain parts of the diagram hierarchy to retain more control over the look of the graph. Due to algorithmic limitations, this approach only works when we have <em>region-like</em> nodes, i.e., nodes that are not connected via edges and should be simply packed in some manner.</p>
<p>The two examples shown below are obtained by configuring the properties in the following way:</p>
<p>For all nodes:</p>
<ul>
<li><code>org.eclipse.elk.topdownLayout=true</code></li>
</ul>
<p>For the root node:</p>
<ul>
<li><code>org.eclipse.elk.topdown.nodeType=ROOT_NODE</code></li>
</ul>
<p>For all white regions:</p>
<ul>
<li><code>org.eclipse.elk.topdown.nodeType=HIERARCHICAL_NODE</code></li>
<li><code>org.eclipse.elk.nodeSize.fixedGraphSize=true</code></li>
<li><code>org.eclipse.elk.algorithm</code> must be set to an algorithm that supports <code>org.eclipse.elk.nodeSize.fixedGraphSize</code></li>
</ul>
<p>For all blue states:</p>
<ul>
<li><code>org.eclipse.elk.topdown.nodeType=PARALLEL_NODE</code></li>
<li><code>org.eclipse.elk.algorithm=topdownpacking</code></li>
<li><code>org.eclipse.elk.topdown.hierarchicalNodeWidth=200</code></li>
<li><code>org.eclipse.elk.topdown.hierarchicalNodeAspectRatio=1.4</code></li>
</ul>
<p>
  <img
    class="img-fluid"
    
      src="../../../img/wagon-topdown.png"
    
     alt="example of top-down layout on wagon scchart."
    style = "max-height: 500px; display: block;
    margin-left: auto;
    margin-right: auto;"
  />
</p>

<p>
  <img
    class="img-fluid"
    
      src="../../../img/controller-topdown.png"
    
     alt="example of top-down layout on controller scchart."
    style = "max-height: 500px; display: block;
    margin-left: auto;
    margin-right: auto;"
  />
</p>


  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
    
      
    
      
    
      
        




  
  <a href="../../../blog/2022.html">
    <li class="navlevel-1">
      2022
    </li>
  </a>
  
    
    




  
  <a href="../../../blog/posts/2022/22-08-31-rectpacking.html">
    <li class="navlevel-2">
      Rectpacking
    </li>
  </a>
  

  
  <a href="../../../blog/posts/2022/22-11-17-libavoid.html">
    <li class="navlevel-2">
      Edge Routing with Libavoid
    </li>
  </a>
  


  

  
  <a href="../../../blog/2023.html">
    <li class="navlevel-1">
      2023
    </li>
  </a>
  
    
    




  
  <a href="../../../blog/posts/2023/23-01-09-constraining-the-model.html">
    <li class="navlevel-2">
      Layered: Constraining the Model
    </li>
  </a>
  

  
  <a href="../../../blog/posts/2023/23-04-11-topdown-layout.html">
    <li class="navlevel-2 active">
      Top-down Layout: Zoom in the Layout Process
    </li>
  </a>
  


  


      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
