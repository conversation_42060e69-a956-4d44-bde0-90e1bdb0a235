<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>0.9.0 (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../downloads.html">Downloads <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../documentation.html">Documentation</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>0.9.0</h1>

    <ul>
<li><a href="https://projects.eclipse.org/projects/modeling.elk/releases/0.9.0">Release log</a></li>
<li><a href="https://download.eclipse.org/elk/updates/releases/0.9.0/elk-0.9.0-docs.zip">Documentation</a></li>
<li><a href="https://download.eclipse.org/elk/updates/releases/0.9.0/">Update site</a></li>
<li><a href="https://download.eclipse.org/elk/updates/releases/0.9.0/elk-0.9.0.zip">Zipped update site</a> (for offline use)</li>
<li><a href="https://repo.maven.apache.org/maven2/org/eclipse/elk/">Maven central</a> (for building pure Java projects that use ELK)</li>
</ul>
<h2 id="details">Details</h2>
<p>This is mainly a bugfix release. See GitHub for the full <a href="https://github.com/eclipse/elk/milestone/15?closed=1">list of resolved issues</a>.</p>
<h3 id="new-features-and-enhancements">New Features and Enhancements</h3>
<ul>
<li><a href="https://github.com/eclipse/elk/pull/962">#962</a>, <a href="https://github.com/eclipse/elk/pull/914">#914</a>: Added an experimental Depth-First and Breadth-First model order layerer.</li>
<li><a href="https://github.com/eclipse/elk/pull/867">#867</a>, <a href="https://github.com/eclipse/elk/pull/902/">#902</a>: Added a model order layering by node promotion.</li>
<li><a href="https://github.com/eclipse/elk/pull/956">#956</a>, <a href="https://github.com/eclipse/elk/pull/942">#942</a>, <a href="https://github.com/eclipse/elk/pull/927">#927</a>, <a href="https://github.com/eclipse/elk/pull/926">#926</a>, <a href="https://github.com/eclipse/elk/pull/921">#921</a>, <a href="https://github.com/eclipse/elk/pull/893">#893</a>, <a href="https://github.com/eclipse/elk/pull/892">#892</a>, <a href="https://github.com/eclipse/elk/pull/886">#886</a>: Added libavoid for standalone edge routing.</li>
<li><a href="https://github.com/eclipse/elk/pull/945">#945</a>, <a href="https://github.com/eclipse/elk/pull/941">#941</a>: Added option for initial rotation of radial layout.</li>
<li><a href="https://github.com/eclipse/elk/issues/947">#947</a>, <a href="https://github.com/eclipse/elk/pull/951">#951</a>, <a href="https://github.com/eclipse/elk/pull/940">#940</a>, <a href="https://github.com/eclipse/elk/pull/936">#936</a>, <a href="https://github.com/eclipse/elk/pull/932">#932,</a> <a href="https://github.com/eclipse/elk/pull/843">#843</a>: Added topdown layout and top-down rectangle packing algorithm.</li>
<li><a href="https://github.com/eclipse/elk/pull/939">#939</a>, <a href="https://github.com/eclipse/elk/issues/930">#930</a>: Added port model order.</li>
<li><a href="https://github.com/eclipse/elk/pull/922">#922</a>: KVectors now have a rotation utility method.</li>
<li><a href="https://github.com/eclipse/elk/issues/406">#406</a>, <a href="https://github.com/eclipse/elk/pull/884">#884</a>: MrTree edge routing options + interactivity and model order.</li>
<li><a href="https://github.com/eclipse/elk/pull/889">#889</a>: Added experimental bendpoints to force layout.</li>
<li><a href="https://github.com/eclipse/elk/pull/877">#877:</a> Added new model order strategy.</li>
<li><a href="https://github.com/eclipse/elk/pull/834">#834</a>, <a href="https://github.com/eclipse/elk/issues/833">#833</a>, <a href="https://github.com/eclipse/elk/pull/855">#855</a>, <a href="https://github.com/eclipse/elk/pull/843">#843</a>:  Support for fixed graphs for mrtree, force, stress, radial, and rectpacking.</li>
</ul>
<h3 id="changes">Changes</h3>
<ul>
<li><a href="https://github.com/eclipse/elk/pull/973">#973</a>, <a href="https://github.com/eclipse/elk/issues/702">#702</a>, <a href="https://github.com/eclipse/elk/issues/794">#794</a>, <a href="https://github.com/eclipse/elk/pull/900">#900</a>, <a href="https://github.com/eclipse/elk/issues/807">#807</a>: Moved from Java 8 to Java 11 while testing compliance with Java 17.</li>
<li><a href="https://github.com/eclipse/elk/pull/876">#876</a>: Renamed and reworked FORCE_MODEL_ORDER component ordering strategy to MODEL_ORDER and GROUP_MODEL_ORDER component ordering strategy.</li>
<li><a href="https://github.com/eclipse/elk/issues/651">#651</a>, <a href="https://github.com/eclipse/elk/pull/885">#885</a>: Report invalid hierarchical cross min configuration instead of fixing it.</li>
<li><a href="https://github.com/eclipse/elk/pull/865">#865</a>, <a href="https://github.com/eclipse/elk/pull/835">#835</a>: Restructure and improve rectpacking.</li>
</ul>
<h3 id="bugfixes">Bugfixes</h3>
<ul>
<li><a href="https://github.com/eclipse/elk/pull/957">#957</a>: Corrected node node spacing usage for radial layout. The node node spacing is now halved and used as a padding around the nodes.</li>
<li><a href="https://github.com/eclipse/elk/pull/946/">#946</a>, <a href="https://github.com/eclipse/elk/issues/944">#944</a>: Fix hyper edge segment splitter to actually save the best area after finding it.</li>
<li><a href="https://github.com/eclipse/elk/issues/850">#850</a>, <a href="https://github.com/eclipse/elk/pull/938">#938</a>: Port labels no longer overlap with self loops.</li>
<li><a href="https://github.com/eclipse/elk/issues/918">#918</a>, <a href="https://github.com/eclipse/elk/pull/933">#933</a>: Fix port model order comparator.</li>
<li><a href="https://github.com/eclipse/elk/pull/923">#923</a>: Wrapping strategies do now correctly work together with model order.</li>
<li><a href="https://github.com/eclipse/elk/pull/906">#906</a>, <a href="https://github.com/eclipse/elk/issues/905">#905</a>, <a href="https://github.com/eclipse/elk/pull/913">#913</a>: Fix tail edge labels.</li>
<li><a href="https://github.com/eclipse/elk/issues/869">#869</a>, <a href="https://github.com/eclipse/elk/pull/911">#911</a>, <a href="https://github.com/eclipse/elk/issues/868">#868</a>:  Model order bug fixes.</li>
<li><a href="https://github.com/eclipse/elk/issues/870">#870</a>: Fixed NoSuchElementException in network simplex node placer.</li>
<li><a href="https://github.com/eclipse/elk/pull/890">#890</a>: Correctly check whether the layered algorithm is set.</li>
<li><a href="https://github.com/eclipse/elk/pull/852">#852</a>, <a href="https://github.com/eclipse/elk/issues/841">#841</a>: Inline edge labels are now correctly centered (hopefully).</li>
<li><a href="https://github.com/eclipse/elk/issues/887">#887</a>: Miscellaneous bug fixes.</li>
</ul>
<h3 id="cleanup">Cleanup</h3>
<ul>
<li><a href="https://github.com/eclipse/elk/pull/924">#924</a>: Corrected pom license for maven central.</li>
</ul>

  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
        




  
  <a href="../../downloads/releasenotes.html">
    <li class="navlevel-1">
      Releases
    </li>
  </a>
  
    
    




  
  <a href="../../downloads/releasenotes/release-0.9.0.html">
    <li class="navlevel-2 active">
      0.9.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.8.1.html">
    <li class="navlevel-2">
      0.8.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.8.0.html">
    <li class="navlevel-2">
      0.8.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.7.1.html">
    <li class="navlevel-2">
      0.7.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.7.0.html">
    <li class="navlevel-2">
      0.7.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.6.1.html">
    <li class="navlevel-2">
      0.6.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.6.0.html">
    <li class="navlevel-2">
      0.6.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.5.0.html">
    <li class="navlevel-2">
      0.5.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.4.1.html">
    <li class="navlevel-2">
      0.4.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.4.0.html">
    <li class="navlevel-2">
      0.4.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.3.0.html">
    <li class="navlevel-2">
      0.3.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.2.3.html">
    <li class="navlevel-2">
      0.2.3
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.2.2.html">
    <li class="navlevel-2">
      0.2.2
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.2.1.html">
    <li class="navlevel-2">
      0.2.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.2.0.html">
    <li class="navlevel-2">
      0.2.0
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.1.1.html">
    <li class="navlevel-2">
      0.1.1
    </li>
  </a>
  

  
  <a href="../../downloads/releasenotes/release-0.1.0.html">
    <li class="navlevel-2">
      0.1.0
    </li>
  </a>
  


  


      
    
      
    
      
    
      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
