<?xml version="1.0" standalone="no"?>

<svg 
     version="1.1"
     baseProfile="full"
     xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:ev="http://www.w3.org/2001/xml-events"
     xmlns:klighd="http://de.cau.cs.kieler/klighd"
     x="0px"
     y="0px"
     width="140px"
     height="60px"
     viewBox="0 0 140 60"
     >
<title></title>
<desc>Creator: FreeHEP Graphics2D Driver Producer: de.cau.cs.kieler.klighd.piccolo.freehep.SemanticSVGGraphics2D Revision Source:  Date: Friday, August 19, 2022 at 2:48:00 PM Central European Summer Time</desc>
<g stroke-dashoffset="0" stroke-linejoin="miter" stroke-dasharray="none" stroke-width="1" stroke-linecap="butt" stroke-miterlimit="10">
<g fill="#ffffff" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 140 0 L 140 60 L 0 60 L 0 0 z"/>
</g>
<g transform="matrix(1, 0, 0, 1, 0, 3.3333330154418945)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 3, 6.8333330154418945)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n1</text>
</g>
<g transform="matrix(1, 0, 0, 1, 40, 0)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 43, 3.5)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n2</text>
</g>
<g transform="matrix(1, 0, 0, 1, 80, 0)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 83, 3.5)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n3</text>
</g>
<g transform="matrix(1, 0, 0, 1, 40, 40)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 43, 43.5)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n4</text>
</g>
<g transform="matrix(1, 0, 0, 1, 120, 3.3333330154418945)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 123, 6.8333330154418945)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n5</text>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -12)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 32 22 L 52 22"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 32, 7)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 32, 7)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -12)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 72 62 L 122 62 L 122 28.66666603088379 L 132 28.66666603088379"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 112, 13.666666030883789)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 112, 13.666666030883789)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -12)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 72 22 L 92 22"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 72, 7)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 72, 7)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -12)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 32 28.66666603088379 L 42 28.66666603088379 L 42 62 L 52 62"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 32, 47)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 32, 47)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -12)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 112 22 L 132 22"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 112, 7)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 112, 7)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
</g>
</svg>
