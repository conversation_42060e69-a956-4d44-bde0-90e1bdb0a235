<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Documentation on Eclipse Layout Kernel</title>
    <link>https://www.eclipse.org/elk/documentation.html</link>
    <description>Recent content in Documentation on Eclipse Layout Kernel</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>en-us</language><atom:link href="https://www.eclipse.org/elk/documentation/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Advanced Configuration</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html</guid>
      <description>What we have learned so far about how automatic layout in Eclipse works was comparatively straightforward: the diagram layout engine looks for a diagram layout connector to get its hands at a configured ElkGraph, invokes the recursive graph layout engine, and asks the diagram layout connector to apply the results back to the original diagram. As you will have guessed, things can become quite a bit more complex than that.</description>
    </item>
    
    <item>
      <title>Algorithm Debugging</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmdebugging.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmdebugging.html</guid>
      <description>The Eclipse Layout Kernel provides debugging support through its debugging infrastructure, which consists of three layers:
Logging mechanisms that algorithm developers can use to generate log messages as well as log snapshots of graphs as they are laid out. The messages can optionally be written to disk.
Viewers that algorithm developers can use to inspect all kinds of logged objects as well as the execution times of their algorithms.
Preferences that algorithm developers and users can use to engage or disengage the production of logs.</description>
    </item>
    
    <item>
      <title>Algorithm Developers</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers.html</guid>
      <description>While the layout algorithms implemented in ELK already cover a wide range of layout styles, your particular application may have more specific requirements. In these cases, it may become necessary to implement your own layout algorithm, which is what this part of the documentation is all about.
Implementing your own layout algorithm basically consists of the following steps:
Install the Eclipse Layout Kernel SDK into your Eclipse development environment. Create and configure a new Eclipse plug-in project.</description>
    </item>
    
    <item>
      <title>Algorithm Implementation</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation.html</guid>
      <description>Once everything is set up, it is time to actually implement your algorithm. The problem your layout algorithm has to solve can be summarized as follows: given an input graph (possibly with existing coordinates), compute coordinates for all graph elements and routings for all edges (subject to layout properties the graph is annotated with) and annotate the layout graph accordingly. Note that the input graph defines the layout problem, but also carries the resulting coordinate assignment after your algorithm has executed.</description>
    </item>
    
    <item>
      <title>Automatic Builds</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html</guid>
      <description>Since .melk files result in code being generated, you may not want to check that code into your repository. Instead, the code should probably be generated as part of your automatic build. Indeed, the ELK metadata language compiler is available through a Maven repository. You can find the repository URLs in our Downloads section. Note that we provide a separate repository for nightly builds and for each release.
To use the compiler, add the following to your pom.</description>
    </item>
    
    <item>
      <title>Building ELK</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/buildingelk.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/buildingelk.html</guid>
      <description>ELK is built using Apache Maven in conjunction with Tycho to tell Maven how to build Eclipse projects. There are two parts that can be built: the Eclipse Layout Kernel itself, and the metadata compiler used by the main ELK build. The remainder of this page assumes that you have opened a shell in the build/ directory inside your clone of the ELK repository.
Building ELK Execute Maven using the following command line (note that the command line is split in order to improve readability):</description>
    </item>
    
    <item>
      <title>Connecting to ELK</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html</guid>
      <description>In our our basic introduction to automatic layout in Eclipse, we have seen how the diagram layout engine needs someone to extract a proper ElkGraph from whatever layout is invoked on. This is what you, as a tool developer, have to supply. While there are already implementations for different graph editing frameworks to build upon, this page describes how doing so from scratch works.
To connect to ELK, there are two things you will have to do:</description>
    </item>
    
    <item>
      <title>Coordinate System</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/coordinatesystem.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/coordinatesystem.html</guid>
      <description>When talking about the layout of a graph, we have to agree on how coordinates of graph elements are to be stored and interpreted. For the ELK graph, this is how it works:
The coordinates of most elements are relative to their parent element. There are a few exceptions:
Edge labels are relative to the coordinate system their edge is relative to. Source points, bend points, and target points of edges are relative to the edge&amp;rsquo;s containing node.</description>
    </item>
    
    <item>
      <title>Creating a New Project</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/creatinganewproject.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/creatinganewproject.html</guid>
      <description>Layout algorithms are developed as Eclipse Plug-in Projects. Let&amp;rsquo;s work through creating a new project using our wizard and what that project consists of.
Creating a New Project Follow these steps to create a new plug-in:
From the File menu, select New - Project&amp;hellip;.
From the Plug-in Development category, select Plug-in Project and click Next.
Configure your project&amp;rsquo;s basic settings, in particular its name, and click Next.
Configure the project&amp;rsquo;s properties as you see fit and click Next.</description>
    </item>
    
    <item>
      <title>Dependency Injection</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html</guid>
      <description>As you have learned, the basic connection between a diagram editor and the Eclipse Layout Kernel is established through an ILayoutSetup implementation registered with an extension point. What an ILayoutSetup basically does is to provide a Google Guice dependency injector that is then used to instantiate all the classes that play a part in making layout happen in the Eclipse layer.
If you have not used dependency injection yet: ELK will ask the injector to retrieve an implementation of a particular type, and the injector knows which implementation to retrieve.</description>
    </item>
    
    <item>
      <title>Development Setup</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentsetup.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentsetup.html</guid>
      <description>Before you can start development, follow this list of steps. Not doing so may result in us having to reject your contributions, which would make everyone sad.
Register with GitHub
Chances are that you already have a GitHub account. If you don&amp;rsquo;t, go to GitHub.com and click Sign up in the upper right corner.
Register with Eclipse
You need an account at Eclipse.org. Create one by going to that site and clicking Create account in the upper right corner of the site.</description>
    </item>
    
    <item>
      <title>Development Workflow</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow.html</guid>
      <description>Once you have everything set up it is time to start getting your hands at the code. The exact steps depend on how you have set up your development environment and your taste in things (and stuff), but here&amp;rsquo;s what you will generally want to do.
Set Up an Eclipse Installation
This used to be rather hard, but thankfully people have seen that problem and started to do something about it.</description>
    </item>
    
    <item>
      <title>ELK Contributors</title>
      <link>https://www.eclipse.org/elk/documentation/contributors.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors.html</guid>
      <description>You want to contribute to the Eclipse Layout Kernel? Excellent! Here&amp;rsquo;s what you need to do:
Setting Everything Up: Get necessary accounts and prepare everything for us to be able to accept your contributions.
Development Workflow: Understand how development works at the ELK project and how to get your code submitted.</description>
    </item>
    
    <item>
      <title>ELK Metadata Language</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage.html</guid>
      <description>As described in other parts of the documentation, the Eclipse Layout Kernel relies on metadata about all available layout algorithms and the layout options they support. Supplying metadata for your layout algorithm is done by writing an ELK Metadata File in our textual metadata language. The file is used by the ELK SDK to generate the following Java classes:
An ILayoutMetaDataProvider that contains IProperty objects for each layout option you declare, along with a method that registers these options and layout algorithm categories with the LayoutMetaDataService.</description>
    </item>
    
    <item>
      <title>ELK Text Format</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/elktextformat.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/elktextformat.html</guid>
      <description>Documentation is coming soon. For the moment you can find an example on github.</description>
    </item>
    
    <item>
      <title>Getting Eclipse Ready</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/gettingeclipseready.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/gettingeclipseready.html</guid>
      <description>Developing layout algorithms requires you to have the Eclipse Layout Kernel SDK installed in your Eclipse installation. Follow these steps to install it:
Start Eclipse and select Help - Install New Software&amp;hellip; from the menu. In the Work with: field, enter the address of our release or nightly update site, which you can find in our Downloads section. From the Eclipse Layout Kernel category, check Eclipse Layout Kernel (Incubation) - SDK as well as Eclipse Layout Kernel (Incubation) - SDK (sources) and click Next.</description>
    </item>
    
    <item>
      <title>Graph Data Structure</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure.html</guid>
      <description>To represent graphs that should be laid out, the Eclipse Layout Kernel provides a robust EMF-based data structure. This page is about describing that graph data structure.
Terminology Graphs This is a simple example graph along with the terminology that goes with it.
Inclusion Trees Inclusion trees capture the hierarchical structure of a graph. See below for the inclusion tree of the graph we just saw, along with the terminology that goes with it.</description>
    </item>
    
    <item>
      <title>Installing With Oomph</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow/installingwithoomph.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow/installingwithoomph.html</guid>
      <description>Setting up an Eclipse to work on the Eclipse Layout Kernel is comparatively easy when using the Oomph Installer. Follow this step-by-step guide for great glory.
Start the installer. This is what it will look like, more or less:
Click on the Hamburger menu (the three stripes at the top right). A menu with additional options will magically appear:
Click the Advanced Mode button (we&amp;rsquo;re programmers, we don&amp;rsquo;t want to deal with the simple stuff).</description>
    </item>
    
    <item>
      <title>JSON Format</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/jsonformat.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/jsonformat.html</guid>
      <description>The JSON graph format has five basic elements: nodes, ports, labels, edges, and edge sections. Details about each element can be found below, with some sections describing features common to multiple elements. Note that in the JSON code, mandatory fields are marked with an asterisk.
Nodes, Ports, Labels, Edges, and Edge Sections All elements, except labels, must have an id that uniquely identifies them. Labels are usually not referred to from other parts of the graph, which is why the id is optional.</description>
    </item>
    
    <item>
      <title>Layout Options</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/layoutoptions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/layoutoptions.html</guid>
      <description>Layout options are key-value pairs that configure the behavior of automatic layout algorithms. Let&amp;rsquo;s look at all of the involved classes first before we concentrate on how to actually use them in practice.
Involved Classes Layout options are represented by annotating objects with properties. There are two main types we need to cover: the properties themselves, and the objects we can configure through properties.
Properties The most important type when it comes to properties is the IProperty interface.</description>
    </item>
    
    <item>
      <title>Layout View Support</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html</guid>
      <description>If UI components are installed, ELK contributes the Layout View to the workbench:
The layout view is a kind of properties view specialized for setting layout properties. It listens to selection changes and, if it recognizes that something is an element of a graph ELK could layout, allows users to customize the layout options used to do so.
It is a fair question whether users should be given access to this view or not.</description>
    </item>
    
    <item>
      <title>Random Graph Generation</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/randomgraphs.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/randomgraphs.html</guid>
      <description>If you need to quickly create random graphs in .elkt or .elkg format, you can use our random graph creation DSL. This way you can save, store, comment and diff your random graph configurations easily!
To get started, create a file with the file ending .elkr. Now Eclipse will offer the usual features you know and love, such as code completion, syntax highlighting and validation. So remember Eclipse rule number 1: Ctrl+Space is your friend!</description>
    </item>
    
    <item>
      <title>Spacing Options</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html</guid>
      <description>Space-able elements As far as the ELK core is concerned, the following elements can have spacing:
Nodes Ports Labels Edges Comments Connected components The first three can be thought of as boxes with some space to be left around them. Edges are not boxes, and connected components may be more complex than simple boxes.
ELK Layered adds the concept of layers, which leads to a few additional spacing values as described later.</description>
    </item>
    
    <item>
      <title>Structuring Algorithms</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html</guid>
      <description>Often enough, algorithms can be divided into a number of layout phases which, executed as a pipeline, compute a layout. If your algorithm falls into this category, read on; ELK provides things you might want to use.
Layout Phases and Layout Processors Structuring the algorithm into phases has several advantages. First, it effectively divides the big layout problem into simpler sub problems. Second, it allows the algorithm to be more flexible.</description>
    </item>
    
    <item>
      <title>Tool Developers</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers.html</guid>
      <description>This section of the documentation is meant for tool developers who simply want to add automatic layout capabilities to their tool. Despite of its name, the Eclipse Layout Kernel is not limited to Eclipse-based applications, but can also be used in pure Java applications and even in JavaScript applications (although this is not yet available in ELK itself).
Three Layers of Layout Goodness At its basic, ELK is structured in three layers, each adding more convenience functionality to lower layers:</description>
    </item>
    
    <item>
      <title>Unit Tests</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/unittesting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/unittesting.html</guid>
      <description>Layout algorithms are complex pieces of software and, thus, should probably be tested. Besides the usual plain JUnit tests, ELK provides a graph algorithm unit test framework based on JUnit 4. Tests written with that framework basically do three things:
Load one or more graphs.
Optionally provide a number of graph configurations. If a test class doesn&amp;rsquo;t specify configurations, a default configuration will be activated.
Run one ore more tests on each graph.</description>
    </item>
    
    <item>
      <title>Using Algorithms Directly</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingalgorithmsdirectly.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingalgorithmsdirectly.html</guid>
      <description>All layout algorithms implemented in the Eclipse Layout Kernel can be called directly. To do so, you must have an instance of our ElkGraph data structure that can be fed to the layout algorithm, which will then compute a layout for the graph&amp;rsquo;s elements.
Warning: This is very low-level stuff. If you want to stay in the pure-Java domain (as opposed to building an Eclipse-based application), you will probably want to use the next layer of abstraction.</description>
    </item>
    
    <item>
      <title>Using Eclipse Layout</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout.html</guid>
      <description>Compared to the two more basic variants, using the Eclipse Layout Kernel within Eclipse introduces the largest amount of general magic to the process. This page explains the basics of how the layout kernel performs automatic layouts. Based on these information, sub-pages then describe how to integrate your tool with the layout kernel by filling in the details the overview decided to best leave out.
How Layout Works in Eclipse Layout in Eclipse revolves around the DiagramLayoutEngine, which in turn builds upon the IGraphLayoutEngine interface that layout in pure Java revolves around.</description>
    </item>
    
    <item>
      <title>Using Plain Java Layout</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingplainjavalayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingplainjavalayout.html</guid>
      <description>While layout algorithms can be called directly, it is usually a better idea not to do so except in the simplest of cases. There are several reasons for that:
When calling a layout algorithm directly, your code is hardwired to that implementation and looses a bit of flexibility.
Most importantly, as graphs get more complex there are more and more details to be aware of when executing layout. This is especially true for compound graphs: graphs with nodes that contain further graphs themselves.</description>
    </item>
    
  </channel>
</rss>
