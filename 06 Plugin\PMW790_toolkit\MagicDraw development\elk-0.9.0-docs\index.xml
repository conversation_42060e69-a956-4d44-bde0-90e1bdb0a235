<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Eclipse Layout Kernel</title>
    <link>https://www.eclipse.org/elk/</link>
    <description>Recent content on Eclipse Layout Kernel</description>
    <generator>Hugo -- gohugo.io</generator>
    <language>en-us</language><atom:link href="https://www.eclipse.org/elk/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>0.1.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.1.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.1.0.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details The initial release under the new Eclipse umbrella.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.1.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.1.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.1.1.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details A bugfix release.
Fixed a problem where edges with labels end up being routed in an ambiguous way. (#96). View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.2.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.0.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) DEtails A major overhaul of ELK&amp;rsquo;s infrastructure. This is an API breaking release. The following are probably the biggest issues we tackled:
Refactored the central graph data structure. This is a biggie: we made the whole graph data structure easier to understand and easier to use, but of course broke compatibility with old diagram layout connectors in the process.</description>
    </item>
    
    <item>
      <title>0.2.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.1.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details A bug fix release that fixes bugs introduced during the overhaul of ELK&amp;rsquo;s infrastructure. This release should not be API-breaking.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.2.2</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.2.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.2.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details A bug fix release that fixes bugs introduced during the overhaul of ELK&amp;rsquo;s infrastructure. This release should not be API-breaking.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.2.3</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.3.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.2.3.html</guid>
      <description>Release log Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details A bug fix release that fixes bugs introduced during the overhaul of ELK&amp;rsquo;s infrastructure. This release should not be API-breaking.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.3.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.3.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.3.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven Repository (for meta data language compiler) Details After the major overhaul of the project&amp;rsquo;s main data structure for the previous release, this release concentrates on fixing bugs and adding major new features. This includes a complete rewrite of the code responsible for placing ports and labels and calculating the size of nodes, the addition of new layout algorithms, and various improvements to the flagship layout algorithm, ELK Layered.</description>
    </item>
    
    <item>
      <title>0.4.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.4.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.4.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Meta data language compiler (for building layout algorithms with Maven) Details This release concentrates on bug fixes and enhancements concerning many details of the layout algorithms, but also introduces compaction and overlap removal algorithms.
Some changes may cause clients to break and may change layout results.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.4.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.4.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.4.1.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Meta data language compiler (for building layout algorithms with Maven) Details A bug fix release that fixes two small bugs. This release should not be API-breaking.
View the release at Eclipse for links to the list of closed issues.</description>
    </item>
    
    <item>
      <title>0.5.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.5.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.5.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Meta data language compiler (for building layout algorithms with Maven) Details Besides the usual bunch of bug fixes, this release adds a number of neat enhancements and feature requests, among them the following:
ELK Layered&amp;rsquo;s support for self loops has been improved a lot. A new box layout algorithm improves how nodes can be packed.</description>
    </item>
    
    <item>
      <title>0.6.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.6.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.6.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Meta data language compiler (for building layout algorithms with Maven) Details Besides the usual bunch of bug fixes, this release adds a number of neat enhancements and feature requests, among them the following:
A complete re-implementation of how ELK Layered routes self loops which should fix all of the problems we saw in release 0.</description>
    </item>
    
    <item>
      <title>0.6.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.6.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.6.1.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details Besides the usual bunch of bug fixes, this release is unusual for a minor release in that we decided to include two major features as well:
The rectangle packing algorithm, which is finally included, got a major rewrite. Port label placement now supports next-to-port labels for outside port labels as well, with some configurability.</description>
    </item>
    
    <item>
      <title>0.7.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.7.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.7.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details This is a major release which comes with quite a number of changes. Some of those are breaking changes, either in the usual API-breaking sense or in the sense that default layouts might look different. Those issues and pull requests are now labeled with &amp;ldquo;breaking&amp;rdquo; to make such changes easier to spot.</description>
    </item>
    
    <item>
      <title>0.7.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.7.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.7.1.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details This is mainly a bugfix release. See GitHub for the full list of resolved issues.
New Features and Enhancements #713: The Stress and Force layout algorithms now properly place inline edge labels. Bugfixes #701, #732: Space is now properly reserved around port labels with fixed position. #682, #683: With non-standard layout directions (other than left-to-right), node label paddings were not applied correctly.</description>
    </item>
    
    <item>
      <title>0.8.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.8.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.8.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details This is mainly a bugfix release. See GitHub for the full list of resolved issues.
New Features and Enhancements #672, #674, #675, #677: Build systems is simplified. #690, #691: Improved documentation of position and layer choice constrains. #695, #698: Support node micro layout with further layout algorithms. #688, #711: Better documentation for content alignment.</description>
    </item>
    
    <item>
      <title>0.8.1</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.8.1.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.8.1.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details This is mainly a bugfix release. See GitHub for the full list of resolved issues.
New Features and Enhancements #827: Added Greedy Model Order cycle breaker. #842: Added a port label placement option to place the label always on the other same side. Bugfixes #828: Node order violations are only counted for real nodes.</description>
    </item>
    
    <item>
      <title>0.9.0</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes/release-0.9.0.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes/release-0.9.0.html</guid>
      <description>Release log Documentation Update site Zipped update site (for offline use) Maven central (for building pure Java projects that use ELK) Details This is mainly a bugfix release. See GitHub for the full list of resolved issues.
New Features and Enhancements #962, #914: Added an experimental Depth-First and Breadth-First model order layerer. #867, #902: Added a model order layering by node promotion. #956, #942, #927, #926, #921, #893, #892, #886: Added libavoid for standalone edge routing.</description>
    </item>
    
    <item>
      <title>2022</title>
      <link>https://www.eclipse.org/elk/blog/2022.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/2022.html</guid>
      <description></description>
    </item>
    
    <item>
      <title>2023</title>
      <link>https://www.eclipse.org/elk/blog/2023.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/2023.html</guid>
      <description></description>
    </item>
    
    <item>
      <title>Activate Inside Self Loops</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-insideSelfLoops-activate.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-insideSelfLoops-activate.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.insideSelfLoops.activate Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: nodes Containing Group: insideSelfLoops Description Whether this node allows to route self loops inside of it instead of around it. If set to true, this will make the node a compound node if it isn&amp;rsquo;t already, and will require the layout algorithm to support compound nodes with hierarchical ports.</description>
    </item>
    
    <item>
      <title>Adapt Port Positions</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-adaptPortPositions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-adaptPortPositions.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.graphviz.adaptPortPositions Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.graphviz) Applies To: parents Description Whether ports should be moved to the point where edges cross the node&amp;rsquo;s bounds.</description>
    </item>
    
    <item>
      <title>Add Unnecessary Bendpoints</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-unnecessaryBendpoints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-unnecessaryBendpoints.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.unnecessaryBendpoints Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Description Adds bend points even if an edge does not change direction. If true, each long edge dummy will contribute a bend point to its edges and hierarchy-crossing edges will always get a bend point where they cross hierarchy boundaries. By default, bend points are only added where an edge changes direction.</description>
    </item>
    
    <item>
      <title>Additional Port Space</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-portsSurrounding.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-portsSurrounding.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.spacing.portsSurrounding Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.ElkMargin Default Value: new ElkMargin(0) (as defined in org.eclipse.elk) Applies To: parents Containing Group: spacing Description Additional space around the sets of ports on each node side. For each side of a node, this option can reserve additional space before and after the ports on each side. For example, a top spacing of 20 makes sure that the first port on the western and eastern side is 20 units away from the northern border.</description>
    </item>
    
    <item>
      <title>Additional Wedge Space</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-computeAdditionalWedgeSpace.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-computeAdditionalWedgeSpace.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.radial.rotation.computeAdditionalWedgeSpace Meta Data Provider: options.RadialMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.radial) Applies To: parents Dependencies: org.eclipse.elk.radial.rotate Containing Group: rotation Description If set to true, modifies the target angle by rotating further such that space is left for an edge to pass in between the nodes. This option should only be used in conjunction with top-down layout.</description>
    </item>
    
    <item>
      <title>Additional Wrapped Edges Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-additionalEdgeSpacing.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-additionalEdgeSpacing.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.additionalEdgeSpacing Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 10 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.SINGLE_EDGE), org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping Description To visually separate edges that are wrapped from regularly routed edges an additional spacing value can be specified in form of this layout option. The spacing is added to the regular edgeNode spacing.</description>
    </item>
    
    <item>
      <title>Advanced Configuration</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html</guid>
      <description>What we have learned so far about how automatic layout in Eclipse works was comparatively straightforward: the diagram layout engine looks for a diagram layout connector to get its hands at a configured ElkGraph, invokes the recursive graph layout engine, and asks the diagram layout connector to apply the results back to the original diagram. As you will have guessed, things can become quite a bit more complex than that.</description>
    </item>
    
    <item>
      <title>Algorithm Debugging</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmdebugging.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmdebugging.html</guid>
      <description>The Eclipse Layout Kernel provides debugging support through its debugging infrastructure, which consists of three layers:
Logging mechanisms that algorithm developers can use to generate log messages as well as log snapshots of graphs as they are laid out. The messages can optionally be written to disk.
Viewers that algorithm developers can use to inspect all kinds of logged objects as well as the execution times of their algorithms.
Preferences that algorithm developers and users can use to engage or disengage the production of logs.</description>
    </item>
    
    <item>
      <title>Algorithm Developers</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers.html</guid>
      <description>While the layout algorithms implemented in ELK already cover a wide range of layout styles, your particular application may have more specific requirements. In these cases, it may become necessary to implement your own layout algorithm, which is what this part of the documentation is all about.
Implementing your own layout algorithm basically consists of the following steps:
Install the Eclipse Layout Kernel SDK into your Eclipse development environment. Create and configure a new Eclipse plug-in project.</description>
    </item>
    
    <item>
      <title>Algorithm Implementation</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation.html</guid>
      <description>Once everything is set up, it is time to actually implement your algorithm. The problem your layout algorithm has to solve can be summarized as follows: given an input graph (possibly with existing coordinates), compute coordinates for all graph elements and routings for all edges (subject to layout properties the graph is annotated with) and annotate the layout graph accordingly. Note that the input graph defines the layout problem, but also carries the resulting coordinate assignment after your algorithm has executed.</description>
    </item>
    
    <item>
      <title>Algorithms</title>
      <link>https://www.eclipse.org/elk/reference/algorithms.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms.html</guid>
      <description>The following layout algorithms are available in ELK:</description>
    </item>
    
    <item>
      <title>Alignment</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alignment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alignment.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alignment Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.Alignment (Enum) Possible Values: AUTOMATIC
LEFT
RIGHT
TOP
BOTTOM
CENTER Default Value: Alignment.AUTOMATIC (as defined in org.eclipse.elk) Applies To: nodes Description Alignment of the selected node relative to other nodes; the exact meaning depends on the used algorithm.</description>
    </item>
    
    <item>
      <title>Allow Non-Flow Ports To Switch Sides</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-allowNonFlowPortsToSwitchSides.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-allowNonFlowPortsToSwitchSides.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.allowNonFlowPortsToSwitchSides Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: ports Legacy Id: org.eclipse.elk.layered.northOrSouthPort Description Specifies whether non-flow ports may switch sides if their node&amp;rsquo;s port constraints are either FIXED_SIDE or FIXED_ORDER. A non-flow port is a port on a side that is not part of the currently configured layout flow. For instance, given a left-to-right layout direction, north and south ports would be considered non-flow ports.</description>
    </item>
    
    <item>
      <title>Angle Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-anglePenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-anglePenalty.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.anglePenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied in its full amount to tight acute bends in the connector path. A smaller portion of the penalty is applied for slight bends, i.e., where the bend is close to 180 degrees. This is useful for polyline routing where there is some evidence that tighter corners are worse for readability, but that slight bends might not be so bad, especially when smoothed by curves.</description>
    </item>
    
    <item>
      <title>Animate</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-animate.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-animate.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.animate Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: true (as defined in org.eclipse.elk) Applies To: parents Description Whether the shift from the old layout to the new computed layout shall be animated.</description>
    </item>
    
    <item>
      <title>Animation Time Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-animTimeFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-animTimeFactor.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.animTimeFactor Meta Data Provider: core.options.CoreOptions Value Type: int Default Value: 100 (as defined in org.eclipse.elk) Lower Bound: 0 Applies To: parents Description Factor for computation of animation time. The higher the value, the longer the animation time. If the value is 0, the resulting time is always equal to the minimum defined by &amp;lsquo;Minimal Animation Time&amp;rsquo;.</description>
    </item>
    
    <item>
      <title>Annulus Wedge Criteria</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-wedgeCriteria.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-wedgeCriteria.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.wedgeCriteria Meta Data Provider: options.RadialMetaDataProvider Value Type: org.eclipse.elk.alg.radial.options.AnnulusWedgeCriteria (Enum) Possible Values: LEAF_NUMBER
NODE_SIZE Default Value: AnnulusWedgeCriteria.NODE_SIZE (as defined in org.eclipse.elk.radial) Applies To: parents Description Determine how the wedge for the node placement is calculated. It can be chosen between wedge determination by the number of leaves or by the maximum sum of diagonals.</description>
    </item>
    
    <item>
      <title>Aspect Ratio</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-aspectRatio.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-aspectRatio.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.aspectRatio Meta Data Provider: core.options.CoreOptions Value Type: double Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Description The desired aspect ratio of the drawing, that is the quotient of width by height.</description>
    </item>
    
    <item>
      <title>Automatic Builds</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html</guid>
      <description>Since .melk files result in code being generated, you may not want to check that code into your repository. Instead, the code should probably be generated as part of your automatic build. Indeed, the ELK metadata language compiler is available through a Maven repository. You can find the repository URLs in our Downloads section. Note that we provide a separate repository for nightly builds and for each release.
To use the compiler, add the following to your pom.</description>
    </item>
    
    <item>
      <title>Bend Points</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-bendPoints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-bendPoints.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.bendPoints Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.KVectorChain Applies To: edges Description A fixed list of bend points for the edge. This is used by the &amp;lsquo;Fixed Layout&amp;rsquo; algorithm to specify a pre-defined routing for an edge. The vector chain must include the source point, any bend points, and the target point, so it must have at least two points.</description>
    </item>
    
    <item>
      <title>BK Edge Straightening</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-bk-edgeStraightening.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-bk-edgeStraightening.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.nodePlacement.bk.edgeStraightening Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.EdgeStraighteningStrategy (Enum) Possible Values: NONE
IMPROVE_STRAIGHTNESS Default Value: EdgeStraighteningStrategy.IMPROVE_STRAIGHTNESS (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.BRANDES_KOEPF) Containing Group: nodePlacement -&amp;gt; bk Description Specifies whether the Brandes Koepf node placer tries to increase the number of straight edges at the expense of diagram size. There is a subtle difference to the &amp;lsquo;favorStraightEdges&amp;rsquo; option, which decides whether a balanced placement of the nodes is desired, or not.</description>
    </item>
    
    <item>
      <title>BK Fixed Alignment</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-bk-fixedAlignment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-bk-fixedAlignment.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.nodePlacement.bk.fixedAlignment Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.FixedAlignment (Enum) Possible Values: NONE
LEFTUP
RIGHTUP
LEFTDOWN
RIGHTDOWN
BALANCED Default Value: FixedAlignment.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.BRANDES_KOEPF) Containing Group: nodePlacement -&amp;gt; bk Description Tells the BK node placer to use a certain alignment (out of its four) instead of the one producing the smallest height, or the combination of all four.</description>
    </item>
    
    <item>
      <title>box</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-box.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-box.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.box Options Box Layout Mode </description>
    </item>
    
    <item>
      <title>Box Layout Mode</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-box-packingMode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-box-packingMode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.box.packingMode Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.util.BoxLayoutProvider$PackingMode (Enum) Possible Values: SIMPLE
GROUP_DEC
GROUP_MIXED
GROUP_INC Default Value: BoxLayoutProvider.PackingMode.SIMPLE (as defined in org.eclipse.elk) Applies To: parents Containing Group: box Description Configures the packing mode used by the {@link BoxLayoutProvider}. If SIMPLE is not required (neither priorities are used nor the interactive mode), GROUP_DEC can improve the packing and decrease the area. GROUP_MIXED and GROUP_INC may, in very specific scenarios, work better.</description>
    </item>
    
    <item>
      <title>Building ELK</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/buildingelk.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/buildingelk.html</guid>
      <description>ELK is built using Apache Maven in conjunction with Tycho to tell Maven how to build Eclipse projects. There are two parts that can be built: the Eclipse Layout Kernel itself, and the metadata compiler used by the main ELK build. The remainder of this page assumes that you have opened a shell in the build/ directory inside your clone of the ELK repository.
Building ELK Execute Maven using the following command line (note that the command line is split in order to improve readability):</description>
    </item>
    
    <item>
      <title>Center On Root</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-centerOnRoot.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-centerOnRoot.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.centerOnRoot Meta Data Provider: options.RadialMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.radial) Applies To: parents Description Centers the layout on the root of the tree i.e. so that the central node is also the center node of the final layout. This introduces additional whitespace.</description>
    </item>
    
    <item>
      <title>Child Area Height</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-childAreaHeight.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-childAreaHeight.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.childAreaHeight Meta Data Provider: core.options.CoreOptions Value Type: double Applies To: parents Description The height of the area occupied by the laid out children of a node.</description>
    </item>
    
    <item>
      <title>Child Area Width</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-childAreaWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-childAreaWidth.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.childAreaWidth Meta Data Provider: core.options.CoreOptions Value Type: double Applies To: parents Description The width of the area occupied by the laid out children of a node.</description>
    </item>
    
    <item>
      <title>Cluster Crossing Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-clusterCrossingPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-clusterCrossingPenalty.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.clusterCrossingPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied whenever a connector path crosses a cluster boundary.</description>
    </item>
    
    <item>
      <title>Comment Box</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-commentBox.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-commentBox.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.commentBox Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: nodes Description Whether the node should be regarded as a comment box instead of a regular node. In that case its placement should be similar to how labels are handled. Any edges incident to a comment box specify to which graph elements the comment is related.</description>
    </item>
    
    <item>
      <title>Comment Comment Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-commentComment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-commentComment.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.commentComment Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between a comment box and other comment boxes connected to the same node. The space left between comment boxes of different nodes is controlled by the node-node spacing.</description>
    </item>
    
    <item>
      <title>Comment Node Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-commentNode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-commentNode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.commentNode Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between a node and its connected comment boxes. The space left between a node and the comments of another node is controlled by the node-node spacing.</description>
    </item>
    
    <item>
      <title>compaction</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-compaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-compaction.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.compaction Options Compaction Strategy Orthogonal Compaction </description>
    </item>
    
    <item>
      <title>compaction</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-compaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-compaction.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.compaction Options Connected Components Compaction Subgroups postCompaction </description>
    </item>
    
    <item>
      <title>Compaction</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-compactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-compactor.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.compactor Meta Data Provider: options.RadialMetaDataProvider Value Type: org.eclipse.elk.alg.radial.options.CompactionStrategy (Enum) Possible Values: NONE
RADIAL_COMPACTION
WEDGE_COMPACTION Default Value: CompactionStrategy.NONE (as defined in org.eclipse.elk.radial) Applies To: parents Description With the compacter option it can be determined how compaction on the graph is done. It can be chosen between none, the radial compaction or the compaction of wedges separately.</description>
    </item>
    
    <item>
      <title>Compaction iterations</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-compaction-iterations.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-compaction-iterations.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.packing.compaction.iterations Meta Data Provider: options.RectPackingMetaDataProvider Value Type: int Default Value: 1 (as defined in org.eclipse.elk.rectpacking) Lower Bound: 1 Applies To: parents Containing Group: packing -&amp;gt; compaction Description Defines the number of compaction iterations. E.g. if set to 2 the width is initially approximated, then the drawing is compacted and based on the resulting drawing the target width is decreased or increased and a second compaction step is executed and the result compared to the first one.</description>
    </item>
    
    <item>
      <title>Compaction Step Size</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-compactionStepSize.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-compactionStepSize.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.compactionStepSize Meta Data Provider: options.RadialMetaDataProvider Value Type: int Default Value: 1 (as defined in org.eclipse.elk.radial) Lower Bound: 0 Applies To: parents Dependencies: org.eclipse.elk.radial.compactor Description Determine the size of steps with which the compaction is done. Step size 1 correlates to a compaction of 1 pixel per Iteration.</description>
    </item>
    
    <item>
      <title>Compaction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-compaction-compactionStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-compaction-compactionStrategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.compaction.compactionStrategy Meta Data Provider: options.SporeMetaDataProvider Value Type: org.eclipse.elk.alg.spore.options.CompactionStrategy (Enum) Possible Values: DEPTH_FIRST Default Value: CompactionStrategy.DEPTH_FIRST (as defined in org.eclipse.elk) Applies To: parents Containing Group: compaction Description This option defines how the compaction is applied.</description>
    </item>
    
    <item>
      <title>Compaction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.packing.strategy Meta Data Provider: options.RectPackingMetaDataProvider Value Type: org.eclipse.elk.alg.rectpacking.p2packing.PackingStrategy (Enum) Possible Values: COMPACTION
SIMPLE
NONE Default Value: PackingStrategy.COMPACTION (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: packing Description Strategy for finding an initial placement on nodes.</description>
    </item>
    
    <item>
      <title>compaction.postCompaction</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-compaction-postCompaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-compaction-postCompaction.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.compaction.postCompaction Options Post Compaction Strategy Post Compaction Constraint Calculation </description>
    </item>
    
    <item>
      <title>componentCompaction</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-disco-componentCompaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-disco-componentCompaction.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.disco.componentCompaction Options Connected Components Compaction Strategy Connected Components Layout Algorithm </description>
    </item>
    
    <item>
      <title>Components Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-componentComponent.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-componentComponent.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.componentComponent Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 20f (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between pairs of connected components. This option is only relevant if &amp;lsquo;separateConnectedComponents&amp;rsquo; is activated.</description>
    </item>
    
    <item>
      <title>Concentrate Edges</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-concentrate.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-concentrate.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.graphviz.concentrate Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.graphviz) Applies To: parents Description Merges multiedges into a single edge and causes partially parallel edges to share part of their paths.</description>
    </item>
    
    <item>
      <title>Connected Components Compaction</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-connectedComponents.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-connectedComponents.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.compaction.connectedComponents Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.separateConnectedComponents (true) Containing Group: compaction Description Tries to further compact components (disconnected sub-graphs).</description>
    </item>
    
    <item>
      <title>Connected Components Compaction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-componentCompaction-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-componentCompaction-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.disco.componentCompaction.strategy Meta Data Provider: options.DisCoMetaDataProvider Value Type: org.eclipse.elk.alg.disco.options.CompactionStrategy (Enum) Possible Values: POLYOMINO Default Value: CompactionStrategy.POLYOMINO (as defined in org.eclipse.elk.disco) Applies To: parents Containing Group: componentCompaction Description Strategy for packing different connected components in order to save space and enhance readability of a graph.</description>
    </item>
    
    <item>
      <title>Connected Components Layout Algorithm</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-componentCompaction-componentLayoutAlgorithm.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-componentCompaction-componentLayoutAlgorithm.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.disco.componentCompaction.componentLayoutAlgorithm Meta Data Provider: options.DisCoMetaDataProvider Value Type: java.lang.String Applies To: parents Containing Group: componentCompaction Description A layout algorithm that is to be applied to each connected component before the components themselves are compacted. If unspecified, the positions of the components&amp;rsquo; nodes are not altered.</description>
    </item>
    
    <item>
      <title>Connecting to ELK</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html</guid>
      <description>In our our basic introduction to automatic layout in Eclipse, we have seen how the diagram layout engine needs someone to extract a proper ElkGraph from whatever layout is invoked on. This is what you, as a tool developer, have to supply. While there are already implementations for different graph editing frameworks to build upon, this page describes how doing so from scratch works.
To connect to ELK, there are two things you will have to do:</description>
    </item>
    
    <item>
      <title>Consider Model Order</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.OrderingStrategy (Enum) Possible Values: NONE
NODES_AND_EDGES
PREFER_EDGES
PREFER_NODES Default Value: OrderingStrategy.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: considerModelOrder Description Preserves the order of nodes and edges in the model file if this does not lead to additional edge crossings. Depending on the strategy this is not always possible since the node and edge order might be conflicting.</description>
    </item>
    
    <item>
      <title>Consider Model Order for Components</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-components.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-components.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.components Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.components.ComponentOrderingStrategy (Enum) Possible Values: NONE
INSIDE_PORT_SIDE_GROUPS
GROUP_MODEL_ORDER
MODEL_ORDER Default Value: ComponentOrderingStrategy.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.separateConnectedComponents Containing Group: considerModelOrder Description If set to NONE the usual ordering strategy (by cumulative node priority and size of nodes) is used. INSIDE_PORT_SIDES orders the components with external ports only inside the groups with the same port side. FORCE_MODEL_ORDER enforces the mode order on components.</description>
    </item>
    
    <item>
      <title>Consider Port Order</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-portModelOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-portModelOrder.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.portModelOrder Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: considerModelOrder Description If disabled the port order of output ports is derived from the edge order and input ports are ordered by their incoming connections. If enabled all ports are ordered by the port model order.</description>
    </item>
    
    <item>
      <title>considerModelOrder</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-considerModelOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-considerModelOrder.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.considerModelOrder Options Consider Model Order Consider Port Order No Model Order Consider Model Order for Components Long Edge Ordering Strategy Crossing Counter Node Order Influence Crossing Counter Port Order Influence </description>
    </item>
    
    <item>
      <title>Content Alignment</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-contentAlignment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-contentAlignment.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.contentAlignment Meta Data Provider: core.options.CoreOptions Value Type: java.util.EnumSet&amp;lt;org.eclipse.elk.core.options.ContentAlignment&amp;gt; Possible Values: V_TOP
V_CENTER
V_BOTTOM
H_LEFT
H_CENTER
H_RIGHT Default Value: ContentAlignment.topLeft() (as defined in org.eclipse.elk) Applies To: parents Description Specifies how the content of a node are aligned. Each node can individually control the alignment of its contents. I.e. if a node should be aligned top left in its parent node, the parent node should specify that option.</description>
    </item>
    
    <item>
      <title>Coordinate System</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/coordinatesystem.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/coordinatesystem.html</guid>
      <description>When talking about the layout of a graph, we have to agree on how coordinates of graph elements are to be stored and interpreted. For the ELK graph, this is how it works:
The coordinates of most elements are relative to their parent element. There are a few exceptions:
Edge labels are relative to the coordinate system their edge is relative to. Source points, bend points, and target points of edges are relative to the edge&amp;rsquo;s containing node.</description>
    </item>
    
    <item>
      <title>Correction Factor for Wrapping</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-correctionFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-correctionFactor.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.correctionFactor Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 1.0 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.SINGLE_EDGE), org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping Description At times and for certain types of graphs the executed wrapping may produce results that are consistently biased in the same fashion: either wrapping to often or to rarely. This factor can be used to correct the bias.</description>
    </item>
    
    <item>
      <title>Cost Function for Spanning Tree</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-spanningTreeCostFunction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-spanningTreeCostFunction.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.processingOrder.spanningTreeCostFunction Meta Data Provider: options.SporeMetaDataProvider Value Type: org.eclipse.elk.alg.spore.options.SpanningTreeCostFunction (Enum) Possible Values: CENTER_DISTANCE
CIRCLE_UNDERLAP
RECTANGLE_UNDERLAP
INVERTED_OVERLAP
MINIMUM_ROOT_DISTANCE Default Value: SpanningTreeCostFunction.CIRCLE_UNDERLAP (as defined in org.eclipse.elk) Applies To: parents Containing Group: processingOrder Description The cost function is used in the creation of the spanning tree.</description>
    </item>
    
    <item>
      <title>Creating a New Project</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/creatinganewproject.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/creatinganewproject.html</guid>
      <description>Layout algorithms are developed as Eclipse Plug-in Projects. Let&amp;rsquo;s work through creating a new project using our wizard and what that project consists of.
Creating a New Project Follow these steps to create a new plug-in:
From the File menu, select New - Project&amp;hellip;.
From the Plug-in Development category, select Plug-in Project and click Next.
Configure your project&amp;rsquo;s basic settings, in particular its name, and click Next.
Configure the project&amp;rsquo;s properties as you see fit and click Next.</description>
    </item>
    
    <item>
      <title>Crossing Counter Node Order Influence</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterNodeInfluence.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterNodeInfluence.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.crossingCounterNodeInfluence Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.considerModelOrder.strategy Containing Group: considerModelOrder Description Indicates with what percentage (1 for 100%) violations of the node model order are weighted against the crossings e.g. a value of 0.5 means two model order violations are as important as on edge crossing. This allows some edge crossings in favor of preserving the model order.</description>
    </item>
    
    <item>
      <title>Crossing Counter Port Order Influence</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterPortInfluence.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterPortInfluence.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.crossingCounterPortInfluence Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.considerModelOrder.strategy Containing Group: considerModelOrder Description Indicates with what percentage (1 for 100%) violations of the port model order are weighted against the crossings e.g. a value of 0.5 means two model order violations are as important as on edge crossing. This allows some edge crossings in favor of preserving the model order.</description>
    </item>
    
    <item>
      <title>Crossing Minimization Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.crossingMinimization.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.CrossingMinimizationStrategy (Enum) Possible Values: LAYER_SWEEP
INTERACTIVE (@AdvancedPropertyValue)
NONE Default Value: CrossingMinimizationStrategy.LAYER_SWEEP (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: crossingMinimization Description Strategy for crossing minimization.</description>
    </item>
    
    <item>
      <title>Crossing Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-crossingPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-crossingPenalty.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.crossingPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied whenever a connector path crosses another connector path. It takes shared paths into consideration and the penalty is only applied if there is an actual crossing.</description>
    </item>
    
    <item>
      <title>crossingMinimization</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.crossingMinimization Options Crossing Minimization Strategy Force Node Model Order Hierarchical Sweepiness Semi-Interactive Crossing Minimization In Layer Predecessor of In Layer Successor of Position Choice Constraint Position ID Subgroups greedySwitch greedySwitchHierarchical </description>
    </item>
    
    <item>
      <title>crossingMinimization.greedySwitch</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitch.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitch.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.crossingMinimization.greedySwitch Options Greedy Switch Activation Threshold Greedy Switch Crossing Minimization </description>
    </item>
    
    <item>
      <title>crossingMinimization.greedySwitchHierarchical</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.crossingMinimization.greedySwitchHierarchical Options Greedy Switch Crossing Minimization (hierarchical) </description>
    </item>
    
    <item>
      <title>Current position of a node in the order of nodes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-currentPosition.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-currentPosition.html</guid>
      <description>Property Value Type: output Identifier: org.eclipse.elk.rectpacking.currentPosition Meta Data Provider: options.RectPackingMetaDataProvider Value Type: int Default Value: -1 (as defined in org.eclipse.elk.rectpacking) Lower Bound: -1 Applies To: nodes Description The rectangles are ordered. Normally according to their definition the the model. This option specifies the current position of a node.</description>
    </item>
    
    <item>
      <title>Cutting Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-strategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.cutting.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.CuttingStrategy (Enum) Possible Values: ARD
MSD
MANUAL Default Value: CuttingStrategy.MSD (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.SINGLE_EDGE), org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping -&amp;gt; cutting Description The strategy by which the layer indexes are determined at which the layering crumbles into chunks.</description>
    </item>
    
    <item>
      <title>Cycle Breaking Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-cycleBreaking-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-cycleBreaking-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.cycleBreaking.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.CycleBreakingStrategy (Enum) Possible Values: GREEDY
DEPTH_FIRST
INTERACTIVE (@AdvancedPropertyValue)
MODEL_ORDER
GREEDY_MODEL_ORDER Default Value: CycleBreakingStrategy.GREEDY (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: cycleBreaking Description Strategy for cycle breaking. Cycle breaking looks for cycles in the graph and determines which edges to reverse to break the cycles. Reversed edges will end up pointing to the opposite direction of regular edges (that is, reversed edges will point left if edges usually point right).</description>
    </item>
    
    <item>
      <title>cycleBreaking</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-cycleBreaking.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-cycleBreaking.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.cycleBreaking Options Cycle Breaking Strategy </description>
    </item>
    
    <item>
      <title>DCGraph</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-debug-discoGraph.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-debug-discoGraph.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.disco.debug.discoGraph Meta Data Provider: options.DisCoMetaDataProvider Value Type: java.lang.Object Applies To: parents Containing Group: debug Description Access to the DCGraph is intended for the debug view,</description>
    </item>
    
    <item>
      <title>debug</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-disco-debug.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-disco-debug.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.disco.debug Options DCGraph List of Polyominoes </description>
    </item>
    
    <item>
      <title>Debug Mode</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-debugMode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-debugMode.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.debugMode Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether additional debug information shall be generated.</description>
    </item>
    
    <item>
      <title>Dependency Injection</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html</guid>
      <description>As you have learned, the basic connection between a diagram editor and the Eclipse Layout Kernel is established through an ILayoutSetup implementation registered with an extension point. What an ILayoutSetup basically does is to provide a Google Guice dependency injector that is then used to instantiate all the classes that play a part in making layout happen in the Eclipse layer.
If you have not used dependency injection yet: ELK will ask the injector to retrieve an implementation of a particular type, and the injector knows which implementation to retrieve.</description>
    </item>
    
    <item>
      <title>Desired Edge Length</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-desiredEdgeLength.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-desiredEdgeLength.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress.desiredEdgeLength Meta Data Provider: options.StressMetaDataProvider Value Type: double Default Value: 100.0 (as defined in org.eclipse.elk.stress) Applies To: parents, edges Description Either specified for parent nodes or for individual edges, where the latter takes higher precedence.</description>
    </item>
    
    <item>
      <title>Desired index of node</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-desiredPosition.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-desiredPosition.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.rectpacking.desiredPosition Meta Data Provider: options.RectPackingMetaDataProvider Value Type: int Default Value: -1 (as defined in org.eclipse.elk.rectpacking) Lower Bound: -1 Applies To: nodes Description The rectangles are ordered. Normally according to their definition the the model. This option allows to specify a desired position that has preference over the original position.</description>
    </item>
    
    <item>
      <title>Development Setup</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentsetup.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentsetup.html</guid>
      <description>Before you can start development, follow this list of steps. Not doing so may result in us having to reject your contributions, which would make everyone sad.
Register with GitHub
Chances are that you already have a GitHub account. If you don&amp;rsquo;t, go to GitHub.com and click Sign up in the upper right corner.
Register with Eclipse
You need an account at Eclipse.org. Create one by going to that site and clicking Create account in the upper right corner of the site.</description>
    </item>
    
    <item>
      <title>Development Workflow</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow.html</guid>
      <description>Once you have everything set up it is time to start getting your hands at the code. The exact steps depend on how you have set up your development environment and your taste in things (and stuff), but here&amp;rsquo;s what you will generally want to do.
Set Up an Eclipse Installation
This used to be rather hard, but thankfully people have seen that problem and started to do something about it.</description>
    </item>
    
    <item>
      <title>Direction</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-direction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-direction.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.direction Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.Direction (Enum) Possible Values: UNDEFINED
RIGHT
LEFT
DOWN
UP Default Value: Direction.UNDEFINED (as defined in org.eclipse.elk) Applies To: parents Description Overall direction of edges: horizontal (right / left) or vertical (down / up).</description>
    </item>
    
    <item>
      <title>Direction Congruency</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-directionCongruency.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-directionCongruency.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.directionCongruency Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.DirectionCongruency (Enum) Possible Values: READING_DIRECTION
ROTATION Default Value: DirectionCongruency.READING_DIRECTION (as defined in org.eclipse.elk.layered) Applies To: parents Description Specifies how drawings of the same graph with different layout directions compare to each other: either a natural reading direction is preserved or the drawings are rotated versions of each other.
Additional Documentation Internally, we map any desired layout direction to the variant of creating a left-to-right drawing.</description>
    </item>
    
    <item>
      <title>Direction Priority</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-direction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-direction.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.priority.direction Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: edges Containing Group: priority Description Defines how important it is to have a certain edge point into the direction of the overall layout. This option is evaluated during the cycle breaking phase.</description>
    </item>
    
    <item>
      <title>Distance Model</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-neatoModel.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-neatoModel.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.graphviz.neatoModel Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: org.eclipse.elk.alg.graphviz.dot.transform.NeatoModel (Enum) Possible Values: SHORTPATH
CIRCUIT
SUBSET Default Value: NeatoModel.SHORTPATH (as defined in org.eclipse.elk.graphviz) Applies To: parents Description Specifies how the distance matrix is computed for the input graph.</description>
    </item>
    
    <item>
      <title>Distance Penalty When Improving Cuts </title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-distancePenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-distancePenalty.html</guid>
      <description> Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.multiEdge.distancePenalty Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 2.0 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE), org.eclipse.elk.layered.wrapping.multiEdge.improveCuts (true) Containing Group: wrapping -&amp;gt; multiEdge </description>
    </item>
    
    <item>
      <title>Draw2D Layout</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-conn-gmf-layouter-Draw2D.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-conn-gmf-layouter-Draw2D.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.conn.gmf.layouter.Draw2D Meta Data Provider: GmfMetaDataProvider Description &amp;lsquo;Directed Graph Layout&amp;rsquo; provided by the Draw2D framework. This is the same algorithm that is used by the standard layout button of GMF diagrams.
Category: Layered The layer-based method was introduced by Sugiyama, Tagawa and Toda in 1981. It emphasizes the direction of edges by pointing as many edges as possible into the same direction. The nodes are arranged in layers, which are sometimes called &amp;ldquo;hierarchies&amp;rdquo;, and then reordered such that the number of edge crossings is minimized.</description>
    </item>
    
    <item>
      <title>Eades Repulsion</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-repulsion.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-repulsion.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force.repulsion Meta Data Provider: options.ForceMetaDataProvider Value Type: double Default Value: 5.0 (as defined in org.eclipse.elk.force) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Dependencies: org.eclipse.elk.force.model (ForceModelStrategy.EADES) Description Factor for repulsive forces in Eades&amp;rsquo; model.</description>
    </item>
    
    <item>
      <title>edge</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-edge.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-edge.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.edge Options Edge Thickness Edge Type </description>
    </item>
    
    <item>
      <title>Edge Center Label Placement Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeLabels-centerLabelPlacementStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeLabels-centerLabelPlacementStrategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.edgeLabels.centerLabelPlacementStrategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.CenterEdgeLabelPlacementStrategy (Enum) Possible Values: MEDIAN_LAYER
TAIL_LAYER
HEAD_LAYER
SPACE_EFFICIENT_LAYER
WIDEST_LAYER (@ExperimentalPropertyValue)
CENTER_LAYER (@ExperimentalPropertyValue) Default Value: CenterEdgeLabelPlacementStrategy.MEDIAN_LAYER (as defined in org.eclipse.elk.layered) Applies To: parents, labels Containing Group: edgeLabels Description Determines in which layer center labels of long edges should be placed.</description>
    </item>
    
    <item>
      <title>Edge Edge Between Layer Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-edgeEdgeBetweenLayers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-edgeEdgeBetweenLayers.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.spacing.edgeEdgeBetweenLayers Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 10 (as defined in org.eclipse.elk.layered) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between pairs of edges that are routed between the same pair of layers. Note that &amp;lsquo;spacing.edgeEdge&amp;rsquo; is used for the spacing between pairs of edges crossing the same layer.</description>
    </item>
    
    <item>
      <title>Edge End Texture Length</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-edgeEndTextureLength.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-edgeEndTextureLength.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.edgeEndTextureLength Meta Data Provider: options.MrTreeMetaDataProvider Value Type: double Default Value: 7 (as defined in org.eclipse.elk.mrtree) Applies To: parents Description Should be set to the length of the texture at the end of an edge. This value can be used to improve the Edge Routing.</description>
    </item>
    
    <item>
      <title>Edge Label Placement</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeLabels-placement.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeLabels-placement.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.edgeLabels.placement Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.EdgeLabelPlacement (Enum) Possible Values: CENTER
HEAD
TAIL Default Value: EdgeLabelPlacement.CENTER (as defined in org.eclipse.elk) Applies To: labels Containing Group: edgeLabels Description Gives a hint on where to put edge labels.</description>
    </item>
    
    <item>
      <title>Edge Label Side Selection</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeLabels-sideSelection.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeLabels-sideSelection.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.edgeLabels.sideSelection Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.EdgeLabelSideSelection (Enum) Possible Values: ALWAYS_UP
ALWAYS_DOWN
DIRECTION_UP
DIRECTION_DOWN
SMART_UP
SMART_DOWN Default Value: EdgeLabelSideSelection.SMART_DOWN (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: edgeLabels Description Method to decide on edge label sides.</description>
    </item>
    
    <item>
      <title>Edge Label Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeLabel.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeLabel.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.edgeLabel Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 2 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description The minimal distance to be preserved between a label and the edge it is associated with. Note that the placement of a label is influenced by the &amp;rsquo;edgelabels.placement&amp;rsquo; option.</description>
    </item>
    
    <item>
      <title>Edge Node Between Layers Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-edgeNodeBetweenLayers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-edgeNodeBetweenLayers.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.spacing.edgeNodeBetweenLayers Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 10 (as defined in org.eclipse.elk.layered) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description The spacing to be preserved between nodes and edges that are routed next to the node&amp;rsquo;s layer. For the spacing between nodes and edges that cross the node&amp;rsquo;s layer &amp;lsquo;spacing.edgeNode&amp;rsquo; is used.</description>
    </item>
    
    <item>
      <title>Edge Node Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeNode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeNode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.edgeNode Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between nodes and edges.</description>
    </item>
    
    <item>
      <title>Edge Routing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeRouting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeRouting.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.edgeRouting Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.EdgeRouting (Enum) Possible Values: UNDEFINED
POLYLINE
ORTHOGONAL
SPLINES Default Value: EdgeRouting.UNDEFINED (as defined in org.eclipse.elk) Applies To: parents Description What kind of edge routing style should be applied for the content of a parent node. Algorithms may also set this option to single edges in order to mark them as splines. The bend point list of edges with this option set to SPLINES must be interpreted as control points for a piecewise cubic spline.</description>
    </item>
    
    <item>
      <title>Edge Routing Mode</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-edgeRoutingMode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-edgeRoutingMode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.edgeRoutingMode Meta Data Provider: options.MrTreeMetaDataProvider Value Type: org.eclipse.elk.alg.mrtree.options.EdgeRoutingMode (Enum) Possible Values: NONE
MIDDLE_TO_MIDDLE
AVOID_OVERLAP Default Value: EdgeRoutingMode.AVOID_OVERLAP (as defined in org.eclipse.elk.mrtree) Applies To: parents Description Chooses an Edge Routing algorithm.</description>
    </item>
    
    <item>
      <title>Edge Routing with Libavoid</title>
      <link>https://www.eclipse.org/elk/blog/posts/2022/22-11-17-libavoid.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2022/22-11-17-libavoid.html</guid>
      <description>By Miro Spönemann, November 17, 2022
I&amp;rsquo;m happy to announce the availability of a new Maven module / Eclipse plug-in org.eclipse.elk.alg.libavoid that offers orthogonal edge routing with fixed nodes. Credits for this work go to Ulf Rüegg, a former employee of the Real-Time and Embedded Systems group, where most of the development on ELK is happening. My contribution is merely to revive this code and include it in the building and publishing process of the ELK project.</description>
    </item>
    
    <item>
      <title>Edge Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeEdge.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-edgeEdge.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.edgeEdge Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between any two edges. Note that while this can somewhat easily be satisfied for the segments of orthogonally drawn edges, it is harder for general polylines or splines.</description>
    </item>
    
    <item>
      <title>Edge Thickness</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edge-thickness.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edge-thickness.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.edge.thickness Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Applies To: edges Containing Group: edge Description The thickness of an edge. This is a hint on the line width used to draw an edge, possibly requiring more space to be reserved for it.</description>
    </item>
    
    <item>
      <title>Edge Type</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edge-type.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edge-type.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.edge.type Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.EdgeType (Enum) Possible Values: NONE
DIRECTED
UNDIRECTED
ASSOCIATION
GENERALIZATION
DEPENDENCY Default Value: EdgeType.NONE (as defined in org.eclipse.elk) Applies To: edges Containing Group: edge Description The type of an edge. This is usually used for UML class diagrams, where associations must be handled differently from generalizations.</description>
    </item>
    
    <item>
      <title>edgeLabels</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-edgeLabels.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-edgeLabels.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.edgeLabels Options Edge Label Placement Inline Edge Labels </description>
    </item>
    
    <item>
      <title>edgeLabels</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeLabels.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeLabels.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.edgeLabels Options Edge Label Side Selection Edge Center Label Placement Strategy </description>
    </item>
    
    <item>
      <title>edgeRouting</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.edgeRouting Options Self-Loop Distribution Self-Loop Ordering Subgroups splines polyline </description>
    </item>
    
    <item>
      <title>edgeRouting.polyline</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-polyline.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-polyline.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.edgeRouting.polyline Options Sloped Edge Zone Width </description>
    </item>
    
    <item>
      <title>edgeRouting.splines</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-splines.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-splines.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.edgeRouting.splines Options Spline Routing Mode Subgroups sloppy </description>
    </item>
    
    <item>
      <title>edgeRouting.splines.sloppy</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-splines-sloppy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-edgeRouting-splines-sloppy.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.edgeRouting.splines.sloppy Options Sloppy Spline Layer Spacing Factor </description>
    </item>
    
    <item>
      <title>ELK Box</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-box.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-box.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.box Meta Data Provider: core.options.CoreOptions Description Algorithm for packing of unconnected boxes, i.e. graphs without edges.
Supported Options Option Default Value Aspect Ratio 1.3f Box Layout Mode BoxLayoutProvider.PackingMode.SIMPLE Content Alignment ContentAlignment.topLeft() Expand Nodes false Interactive false Node Size Constraints EnumSet.noneOf(SizeConstraint) Node Size Minimum new KVector(0, 0) Node Size Options EnumSet.of(SizeOptions.DEFAULT_MINIMUM_SIZE) Node Spacing 15 Padding new ElkPadding(15) Priority 0 </description>
    </item>
    
    <item>
      <title>ELK Contributors</title>
      <link>https://www.eclipse.org/elk/documentation/contributors.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors.html</guid>
      <description>You want to contribute to the Eclipse Layout Kernel? Excellent! Here&amp;rsquo;s what you need to do:
Setting Everything Up: Get necessary accounts and prepare everything for us to be able to accept your contributions.
Development Workflow: Understand how development works at the ELK project and how to get your code submitted.</description>
    </item>
    
    <item>
      <title>ELK DisCo</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-disco.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-disco.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.disco Meta Data Provider: options.DisCoMetaDataProvider Description Layouter for arranging unconnected subgraphs. The subgraphs themselves are, by default, not laid out.
Supported Options Option Default Value Aspect Ratio &amp;lt;not defined&amp;gt; Components Spacing 20f Connected Components Compaction Strategy CompactionStrategy.POLYOMINO Connected Components Layout Algorithm &amp;lt;not defined&amp;gt; DCGraph &amp;lt;not defined&amp;gt; Edge Thickness 1 Fill Polyominoes true List of Polyominoes &amp;lt;not defined&amp;gt; Padding new ElkPadding(12) Polyomino Primary Sorting Criterion HighLevelSortingCriterion.NUM_OF_EXTERNAL_SIDES_THAN_NUM_OF_EXTENSIONS_LAST Polyomino Secondary Sorting Criterion LowLevelSortingCriterion.</description>
    </item>
    
    <item>
      <title>ELK Fixed</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-fixed.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-fixed.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.fixed Meta Data Provider: core.options.CoreOptions Description Keeps the current layout as it is, without any automatic modification. Optional coordinates can be given for nodes and edge bend points.
Supported Options Option Default Value Bend Points &amp;lt;not defined&amp;gt; Fixed Graph Size false Node Size Constraints EnumSet.noneOf(SizeConstraint) Node Size Minimum new KVector(0, 0) Padding new ElkPadding(15) Position &amp;lt;not defined&amp;gt; </description>
    </item>
    
    <item>
      <title>ELK Force</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-force.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-force.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force Meta Data Provider: options.ForceMetaDataProvider Description Force-based algorithm provided by the Eclipse Layout Kernel. Implements methods that follow physical analogies by simulating forces that move the nodes into a balanced distribution. Currently the original Eades model and the Fruchterman - Reingold model are supported.
Category: Force Layout algorithms that follow physical analogies by simulating a system of attractive and repulsive forces. The first successful method of this kind was proposed by Eades in 1984.</description>
    </item>
    
    <item>
      <title>ELK Layered</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-layered.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-layered.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered Meta Data Provider: options.LayeredMetaDataProvider Description Layer-based algorithm provided by the Eclipse Layout Kernel. Arranges as many edges as possible into one direction by placing nodes into subsequent layers. This implementation supports different routing styles (straight, orthogonal, splines); if orthogonal routing is selected, arbitrary port constraints are respected, thus enabling the layout of block diagrams such as actor-oriented models or circuit schematics. Furthermore, full layout of compound graphs with cross-hierarchy edges is supported when the respective option is activated on the top level.</description>
    </item>
    
    <item>
      <title>ELK Metadata Language</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/metadatalanguage.html</guid>
      <description>As described in other parts of the documentation, the Eclipse Layout Kernel relies on metadata about all available layout algorithms and the layout options they support. Supplying metadata for your layout algorithm is done by writing an ELK Metadata File in our textual metadata language. The file is used by the ELK SDK to generate the following Java classes:
An ILayoutMetaDataProvider that contains IProperty objects for each layout option you declare, along with a method that registers these options and layout algorithm categories with the LayoutMetaDataService.</description>
    </item>
    
    <item>
      <title>ELK Mr. Tree</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-mrtree.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-mrtree.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree Meta Data Provider: options.MrTreeMetaDataProvider Description Tree-based algorithm provided by the Eclipse Layout Kernel. Computes a spanning tree of the input graph and arranges all nodes according to the resulting parent-children hierarchy. I pity the fool who doesn&amp;rsquo;t use Mr. Tree Layout.
Category: Tree Specialized layout methods for trees, i.e. acyclic graphs. The regular structure of graphs that have no undirected cycles can be emphasized using an algorithm of this type.</description>
    </item>
    
    <item>
      <title>ELK Radial</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-radial.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-radial.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial Meta Data Provider: options.RadialMetaDataProvider Description A radial layout provider which is based on the algorithm of Peter Eades published in &amp;ldquo;Drawing free trees.&amp;rdquo;, published by International Institute for Advanced Study of Social Information Science, Fujitsu Limited in 1991. The radial layouter takes a tree and places the nodes in radial order around the root. The nodes of the same tree level are placed on the same radius.</description>
    </item>
    
    <item>
      <title>ELK Randomizer</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-random.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-random.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.random Meta Data Provider: core.options.CoreOptions Description Distributes the nodes randomly on the plane, leading to very obfuscating layouts. Can be useful to demonstrate the power of &amp;ldquo;real&amp;rdquo; layout algorithms.
Supported Options Option Default Value Aspect Ratio 1.6f Node Spacing 15 Padding new ElkPadding(15) Randomization Seed 0 </description>
    </item>
    
    <item>
      <title>ELK Rectangle Packing</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-rectpacking.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-rectpacking.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking Meta Data Provider: options.RectPackingMetaDataProvider Description Algorithm for packing of unconnected boxes, i.e. graphs without edges. The given order of the boxes is always preserved and the main reading direction of the boxes is left to right. The algorithm is divided into two phases. One phase approximates the width in which the rectangles can be placed. The next phase places the rectangles in rows using the previously calculated width as bounding width and bundles rectangles with a similar height in blocks.</description>
    </item>
    
    <item>
      <title>ELK SPOrE Compaction</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-sporeCompaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-sporeCompaction.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.sporeCompaction Meta Data Provider: options.SporeMetaDataProvider Description ShrinkTree is a compaction algorithm that maintains the topology of a layout. The relocation of diagram elements is based on contracting a spanning tree.
Additional Documentation The algorithm uses the programmatic structure of SPOrE, which stands for Structure, Processing Order, and Execution.
The Three Phases The structure phase extracts geometric information from the given layout to capture the topology that should be preserved during the subsequent adjustment of the layout.</description>
    </item>
    
    <item>
      <title>ELK SPOrE Overlap Removal</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-sporeOverlap.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-sporeOverlap.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.sporeOverlap Meta Data Provider: options.SporeMetaDataProvider Description A node overlap removal algorithm proposed by Nachmanson et al. in &amp;ldquo;Node overlap removal by growing a tree&amp;rdquo;.
Supported Options Option Default Value Debug Mode false Node Spacing 8 Padding new ElkPadding(8) Structure Extraction Strategy StructureExtractionStrategy.DELAUNAY_TRIANGULATION Underlying Layout Algorithm &amp;lt;not defined&amp;gt; Upper limit for iterations of overlap removal 64 Whether to run a supplementary scanline overlap check. true </description>
    </item>
    
    <item>
      <title>ELK Stress</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-stress.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-stress.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress Meta Data Provider: options.StressMetaDataProvider Description Minimizes the stress within a layout using stress majorization. Stress exists if the euclidean distance between a pair of nodes doesn&amp;rsquo;t match their graph theoretic distance, that is, the shortest path between the two nodes. The method allows to specify individual edge lengths.
Category: Force Layout algorithms that follow physical analogies by simulating a system of attractive and repulsive forces. The first successful method of this kind was proposed by Eades in 1984.</description>
    </item>
    
    <item>
      <title>ELK Text Format</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/elktextformat.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/elktextformat.html</guid>
      <description>Documentation is coming soon. For the moment you can find an example on github.</description>
    </item>
    
    <item>
      <title>ELK Top-down Packing</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-topdownpacking.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-topdownpacking.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.topdownpacking Meta Data Provider: options.TopdownpackingMetaDataProvider Description An algorithm for placing boxes of fixed sizes. Expands boxes horizontally to fill empty whitespace. This algorithm can be used standalone or specifically for {@link CoreOptions.TOPDOWN_LAYOUT}. In this use case it should be set for nodes whose {@link CoreOptions.TOPDOWN_NODE_TYPE} is set to {@link TopdownNodeTypes.PARALLEL_NODE}. This allows topdown layout to give children larger sizes based on their number of children.
Supported Options Option Default Value Node arrangement strategy NodeArrangementStrategy.</description>
    </item>
    
    <item>
      <title>Enable Hyperedges From Common Source</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-enableHyperedgesFromCommonSource.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-enableHyperedgesFromCommonSource.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.enableHyperedgesFromCommonSource Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option enables a post-processing step that creates hyperedges for all edges with the same source. Be aware that this step will significantly decrease performance.</description>
    </item>
    
    <item>
      <title>Epsilon</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-epsilon.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-epsilon.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.epsilon Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: double Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Description Terminating condition. If the length squared of all energy gradients are less than epsilon, the algorithm stops.</description>
    </item>
    
    <item>
      <title>Expand Nodes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-expandNodes.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-expandNodes.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.expandNodes Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description If active, nodes are expanded to fill the area of their parent.</description>
    </item>
    
    <item>
      <title>Favor Straight Edges Over Balancing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-favorStraightEdges.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-favorStraightEdges.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.nodePlacement.favorStraightEdges Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Applies To: parents Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.NETWORK_SIMPLEX), org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.BRANDES_KOEPF) Containing Group: nodePlacement Description Favor straight edges over a balanced node placement. The default behavior is determined automatically based on the used &amp;rsquo;edgeRouting&amp;rsquo;. For an orthogonal style it is set to true, for all other styles to false.</description>
    </item>
    
    <item>
      <title>Feedback Edges</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-feedbackEdges.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-feedbackEdges.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.feedbackEdges Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Description Whether feedback edges should be highlighted by routing around the nodes.</description>
    </item>
    
    <item>
      <title>Fill Polyominoes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-fill.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-fill.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.polyomino.fill Meta Data Provider: options.PolyominoOptions Value Type: boolean Default Value: true (as defined in org.eclipse.elk) Applies To: parents Containing Group: polyomino Description Use the Profile Fill algorithm to fill polyominoes to prevent small polyominoes from being placed inside of big polyominoes with large holes. Might increase packing area.</description>
    </item>
    
    <item>
      <title>Fixed Graph Size</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-fixedGraphSize.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-fixedGraphSize.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.nodeSize.fixedGraphSize Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Containing Group: nodeSize Description By default, the fixed layout provider will enlarge a graph until it is large enough to contain its children. If this option is set, it won&amp;rsquo;t do so.</description>
    </item>
    
    <item>
      <title>Fixed Position</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-fixed.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-fixed.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress.fixed Meta Data Provider: options.StressMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.stress) Applies To: nodes Description Prevent that the node is moved by the layout algorithm.</description>
    </item>
    
    <item>
      <title>Fixed Shared Path Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-fixedSharedPathPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-fixedSharedPathPenalty.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.fixedSharedPathPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied whenever a connector path shares some segments with an immovable portion of an existing connector route (such as the first or last segment of a connector).</description>
    </item>
    
    <item>
      <title>font</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-font.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-font.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.font Options Font Name Font Size </description>
    </item>
    
    <item>
      <title>Font Name</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-font-name.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-font-name.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.font.name Meta Data Provider: core.options.CoreOptions Value Type: java.lang.String Applies To: labels Containing Group: font Description Font name used for a label.</description>
    </item>
    
    <item>
      <title>Font Size</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-font-size.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-font-size.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.font.size Meta Data Provider: core.options.CoreOptions Value Type: int Lower Bound: 1 Applies To: labels Containing Group: font Description Font size used for a label.</description>
    </item>
    
    <item>
      <title>Force Model</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-model.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-model.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force.model Meta Data Provider: options.ForceMetaDataProvider Value Type: org.eclipse.elk.alg.force.options.ForceModelStrategy (Enum) Possible Values: EADES
FRUCHTERMAN_REINGOLD Default Value: ForceModelStrategy.FRUCHTERMAN_REINGOLD (as defined in org.eclipse.elk.force) Applies To: parents Description Determines the model for force calculation.</description>
    </item>
    
    <item>
      <title>Force Node Model Order</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-forceNodeModelOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-forceNodeModelOrder.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.crossingMinimization.forceNodeModelOrder Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: crossingMinimization Description The node order given by the model does not change to produce a better layout. E.g. if node A is before node B in the model this is not changed during crossing minimization. This assumes that the node model order is already respected before crossing minimization.</description>
    </item>
    
    <item>
      <title>FR Temperature</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-temperature.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-temperature.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force.temperature Meta Data Provider: options.ForceMetaDataProvider Value Type: double Default Value: 0.001 (as defined in org.eclipse.elk.force) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Dependencies: org.eclipse.elk.force.model (ForceModelStrategy.FRUCHTERMAN_REINGOLD) Description The temperature is used as a scaling factor for particle displacements.</description>
    </item>
    
    <item>
      <title>Generate Position and Layer IDs</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-generatePositionAndLayerIds.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-generatePositionAndLayerIds.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.generatePositionAndLayerIds Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Description If enabled position id and layer id are generated, which are usually only used internally when setting the interactiveLayout option. This option should be specified on the root node.</description>
    </item>
    
    <item>
      <title>Getting Eclipse Ready</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/gettingeclipseready.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/gettingeclipseready.html</guid>
      <description>Developing layout algorithms requires you to have the Eclipse Layout Kernel SDK installed in your Eclipse installation. Follow these steps to install it:
Start Eclipse and select Help - Install New Software&amp;hellip; from the menu. In the Work with: field, enter the address of our release or nightly update site, which you can find in our Downloads section. From the Eclipse Layout Kernel category, check Eclipse Layout Kernel (Incubation) - SDK as well as Eclipse Layout Kernel (Incubation) - SDK (sources) and click Next.</description>
    </item>
    
    <item>
      <title>Graph Data Structure</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure.html</guid>
      <description>To represent graphs that should be laid out, the Eclipse Layout Kernel provides a robust EMF-based data structure. This page is about describing that graph data structure.
Terminology Graphs This is a simple example graph along with the terminology that goes with it.
Inclusion Trees Inclusion trees capture the hierarchical structure of a graph. See below for the inclusion tree of the graph we just saw, along with the terminology that goes with it.</description>
    </item>
    
    <item>
      <title>Graph Wrapping Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-strategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.WrappingStrategy (Enum) Possible Values: OFF
SINGLE_EDGE
MULTI_EDGE Default Value: WrappingStrategy.OFF (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: wrapping Description For certain graphs and certain prescribed drawing areas it may be desirable to split the laid out graph into chunks that are placed side by side. The edges that connect different chunks are &amp;lsquo;wrapped&amp;rsquo; around from the end of one chunk to the start of the other chunk.</description>
    </item>
    
    <item>
      <title>Graphviz Circo</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-circo.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-circo.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.circo Meta Data Provider: layouter.GraphvizMetaDataProvider Description Circular layout, after Six and Tollis &amp;lsquo;99, Kaufmann and Wiese &amp;lsquo;02. The algorithm finds biconnected components and arranges each component in a circle, trying to minimize the number of crossings inside the circle. This is suitable for certain diagrams of multiple cyclic structures such as certain telecommunications networks.
Category: Circle Circular layout algorithms emphasize cycles or biconnected components of a graph by arranging them in circles.</description>
    </item>
    
    <item>
      <title>Graphviz Dot</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-dot.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-dot.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.dot Meta Data Provider: layouter.GraphvizMetaDataProvider Description Layered drawings of directed graphs. The algorithm aims edges in the same direction (top to bottom, or left to right) and then attempts to avoid edge crossings and reduce edge length. Edges are routed as spline curves and are thus drawn very smoothly. This algorithm is very suitable for state machine and activity diagrams, where the direction of edges has an important role.</description>
    </item>
    
    <item>
      <title>Graphviz FDP</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-fdp.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-fdp.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.fdp Meta Data Provider: layouter.GraphvizMetaDataProvider Description Spring model layouts similar to those of Neato, but does this by reducing forces rather than working with energy. Fdp implements the Fruchterman-Reingold heuristic including a multigrid solver that handles larger graphs and clustered undirected graphs.
Category: Force Layout algorithms that follow physical analogies by simulating a system of attractive and repulsive forces. The first successful method of this kind was proposed by Eades in 1984.</description>
    </item>
    
    <item>
      <title>Graphviz Neato</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-neato.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-neato.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.neato Meta Data Provider: layouter.GraphvizMetaDataProvider Description Spring model layouts. Neato attempts to minimize a global energy function, which is equivalent to statistical multi-dimensional scaling. The solution is achieved using stress majorization, though the older Kamada-Kawai algorithm, using steepest descent, is also available.
Category: Force Layout algorithms that follow physical analogies by simulating a system of attractive and repulsive forces. The first successful method of this kind was proposed by Eades in 1984.</description>
    </item>
    
    <item>
      <title>Graphviz Twopi</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-twopi.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-graphviz-twopi.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.twopi Meta Data Provider: layouter.GraphvizMetaDataProvider Description Radial layouts, after Wills &amp;lsquo;97. The nodes are placed on concentric circles depending on their distance from a given root node. The algorithm is designed to handle not only small graphs, but also very large ones.
Category: Radial Radial layout algorithms usually position the nodes of the graph on concentric circles.
Supported Graph Features Name Description Self Loops Edges connecting a node with itself.</description>
    </item>
    
    <item>
      <title>Greedy Switch Activation Threshold</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-activationThreshold.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-activationThreshold.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.greedySwitch.activationThreshold Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 40 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: parents Containing Group: crossingMinimization -&amp;gt; greedySwitch Description By default it is decided automatically if the greedy switch is activated or not. The decision is based on whether the size of the input graph (without dummy nodes) is smaller than the value of this option. A &amp;lsquo;0&amp;rsquo; enforces the activation.</description>
    </item>
    
    <item>
      <title>Greedy Switch Crossing Minimization</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-type.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-type.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.greedySwitch.type Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.GreedySwitchType (Enum) Possible Values: ONE_SIDED
TWO_SIDED
OFF Default Value: GreedySwitchType.TWO_SIDED (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.crossingMinimization.strategy (CrossingMinimizationStrategy.LAYER_SWEEP) Containing Group: crossingMinimization -&amp;gt; greedySwitch Description Greedy Switch strategy for crossing minimization. The greedy switch heuristic is executed after the regular crossing minimization as a post-processor. Note that if &amp;lsquo;hierarchyHandling&amp;rsquo; is set to &amp;lsquo;INCLUDE_CHILDREN&amp;rsquo;, the &amp;lsquo;greedySwitchHierarchical.type&amp;rsquo; option must be used.</description>
    </item>
    
    <item>
      <title>Greedy Switch Crossing Minimization (hierarchical)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical-type.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical-type.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.greedySwitchHierarchical.type Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.GreedySwitchType (Enum) Possible Values: ONE_SIDED
TWO_SIDED
OFF Default Value: GreedySwitchType.OFF (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.crossingMinimization.strategy (CrossingMinimizationStrategy.LAYER_SWEEP), org.eclipse.elk.hierarchyHandling (HierarchyHandling.INCLUDE_CHILDREN) Containing Group: crossingMinimization -&amp;gt; greedySwitchHierarchical Description Activates the greedy switch heuristic in case hierarchical layout is used. The differences to the non-hierarchical case (see &amp;lsquo;greedySwitch.type&amp;rsquo;) are: 1) greedy switch is inactive by default, 3) only the option value set on the node at which hierarchical layout starts is relevant, and 2) if it&amp;rsquo;s activated by the user, it properly addresses hierarchy-crossing edges.</description>
    </item>
    
    <item>
      <title>Hierarchical Sweepiness</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-hierarchicalSweepiness.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-hierarchicalSweepiness.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.hierarchicalSweepiness Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 0.1 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.hierarchyHandling (HierarchyHandling.INCLUDE_CHILDREN) Containing Group: crossingMinimization Description How likely it is to use cross-hierarchy (1) vs bottom-up (-1).</description>
    </item>
    
    <item>
      <title>Hierarchy Handling</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hierarchyHandling.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hierarchyHandling.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.hierarchyHandling Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.HierarchyHandling (Enum) Possible Values: INHERIT
INCLUDE_CHILDREN
SEPARATE_CHILDREN Default Value: HierarchyHandling.INHERIT (as defined in org.eclipse.elk) Applies To: parents, nodes Description Determines whether separate layout runs are triggered for different compound nodes in a hierarchical graph. Setting a node&amp;rsquo;s hierarchy handling to INCLUDE_CHILDREN will lay out that node and all of its descendants in a single layout run, until a descendant is encountered which has its hierarchy handling set to SEPARATE_CHILDREN.</description>
    </item>
    
    <item>
      <title>Hierarchy Handling (Graphviz Dot)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hierarchyHandling_org-eclipse-elk-graphviz-dot.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hierarchyHandling_org-eclipse-elk-graphviz-dot.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.hierarchyHandling Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.HierarchyHandling (Enum) Possible Values: INHERIT
INCLUDE_CHILDREN
SEPARATE_CHILDREN Default Value: HierarchyHandling.INHERIT (as defined in org.eclipse.elk) Applies To: parents, nodes Description Determines whether separate layout runs are triggered for different compound nodes in a hierarchical graph. Setting a node&amp;rsquo;s hierarchy handling to INCLUDE_CHILDREN will lay out that node and all of its descendants in a single layout run, until a descendant is encountered which has its hierarchy handling set to SEPARATE_CHILDREN.</description>
    </item>
    
    <item>
      <title>High Degree Node Maximum Tree Height</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-treeHeight.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-treeHeight.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.highDegreeNodes.treeHeight Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 5 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: parents Dependencies: org.eclipse.elk.layered.highDegreeNodes.treatment (true) Containing Group: highDegreeNodes Description Maximum height of a subtree connected to a high degree node to be moved to separate layers.</description>
    </item>
    
    <item>
      <title>High Degree Node Threshold</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-threshold.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-threshold.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.highDegreeNodes.threshold Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 16 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: parents Dependencies: org.eclipse.elk.layered.highDegreeNodes.treatment (true) Containing Group: highDegreeNodes Description Whether a node is considered to have a high degree.</description>
    </item>
    
    <item>
      <title>High Degree Node Treatment</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-treatment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-highDegreeNodes-treatment.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.highDegreeNodes.treatment Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: highDegreeNodes Description Makes room around high degree nodes to place leafs and trees.</description>
    </item>
    
    <item>
      <title>highDegreeNodes</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-highDegreeNodes.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-highDegreeNodes.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.highDegreeNodes Options High Degree Node Treatment High Degree Node Threshold High Degree Node Maximum Tree Height </description>
    </item>
    
    <item>
      <title>Horizontal spacing between Label and Port</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelPortHorizontal.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelPortHorizontal.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.labelPortHorizontal Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Applies To: parents Containing Group: spacing Description Horizontal spacing to be preserved between labels and the ports they are associated with. Note that the placement of a label is influenced by the &amp;lsquo;portlabels.placement&amp;rsquo; option.</description>
    </item>
    
    <item>
      <title>Hypernode</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hypernode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-hypernode.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.hypernode Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: nodes Description Whether the node should be handled as a hypernode.</description>
    </item>
    
    <item>
      <title>Ideal Nudging Distance</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-idealNudgingDistance.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-idealNudgingDistance.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.idealNudgingDistance Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 4 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This parameter defines the spacing distance that will be used for nudging apart overlapping corners and line segments of connectors.</description>
    </item>
    
    <item>
      <title>Improve Cuts</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveCuts.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveCuts.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.multiEdge.improveCuts Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping -&amp;gt; multiEdge Description For general graphs it is important that not too many edges wrap backwards. Thus a compromise between evenly-distributed cuts and the total number of cut edges is sought.</description>
    </item>
    
    <item>
      <title>Improve Hyperedge Routes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingJunctions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingJunctions.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.improveHyperedgeRoutesMovingJunctions Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option causes hyperedge routes to be locally improved fixing obviously bad paths. As part of this process libavoid will effectively move junctions, setting new ideal positions for each junction.</description>
    </item>
    
    <item>
      <title>Improve Hyperedge Routes Add/Delete</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingAddingAndDeletingJunctions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingAddingAndDeletingJunctions.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.improveHyperedgeRoutesMovingAddingAndDeletingJunctions Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option causes hyperedge routes to be locally improved fixing obviously bad paths. It can cause junctions and connectors to be added or removed from hyperedges. As part of this process libavoid will effectively move junctions by setting new ideal positions for each remaining or added junction.</description>
    </item>
    
    <item>
      <title>Improve Wrapped Edges</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveWrappedEdges.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveWrappedEdges.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.multiEdge.improveWrappedEdges Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping -&amp;gt; multiEdge Description The initial wrapping is performed in a very simple way. As a consequence, edges that wrap from one chunk to another may be unnecessarily long. Activating this option tries to shorten such edges.</description>
    </item>
    
    <item>
      <title>In Layer Predecessor of</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerPredOf.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerPredOf.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.crossingMinimization.inLayerPredOf Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.lang.String Default Value: null (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: crossingMinimization Description Allows to set a constraint which specifies of which node the current node is the predecessor. If set to &amp;rsquo;s&amp;rsquo; then the node is the predecessor of &amp;rsquo;s&amp;rsquo; and is in the same layer</description>
    </item>
    
    <item>
      <title>In Layer Successor of</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerSuccOf.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerSuccOf.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.inLayerSuccOf Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.lang.String Default Value: null (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: crossingMinimization Description Allows to set a constraint which specifies of which node the current node is the successor. If set to &amp;rsquo;s&amp;rsquo; then the node is the successor of &amp;rsquo;s&amp;rsquo; and is in the same layer</description>
    </item>
    
    <item>
      <title>In new Row</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-inNewRow.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-inNewRow.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.inNewRow Meta Data Provider: options.RectPackingMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.rectpacking) Applies To: nodes Description If set to true this node begins in a new row. Consequently this node cannot be moved in a previous layer during compaction. Width approximation does does not take this into account.</description>
    </item>
    
    <item>
      <title>Individual Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-individual.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-individual.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.spacing.individual Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.util.IndividualSpacings Applies To: nodes, edges, ports, labels Containing Group: spacing Description Allows to specify individual spacing values for graph elements that shall be different from the value specified for the element&amp;rsquo;s parent.
Additional Documentation In most cases, spacing values apply to the children of the hierarchical node (possibly the root node) for which the values are actually specified.</description>
    </item>
    
    <item>
      <title>Individual Spacing (ELK Layered)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-individual_org-eclipse-elk-layered.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-individual_org-eclipse-elk-layered.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.spacing.individual Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.util.IndividualSpacings Applies To: nodes, edges, ports, labels Containing Group: spacing Description Allows to specify individual spacing values for graph elements that shall be different from the value specified for the element&amp;rsquo;s parent.
Additional Documentation Most parts of the algorithm do not support this yet.</description>
    </item>
    
    <item>
      <title>Inline Edge Labels</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeLabels-inline.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-edgeLabels-inline.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.edgeLabels.inline Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: labels Containing Group: edgeLabels Description If true, an edge label is placed directly on its edge. May only apply to center edge labels. This kind of label placement is only advisable if the label&amp;rsquo;s rendering is such that it is not crossed by its edge and thus stays legible.</description>
    </item>
    
    <item>
      <title>Inside Self Loop</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-insideSelfLoops-yo.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-insideSelfLoops-yo.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.insideSelfLoops.yo Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: edges Containing Group: insideSelfLoops Description Whether a self loop should be routed inside a node instead of around that node.</description>
    </item>
    
    <item>
      <title>insideSelfLoops</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-insideSelfLoops.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-insideSelfLoops.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.insideSelfLoops Options Activate Inside Self Loops Inside Self Loop </description>
    </item>
    
    <item>
      <title>Installing With Oomph</title>
      <link>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow/installingwithoomph.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/contributors/developmentworkflow/installingwithoomph.html</guid>
      <description>Setting up an Eclipse to work on the Eclipse Layout Kernel is comparatively easy when using the Oomph Installer. Follow this step-by-step guide for great glory.
Start the installer. This is what it will look like, more or less:
Click on the Hamburger menu (the three stripes at the top right). A menu with additional options will magically appear:
Click the Advanced Mode button (we&amp;rsquo;re programmers, we don&amp;rsquo;t want to deal with the simple stuff).</description>
    </item>
    
    <item>
      <title>Interactive</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-interactive.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-interactive.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.interactive Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether the algorithm should be run in interactive mode for the content of a parent node. What this means exactly depends on how the specific algorithm interprets this option. Usually in the interactive mode algorithms try to modify the current layout as little as possible.</description>
    </item>
    
    <item>
      <title>interactive Layout</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-interactiveLayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-interactiveLayout.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.interactiveLayout Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether the graph should be changeable interactively and by setting constraints</description>
    </item>
    
    <item>
      <title>Interactive Reference Point</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-interactiveReferencePoint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-interactiveReferencePoint.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.interactiveReferencePoint Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.InteractiveReferencePoint (Enum) Possible Values: CENTER
TOP_LEFT Default Value: InteractiveReferencePoint.CENTER (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.cycleBreaking.strategy (CycleBreakingStrategy.INTERACTIVE), org.eclipse.elk.layered.crossingMinimization.strategy (CrossingMinimizationStrategy.INTERACTIVE) Description Determines which point of a node is considered by interactive layout phases.</description>
    </item>
    
    <item>
      <title>Iteration Limit</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-iterationLimit.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-iterationLimit.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress.iterationLimit Meta Data Provider: options.StressMetaDataProvider Value Type: int Default Value: Integer.MAX_VALUE (as defined in org.eclipse.elk.stress) Applies To: parents Description Maximum number of performed iterations. Takes higher precedence than &amp;rsquo;epsilon&#39;.</description>
    </item>
    
    <item>
      <title>Iterations</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-iterations.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-iterations.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force.iterations Meta Data Provider: options.ForceMetaDataProvider Value Type: int Default Value: 300 (as defined in org.eclipse.elk.force) Lower Bound: 1 Applies To: parents Description The number of iterations on the force model.</description>
    </item>
    
    <item>
      <title>Iterations Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-iterationsFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-iterationsFactor.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.graphviz.iterationsFactor Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: double Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Description Multiplicative scale factor for the maximal number of iterations used during crossing minimization, node ranking, and node positioning.</description>
    </item>
    
    <item>
      <title>JSON Format</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/jsonformat.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/jsonformat.html</guid>
      <description>The JSON graph format has five basic elements: nodes, ports, labels, edges, and edge sections. Details about each element can be found below, with some sections describing features common to multiple elements. Note that in the JSON code, mandatory fields are marked with an asterisk.
Nodes, Ports, Labels, Edges, and Edge Sections All elements, except labels, must have an id that uniquely identifies them. Labels are usually not referred to from other parts of the graph, which is why the id is optional.</description>
    </item>
    
    <item>
      <title>Junction Points</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-junctionPoints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-junctionPoints.html</guid>
      <description>Property Value Type: output Identifier: org.eclipse.elk.junctionPoints Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.KVectorChain Default Value: new KVectorChain() (as defined in org.eclipse.elk) Applies To: edges Description This option is not used as option, but as output of the layout algorithms. It is attached to edges and determines the points where junction symbols should be drawn in order to represent hyperedges with orthogonal routing. Whether such points are computed depends on the chosen layout algorithm and edge routing style.</description>
    </item>
    
    <item>
      <title>Label Angle</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-labelAngle.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-labelAngle.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.labelAngle Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: double Default Value: -25 (as defined in org.eclipse.elk.graphviz) Applies To: edges Description Angle between head / tail positioned edge labels and the corresponding edge.</description>
    </item>
    
    <item>
      <title>Label Distance</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-labelDistance.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-labelDistance.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.labelDistance Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: double Default Value: 1 (as defined in org.eclipse.elk.graphviz) Lower Bound: 0.0 Applies To: edges Description Distance of head / tail positioned edge labels to the source or target node.</description>
    </item>
    
    <item>
      <title>Label Manager</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-labelManager.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-labelManager.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.labelManager Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.labels.ILabelManager Applies To: parents, labels Description Label managers can shorten labels upon a layout algorithm&amp;rsquo;s request.</description>
    </item>
    
    <item>
      <title>Label Manager</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-labels-labelManager.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-labels-labelManager.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.labels.labelManager Meta Data Provider: LabelManagementOptions Value Type: org.eclipse.elk.core.labels.ILabelManager Applies To: parents, labels Description The label manager responsible for a given part of the graph. A label manager can either be attached to a compound node (in which case it is responsible for all labels inside) or to specific labels. The label manager can then be called by layout algorithms to modify labels that are too wide to try and shorten them to a given target width.</description>
    </item>
    
    <item>
      <title>Label Node Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelNode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelNode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.labelNode Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 5 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between labels and the border of node they are associated with. Note that the placement of a label is influenced by the &amp;rsquo;nodelabels.placement&amp;rsquo; option.</description>
    </item>
    
    <item>
      <title>Label Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelLabel.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelLabel.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.labelLabel Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 0 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Determines the amount of space to be left between two labels of the same graph element.</description>
    </item>
    
    <item>
      <title>Layer Bound</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-coffmanGraham-layerBound.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-coffmanGraham-layerBound.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.coffmanGraham.layerBound Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: Integer.MAX_VALUE (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.layering.strategy (LayeringStrategy.COFFMAN_GRAHAM) Containing Group: layering -&amp;gt; coffmanGraham Description The maximum number of nodes allowed per layer.</description>
    </item>
    
    <item>
      <title>Layer Choice Constraint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerChoiceConstraint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerChoiceConstraint.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.layerChoiceConstraint Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.lang.Integer Default Value: null (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: nodes Containing Group: layering Description Allows to set a constraint regarding the layer placement of a node. Let i be the value of teh constraint. Assumed the drawing has n layers and i &amp;lt; n. If set to i, it expresses that the node should be placed in i-th layer.</description>
    </item>
    
    <item>
      <title>Layer Constraint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerConstraint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerConstraint.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.layerConstraint Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.LayerConstraint (Enum) Possible Values: NONE
FIRST
FIRST_SEPARATE
LAST
LAST_SEPARATE Default Value: LayerConstraint.NONE (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: layering Description Determines a constraint on the placement of the node regarding the layering.</description>
    </item>
    
    <item>
      <title>Layer ID</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerId.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-layerId.html</guid>
      <description>Property Value Type: output Identifier: org.eclipse.elk.layered.layering.layerId Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: -1 (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: nodes Containing Group: layering Description Layer identifier that was calculated by ELK Layered for a node. This is only generated if interactiveLayot or generatePositionAndLayerIds is set.</description>
    </item>
    
    <item>
      <title>Layer Spacing Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-layerSpacingFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-layerSpacingFactor.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.layerSpacingFactor Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: double Default Value: 1 (as defined in org.eclipse.elk.graphviz) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Description Factor for the spacing of different layers (ranks).</description>
    </item>
    
    <item>
      <title>Layered: Constraining the Model</title>
      <link>https://www.eclipse.org/elk/blog/posts/2023/23-01-09-constraining-the-model.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2023/23-01-09-constraining-the-model.html</guid>
      <description>By Sören Domrös, January 9, 2023
Since it is often desired to constrain the layout in a specific way to achieve a desired layout or to increase layout stability, this post should summarize all current (ELK 0.8.1) and future (planned for 0.9.0) ways to do that. The following will focus primarily on constraining the node order. If you wish to constrain the ports have a look at the portConstraints property and this example.</description>
    </item>
    
    <item>
      <title>layering</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.layering Options Node Layering Strategy Layer Constraint Layer Choice Constraint Layer ID Subgroups minWidth nodePromotion coffmanGraham </description>
    </item>
    
    <item>
      <title>layering.coffmanGraham</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-coffmanGraham.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-coffmanGraham.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.layering.coffmanGraham Options Layer Bound </description>
    </item>
    
    <item>
      <title>layering.minWidth</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-minWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-minWidth.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.layering.minWidth Options Upper Bound On Width [MinWidth Layerer] Upper Layer Estimation Scaling Factor [MinWidth Layerer] </description>
    </item>
    
    <item>
      <title>layering.nodePromotion</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-nodePromotion.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-layering-nodePromotion.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.layering.nodePromotion Options Node Promotion Strategy Max Node Promotion Iterations </description>
    </item>
    
    <item>
      <title>Layout Algorithm</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-algorithm.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-algorithm.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.algorithm Meta Data Provider: core.options.CoreOptions Value Type: java.lang.String Applies To: parents Description Select a specific layout algorithm.</description>
    </item>
    
    <item>
      <title>Layout Ancestors</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layoutAncestors.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layoutAncestors.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.layoutAncestors Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether the hierarchy levels on the path from the selected element to the root of the diagram shall be included in the layout process.</description>
    </item>
    
    <item>
      <title>Layout Dimension</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-dimension.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-dimension.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress.dimension Meta Data Provider: options.StressMetaDataProvider Value Type: org.eclipse.elk.alg.force.stress.StressMajorization$Dimension (Enum) Possible Values: XY
X
Y Default Value: Dimension.XY (as defined in org.eclipse.elk.stress) Applies To: parents Description Dimensions that are permitted to be altered during layout.</description>
    </item>
    
    <item>
      <title>Layout Option Groups</title>
      <link>https://www.eclipse.org/elk/reference/groups.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups.html</guid>
      <description>The following layout algorithms are available in ELK:</description>
    </item>
    
    <item>
      <title>Layout Options</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/layoutoptions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/layoutoptions.html</guid>
      <description>Layout options are key-value pairs that configure the behavior of automatic layout algorithms. Let&amp;rsquo;s look at all of the involved classes first before we concentrate on how to actually use them in practice.
Involved Classes Layout options are represented by annotating objects with properties. There are two main types we need to cover: the properties themselves, and the objects we can configure through properties.
Properties The most important type when it comes to properties is the IProperty interface.</description>
    </item>
    
    <item>
      <title>Layout Options</title>
      <link>https://www.eclipse.org/elk/reference/options.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options.html</guid>
      <description>The following layout options are available in ELK:</description>
    </item>
    
    <item>
      <title>Layout Partition</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-partitioning-partition.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-partitioning-partition.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.partitioning.partition Meta Data Provider: core.options.CoreOptions Value Type: java.lang.Integer Applies To: parents, nodes Dependencies: org.eclipse.elk.partitioning.activate (true) Containing Group: partitioning Description Partition to which the node belongs. This requires Layout Partitioning to be active. Nodes with lower partition IDs will appear to the left of nodes with higher partition IDs (assuming a left-to-right layout direction).</description>
    </item>
    
    <item>
      <title>Layout Partitioning</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-partitioning-activate.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-partitioning-activate.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.partitioning.activate Meta Data Provider: core.options.CoreOptions Value Type: java.lang.Boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Containing Group: partitioning Description Whether to activate partitioned layout. This will allow to group nodes through the Layout Partition option. a pair of nodes with different partition indices is then placed such that the node with lower index is placed to the left of the other node (with left-to-right layout direction).</description>
    </item>
    
    <item>
      <title>Layout View Support</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html</guid>
      <description>If UI components are installed, ELK contributes the Layout View to the workbench:
The layout view is a kind of properties view specialized for setting layout properties. It listens to selection changes and, if it recognizes that something is an element of a graph ELK could layout, allows users to customize the layout options used to do so.
It is a fair question whether users should be given access to this view or not.</description>
    </item>
    
    <item>
      <title>Libavoid</title>
      <link>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-alg-libavoid.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/algorithms/org-eclipse-elk-alg-libavoid.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid Meta Data Provider: options.LibavoidMetaDataProvider Description libavoid is a cross-platform C++ library providing fast, object-avoiding orthogonal and polyline connector routing for use in interactive diagram editors.
Category: Edge Routing Only route the edges without touching the node&#39;s positions.
Supported Options Option Default Value Angle Penalty 0 Cluster Crossing Penalty 0 Crossing Penalty 0 Debug Mode false Direction Direction.UNDEFINED Edge Routing EdgeRouting.ORTHOGONAL Enable Hyperedges From Common Source false Fixed Shared Path Penalty 0 Ideal Nudging Distance 4 Improve Hyperedge Routes true Improve Hyperedge Routes Add/Delete false Marks a node as a cluster false Nudge Orthogonal Segments false Nudge Orthogonal Touching Colinear Segments false Nudge Shared Paths With Common Endpoint true Omit Node Micro Layout false Penalise Orthogonal Shared Paths false Perform Unifying Nudging Preprocessing true Port Constraints PortConstraints.</description>
    </item>
    
    <item>
      <title>Linear Segments Deflection Dampening</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-linearSegments-deflectionDampening.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-linearSegments-deflectionDampening.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.nodePlacement.linearSegments.deflectionDampening Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 0.3 (as defined in org.eclipse.elk.layered) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.LINEAR_SEGMENTS) Containing Group: nodePlacement -&amp;gt; linearSegments Description Dampens the movement of nodes to keep the diagram from getting too large.</description>
    </item>
    
    <item>
      <title>List of Polyominoes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-debug-discoPolys.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-disco-debug-discoPolys.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.disco.debug.discoPolys Meta Data Provider: options.DisCoMetaDataProvider Value Type: java.lang.Object Applies To: parents Containing Group: debug Description Access to the polyominoes is intended for the debug view,</description>
    </item>
    
    <item>
      <title>Long Edge Ordering Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-longEdgeStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-longEdgeStrategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.longEdgeStrategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.LongEdgeOrderingStrategy (Enum) Possible Values: DUMMY_NODE_OVER
DUMMY_NODE_UNDER
EQUAL Default Value: LongEdgeOrderingStrategy.DUMMY_NODE_OVER (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: considerModelOrder Description Indicates whether long edges are sorted under, over, or equal to nodes that have no connection to a previous layer in a left-to-right or right-to-left layout. Under and over changes to right and left in a vertical layout.</description>
    </item>
    
    <item>
      <title>Manually Specified Cuts</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-cuts.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-cuts.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.cutting.cuts Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.util.List&amp;lt;java.lang.Integer&amp;gt; Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.cutting.strategy (CuttingStrategy.MANUAL) Containing Group: wrapping -&amp;gt; cutting Description Allows the user to specify her own cuts for a certain graph.</description>
    </item>
    
    <item>
      <title>Margins</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-margins.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-margins.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.margins Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.ElkMargin Default Value: new ElkMargin() (as defined in org.eclipse.elk) Applies To: nodes Description Margins define additional space around the actual bounds of a graph element. For instance, ports or labels being placed on the outside of a node&amp;rsquo;s border might introduce such a margin. The margin is used to guarantee non-overlap of other graph elements with those ports or labels.</description>
    </item>
    
    <item>
      <title>Marks a node as a cluster</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-isCluster.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-isCluster.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.isCluster Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: nodes Description This option marks a node as a cluster, resulting in its children being handled as relative to the graph itself while the marked node is only added as a cluster. Note that clusters are experimental and can therefore have a negative impact on performance. The cluster node cannot have: - clusters as children - outgoing or incoming connections (directly to the node) - ports Edges into or out of the cluster must be added across the cluster&amp;rsquo;s borders, without the use of hierarchical ports.</description>
    </item>
    
    <item>
      <title>Max Node Promotion Iterations</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-nodePromotion-maxIterations.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-nodePromotion-maxIterations.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.nodePromotion.maxIterations Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: parents Dependencies: org.eclipse.elk.layered.layering.nodePromotion.strategy Containing Group: layering -&amp;gt; nodePromotion Description Limits the number of iterations for node promotion.</description>
    </item>
    
    <item>
      <title>Max. Iterations</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-maxiter.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-maxiter.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.graphviz.maxiter Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: int Lower Bound: 1 Applies To: parents Description The maximum number of iterations.</description>
    </item>
    
    <item>
      <title>Maximal Animation Time</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-maxAnimTime.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-maxAnimTime.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.maxAnimTime Meta Data Provider: core.options.CoreOptions Value Type: int Default Value: 4000 (as defined in org.eclipse.elk) Lower Bound: 0 Applies To: parents Description The maximal time for animations, in milliseconds.</description>
    </item>
    
    <item>
      <title>Merge Edges</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-mergeEdges.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-mergeEdges.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.mergeEdges Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Description Edges that have no ports are merged so they touch the connected nodes at the same points. When this option is disabled, one port is created for each edge directly connected to a node. When it is enabled, all such incoming edges share an input port, and all outgoing edges share an output port.</description>
    </item>
    
    <item>
      <title>Merge Hierarchy-Crossing Edges</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-mergeHierarchyEdges.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-mergeHierarchyEdges.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.mergeHierarchyEdges Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.layered) Applies To: parents Description If hierarchical layout is active, hierarchy-crossing edges use as few hierarchical ports as possible. They are broken by the algorithm, with hierarchical ports inserted as required. Usually, one such port is created for each edge at each hierarchy crossing point. With this option set to true, we try to create as few hierarchical ports as possible in the process.</description>
    </item>
    
    <item>
      <title>Minimal Animation Time</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-minAnimTime.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-minAnimTime.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.minAnimTime Meta Data Provider: core.options.CoreOptions Value Type: int Default Value: 400 (as defined in org.eclipse.elk) Lower Bound: 0 Applies To: parents Description The minimal time for animations, in milliseconds.</description>
    </item>
    
    <item>
      <title>MSD Freedom</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-msd-freedom.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-cutting-msd-freedom.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.cutting.msd.freedom Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.lang.Integer Default Value: 1 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.cutting.strategy (CuttingStrategy.MSD) Containing Group: wrapping -&amp;gt; cutting -&amp;gt; msd Description The MSD cutting strategy starts with an initial guess on the number of chunks the graph should be split into. The freedom specifies how much the strategy may deviate from this guess. E.</description>
    </item>
    
    <item>
      <title>No Layout</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-noLayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-noLayout.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.noLayout Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: nodes, edges, ports, labels Description No layout is done for the associated element. This is used to mark parts of a diagram to avoid their inclusion in the layout graph, or to mark parts of the layout graph to prevent layout engines from processing them. If you wish to exclude the contents of a compound node from automatic layout, while the node itself is still considered on its own layer, use the &amp;lsquo;Fixed Layout&amp;rsquo; algorithm for that node.</description>
    </item>
    
    <item>
      <title>No Model Order</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-noModelOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-considerModelOrder-noModelOrder.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.considerModelOrder.noModelOrder Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: considerModelOrder Description Set on a node to not set a model order for this node even though it is a real node.</description>
    </item>
    
    <item>
      <title>Node arrangement strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownpacking-nodeArrangement-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownpacking-nodeArrangement-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.topdownpacking.nodeArrangement.strategy Meta Data Provider: options.TopdownpackingMetaDataProvider Value Type: org.eclipse.elk.alg.topdownpacking.NodeArrangementStrategy (Enum) Possible Values: LEFT_RIGHT_TOP_DOWN_NODE_PLACER Default Value: NodeArrangementStrategy.LEFT_RIGHT_TOP_DOWN_NODE_PLACER (as defined in org.eclipse.elk.topdownpacking) Applies To: parents Containing Group: nodeArrangement Description Strategy for node arrangement. The strategy determines the size of the resulting graph.</description>
    </item>
    
    <item>
      <title>Node Flexibility</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.nodePlacement.networkSimplex.nodeFlexibility Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.NodeFlexibility (Enum) Possible Values: NONE
PORT_POSITION
NODE_SIZE_WHERE_SPACE_PERMITS (@ExperimentalPropertyValue)
NODE_SIZE Applies To: nodes Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.NETWORK_SIMPLEX) Containing Group: nodePlacement -&amp;gt; networkSimplex Description Aims at shorter and straighter edges. Two configurations are possible: (a) allow ports to move freely on the side they are assigned to (the order is always defined beforehand), (b) additionally allow to enlarge a node wherever it helps.</description>
    </item>
    
    <item>
      <title>Node Flexibility Default</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility-default.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility-default.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.nodePlacement.networkSimplex.nodeFlexibility.default Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.NodeFlexibility (Enum) Possible Values: NONE
PORT_POSITION
NODE_SIZE_WHERE_SPACE_PERMITS (@ExperimentalPropertyValue)
NODE_SIZE Default Value: NodeFlexibility.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.nodePlacement.strategy (NodePlacementStrategy.NETWORK_SIMPLEX) Containing Group: nodePlacement -&amp;gt; networkSimplex -&amp;gt; nodeFlexibility Description Default value of the &amp;rsquo;nodeFlexibility&amp;rsquo; option for the children of a hierarchical node.</description>
    </item>
    
    <item>
      <title>Node Label Padding</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeLabels-padding.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeLabels-padding.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.nodeLabels.padding Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.ElkPadding Default Value: new ElkPadding(5) (as defined in org.eclipse.elk) Applies To: parents Containing Group: nodeLabels Description Define padding for node labels that are placed inside of a node.</description>
    </item>
    
    <item>
      <title>Node Label Placement</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeLabels-placement.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeLabels-placement.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.nodeLabels.placement Meta Data Provider: core.options.CoreOptions Value Type: java.util.EnumSet&amp;lt;org.eclipse.elk.core.options.NodeLabelPlacement&amp;gt; Possible Values: H_LEFT
H_CENTER
H_RIGHT
V_TOP
V_CENTER
V_BOTTOM
INSIDE
OUTSIDE
H_PRIORITY Default Value: NodeLabelPlacement.fixed() (as defined in org.eclipse.elk) Applies To: nodes, labels Containing Group: nodeLabels Description Hints for where node labels are to be placed; if empty, the node label&amp;rsquo;s position is not modified.</description>
    </item>
    
    <item>
      <title>Node Layering Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.layering.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.LayeringStrategy (Enum) Possible Values: NETWORK_SIMPLEX
LONGEST_PATH
LONGEST_PATH_SOURCE
COFFMAN_GRAHAM (@AdvancedPropertyValue)
INTERACTIVE (@AdvancedPropertyValue)
STRETCH_WIDTH (@ExperimentalPropertyValue)
MIN_WIDTH (@ExperimentalPropertyValue)
BF_MODEL_ORDER (@ExperimentalPropertyValue)
DF_MODEL_ORDER (@ExperimentalPropertyValue) Default Value: LayeringStrategy.NETWORK_SIMPLEX (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: layering Description Strategy for node layering.</description>
    </item>
    
    <item>
      <title>Node Node Between Layers Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-nodeNodeBetweenLayers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-nodeNodeBetweenLayers.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.spacing.nodeNodeBetweenLayers Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 20 (as defined in org.eclipse.elk.layered) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description The spacing to be preserved between any pair of nodes of two adjacent layers. Note that &amp;lsquo;spacing.nodeNode&amp;rsquo; is used for the spacing between nodes within the layer itself.</description>
    </item>
    
    <item>
      <title>Node Placement Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-nodePlacement-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.nodePlacement.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.NodePlacementStrategy (Enum) Possible Values: SIMPLE
INTERACTIVE (@AdvancedPropertyValue)
LINEAR_SEGMENTS
BRANDES_KOEPF
NETWORK_SIMPLEX Default Value: NodePlacementStrategy.BRANDES_KOEPF (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: nodePlacement Description Strategy for node placement.</description>
    </item>
    
    <item>
      <title>Node Promotion Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-nodePromotion-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-nodePromotion-strategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.nodePromotion.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.NodePromotionStrategy (Enum) Possible Values: NONE
NIKOLOV
NIKOLOV_PIXEL
NIKOLOV_IMPROVED (@AdvancedPropertyValue)
NIKOLOV_IMPROVED_PIXEL (@AdvancedPropertyValue)
DUMMYNODE_PERCENTAGE (@AdvancedPropertyValue)
NODECOUNT_PERCENTAGE (@AdvancedPropertyValue)
NO_BOUNDARY (@AdvancedPropertyValue)
MODEL_ORDER_LEFT_TO_RIGHT
MODEL_ORDER_RIGHT_TO_LEFT Default Value: NodePromotionStrategy.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: layering -&amp;gt; nodePromotion Description Reduces number of dummy nodes after layering phase (if possible).</description>
    </item>
    
    <item>
      <title>Node Self Loop Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-nodeSelfLoop.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-nodeSelfLoop.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.nodeSelfLoop Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description Spacing to be preserved between a node and its self loops.</description>
    </item>
    
    <item>
      <title>Node Size Constraints</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-constraints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-constraints.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.nodeSize.constraints Meta Data Provider: core.options.CoreOptions Value Type: java.util.EnumSet&amp;lt;org.eclipse.elk.core.options.SizeConstraint&amp;gt; Possible Values: PORTS
PORT_LABELS
NODE_LABELS
MINIMUM_SIZE Default Value: EnumSet.noneOf(SizeConstraint) (as defined in org.eclipse.elk) Applies To: nodes Containing Group: nodeSize Description What should be taken into account when calculating a node&amp;rsquo;s size. Empty size constraints specify that a node&amp;rsquo;s size is already fixed and should not be changed.
Additional Documentation Size constraints basically restrict the freedom a layout algorithm has in resizing a node subject to its node labels, ports, and port labels.</description>
    </item>
    
    <item>
      <title>Node Size Minimum</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-minimum.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-minimum.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.nodeSize.minimum Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.KVector Default Value: new KVector(0, 0) (as defined in org.eclipse.elk) Applies To: nodes Containing Group: nodeSize Description The minimal size to which a node can be reduced.</description>
    </item>
    
    <item>
      <title>Node Size Options</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-options.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-nodeSize-options.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.nodeSize.options Meta Data Provider: core.options.CoreOptions Value Type: java.util.EnumSet&amp;lt;org.eclipse.elk.core.options.SizeOptions&amp;gt; Possible Values: DEFAULT_MINIMUM_SIZE
MINIMUM_SIZE_ACCOUNTS_FOR_PADDING
COMPUTE_PADDING
OUTSIDE_NODE_LABELS_OVERHANG
PORTS_OVERHANG
UNIFORM_PORT_SPACING
SPACE_EFFICIENT_PORT_LABELS (@Deprecated)
FORCE_TABULAR_NODE_LABELS
ASYMMETRICAL Default Value: EnumSet.of(SizeOptions.DEFAULT_MINIMUM_SIZE) (as defined in org.eclipse.elk) Applies To: nodes Containing Group: nodeSize Description Options modifying the behavior of the size constraints set on a node. Each member of the set specifies something that should be taken into account when calculating node sizes. The empty set corresponds to no further modifications.</description>
    </item>
    
    <item>
      <title>Node Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-nodeNode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-nodeNode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.nodeNode Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 20 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description The minimal distance to be preserved between each two nodes.</description>
    </item>
    
    <item>
      <title>nodeArrangement</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdownpacking-nodeArrangement.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdownpacking-nodeArrangement.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.topdownpacking.nodeArrangement Options Node arrangement strategy </description>
    </item>
    
    <item>
      <title>nodeLabels</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-nodeLabels.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-nodeLabels.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.nodeLabels Options Node Label Padding Node Label Placement </description>
    </item>
    
    <item>
      <title>nodePlacement</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.nodePlacement Options Node Placement Strategy Favor Straight Edges Over Balancing Subgroups bk linearSegments networkSimplex </description>
    </item>
    
    <item>
      <title>nodePlacement.bk</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-bk.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-bk.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.nodePlacement.bk Options BK Edge Straightening BK Fixed Alignment </description>
    </item>
    
    <item>
      <title>nodePlacement.linearSegments</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-linearSegments.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-linearSegments.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.nodePlacement.linearSegments Options Linear Segments Deflection Dampening </description>
    </item>
    
    <item>
      <title>nodePlacement.networkSimplex</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.nodePlacement.networkSimplex Options Node Flexibility Subgroups nodeFlexibility </description>
    </item>
    
    <item>
      <title>nodePlacement.networkSimplex.nodeFlexibility</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.nodePlacement.networkSimplex.nodeFlexibility Options Node Flexibility Default </description>
    </item>
    
    <item>
      <title>nodeSize</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-nodeSize.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-nodeSize.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.nodeSize Options Node Size Constraints Node Size Options Node Size Minimum Fixed Graph Size </description>
    </item>
    
    <item>
      <title>Nudge Orthogonal Segments</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalSegmentsConnectedToShapes.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalSegmentsConnectedToShapes.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.nudgeOrthogonalSegmentsConnectedToShapes Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option causes the final segments of connectors, which are attached to shapes, to be nudged apart. Usually these segments are fixed, since they are considered to be attached to ports.</description>
    </item>
    
    <item>
      <title>Nudge Orthogonal Touching Colinear Segments</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalTouchingColinearSegments.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalTouchingColinearSegments.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.nudgeOrthogonalTouchingColinearSegments Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option can be used to control whether colinear line segments that touch just at their ends will be nudged apart. The overlap will usually be resolved in the other dimension, so this is not usually required.</description>
    </item>
    
    <item>
      <title>Nudge Shared Paths With Common Endpoint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeSharedPathsWithCommonEndPoint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-nudgeSharedPathsWithCommonEndPoint.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.nudgeSharedPathsWithCommonEndPoint Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option determines whether intermediate segments of connectors that are attached to common endpoints will be nudged apart. Usually these segments get nudged apart, but you may want to turn this off if you would prefer that entire shared paths terminating at a common end point should overlap.</description>
    </item>
    
    <item>
      <title>Omit Node Micro Layout</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-omitNodeMicroLayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-omitNodeMicroLayout.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.omitNodeMicroLayout Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Node micro layout comprises the computation of node dimensions (if requested), the placement of ports and their labels, and the placement of node labels. The functionality is implemented independent of any specific layout algorithm and shouldn&amp;rsquo;t have any negative impact on the layout algorithm&amp;rsquo;s performance itself.</description>
    </item>
    
    <item>
      <title>Optimization Goal</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-optimizationGoal.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-optimizationGoal.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.widthApproximation.optimizationGoal Meta Data Provider: options.RectPackingMetaDataProvider Value Type: org.eclipse.elk.alg.rectpacking.options.OptimizationGoal (Enum) Possible Values: ASPECT_RATIO_DRIVEN
MAX_SCALE_DRIVEN
AREA_DRIVEN Default Value: OptimizationGoal.MAX_SCALE_DRIVEN (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: widthApproximation Description Optimization goal for approximation of the bounding box given by the first iteration. Determines whether layout is sorted by the maximum scaling, aspect ratio, or area. Depending on the strategy the aspect ratio might be nearly ignored.
Additional Documentation The packing strategy specifies what is most important during approximation of the drawing width.</description>
    </item>
    
    <item>
      <title>Order ID </title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-orderId.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-orderId.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.orderId Meta Data Provider: options.RadialMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.radial) Applies To: nodes Description The id can be used to define an order for nodes of one radius. This can be used to sort them in the layer accordingly.</description>
    </item>
    
    <item>
      <title>Orthogonal Compaction</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-compaction-orthogonal.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-compaction-orthogonal.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.compaction.orthogonal Meta Data Provider: options.SporeMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Containing Group: compaction Description Restricts the translation of nodes to orthogonal directions in the compaction phase.</description>
    </item>
    
    <item>
      <title>Outgoing Edge Angles</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-outgoingEdgeAngles.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-outgoingEdgeAngles.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.radial.rotation.outgoingEdgeAngles Meta Data Provider: options.RadialMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.radial) Applies To: parents Containing Group: rotation Description Calculate the required angle of connected nodes to leave space for an incoming edge. This option should only be used in conjunction with top-down layout.</description>
    </item>
    
    <item>
      <title>Overlap Removal</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-overlapMode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-graphviz-overlapMode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.graphviz.overlapMode Meta Data Provider: layouter.GraphvizMetaDataProvider Value Type: org.eclipse.elk.alg.graphviz.dot.transform.OverlapMode (Enum) Possible Values: NONE
SCALE
SCALEXY
PRISM
COMPRESS Default Value: OverlapMode.PRISM (as defined in org.eclipse.elk.graphviz) Applies To: parents Description Determines if and how node overlaps should be removed.</description>
    </item>
    
    <item>
      <title>overlapRemoval</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-overlapRemoval.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-overlapRemoval.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.overlapRemoval Options Upper limit for iterations of overlap removal Whether to run a supplementary scanline overlap check. </description>
    </item>
    
    <item>
      <title>packing</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-packing.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-packing.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.packing Options Compaction Strategy Subgroups compaction </description>
    </item>
    
    <item>
      <title>packing.compaction</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-packing-compaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-packing-compaction.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.rectpacking.packing.compaction Options Row Height Reevaluation Compaction iterations </description>
    </item>
    
    <item>
      <title>Padding</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-padding.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-padding.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.padding Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.ElkPadding Default Value: new ElkPadding(12) (as defined in org.eclipse.elk) Applies To: parents, nodes Description The padding to be left to a parent element&amp;rsquo;s border when placing child elements. This can also serve as an output option of a layout algorithm if node size calculation is setup appropriately.</description>
    </item>
    
    <item>
      <title>partitioning</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-partitioning.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-partitioning.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.partitioning Options Layout Partition Layout Partitioning </description>
    </item>
    
    <item>
      <title>Penalise Orthogonal Shared Paths</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-penaliseOrthogonalSharedPathsAtConnEnds.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-penaliseOrthogonalSharedPathsAtConnEnds.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.penaliseOrthogonalSharedPathsAtConnEnds Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option penalises and attempts to reroute orthogonal shared connector paths terminating at a common junction or shape connection pin. When multiple connector paths enter or leave the same side of a junction (or shape pin), the router will attempt to reroute these to different sides of the junction or different shape pins.</description>
    </item>
    
    <item>
      <title>Perform Unifying Nudging Preprocessing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-performUnifyingNudgingPreprocessingStep.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-performUnifyingNudgingPreprocessingStep.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.performUnifyingNudgingPreprocessingStep Meta Data Provider: options.LibavoidMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This option can be used to control whether the router performs a preprocessing step before orthogonal nudging where is tries to unify segments and centre them in free space. This generally results in better quality ordering and nudging.</description>
    </item>
    
    <item>
      <title>polyomino</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-polyomino.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-polyomino.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.polyomino Options Polyomino Traversal Strategy Polyomino Secondary Sorting Criterion Polyomino Primary Sorting Criterion Fill Polyominoes </description>
    </item>
    
    <item>
      <title>Polyomino Primary Sorting Criterion</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-highLevelSort.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-highLevelSort.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.polyomino.highLevelSort Meta Data Provider: options.PolyominoOptions Value Type: org.eclipse.elk.alg.common.compaction.options.HighLevelSortingCriterion (Enum) Possible Values: NUM_OF_EXTERNAL_SIDES_THAN_NUM_OF_EXTENSIONS_LAST
CORNER_CASES_THAN_SINGLE_SIDE_LAST Default Value: HighLevelSortingCriterion.NUM_OF_EXTERNAL_SIDES_THAN_NUM_OF_EXTENSIONS_LAST (as defined in org.eclipse.elk) Applies To: parents Containing Group: polyomino Description Possible primary sorting criteria for the processing order of polyominoes.</description>
    </item>
    
    <item>
      <title>Polyomino Secondary Sorting Criterion</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-lowLevelSort.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-lowLevelSort.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.polyomino.lowLevelSort Meta Data Provider: options.PolyominoOptions Value Type: org.eclipse.elk.alg.common.compaction.options.LowLevelSortingCriterion (Enum) Possible Values: BY_SIZE
BY_SIZE_AND_SHAPE Default Value: LowLevelSortingCriterion.BY_SIZE_AND_SHAPE (as defined in org.eclipse.elk) Applies To: parents Containing Group: polyomino Description Possible secondary sorting criteria for the processing order of polyominoes. They are used when polyominoes are equal according to the primary sorting criterion HighLevelSortingCriterion.</description>
    </item>
    
    <item>
      <title>Polyomino Traversal Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-traversalStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-polyomino-traversalStrategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.polyomino.traversalStrategy Meta Data Provider: options.PolyominoOptions Value Type: org.eclipse.elk.alg.common.compaction.options.TraversalStrategy (Enum) Possible Values: SPIRAL
LINE_BY_LINE
MANHATTAN
JITTER
QUADRANTS_LINE_BY_LINE
QUADRANTS_MANHATTAN
QUADRANTS_JITTER
COMBINE_LINE_BY_LINE_MANHATTAN
COMBINE_JITTER_MANHATTAN Default Value: TraversalStrategy.QUADRANTS_LINE_BY_LINE (as defined in org.eclipse.elk) Applies To: parents Containing Group: polyomino Description Traversal strategy for trying different candidate positions for polyominoes.</description>
    </item>
    
    <item>
      <title>port</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-port.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-port.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.port Options Port Anchor Offset Port Index Port Side Port Border Offset </description>
    </item>
    
    <item>
      <title>Port Alignment</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-default.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-default.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.portAlignment.default Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortAlignment (Enum) Possible Values: DISTRIBUTED
JUSTIFIED
BEGIN
CENTER
END Default Value: PortAlignment.DISTRIBUTED (as defined in org.eclipse.elk) Applies To: nodes Containing Group: portAlignment Description Defines the default port distribution for a node. May be overridden for each side individually.</description>
    </item>
    
    <item>
      <title>Port Alignment (East)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-east.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-east.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.portAlignment.east Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortAlignment (Enum) Possible Values: DISTRIBUTED
JUSTIFIED
BEGIN
CENTER
END Applies To: nodes Containing Group: portAlignment Description Defines how ports on the eastern side are placed, overriding the node&amp;rsquo;s general port alignment.</description>
    </item>
    
    <item>
      <title>Port Alignment (North)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-north.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-north.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.portAlignment.north Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortAlignment (Enum) Possible Values: DISTRIBUTED
JUSTIFIED
BEGIN
CENTER
END Applies To: nodes Containing Group: portAlignment Description Defines how ports on the northern side are placed, overriding the node&amp;rsquo;s general port alignment.</description>
    </item>
    
    <item>
      <title>Port Alignment (South)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-south.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-south.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.portAlignment.south Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortAlignment (Enum) Possible Values: DISTRIBUTED
JUSTIFIED
BEGIN
CENTER
END Applies To: nodes Containing Group: portAlignment Description Defines how ports on the southern side are placed, overriding the node&amp;rsquo;s general port alignment.</description>
    </item>
    
    <item>
      <title>Port Alignment (West)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-west.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portAlignment-west.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.portAlignment.west Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortAlignment (Enum) Possible Values: DISTRIBUTED
JUSTIFIED
BEGIN
CENTER
END Applies To: nodes Containing Group: portAlignment Description Defines how ports on the western side are placed, overriding the node&amp;rsquo;s general port alignment.</description>
    </item>
    
    <item>
      <title>Port Anchor Offset</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-anchor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-anchor.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.port.anchor Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.KVector Applies To: ports Containing Group: port Description The offset to the port position where connections shall be attached.</description>
    </item>
    
    <item>
      <title>Port Border Offset</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-borderOffset.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-borderOffset.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.port.borderOffset Meta Data Provider: core.options.CoreOptions Value Type: double Applies To: ports Containing Group: port Description The offset of ports on the node border. With a positive offset the port is moved outside of the node, while with a negative offset the port is moved towards the inside. An offset of 0 means that the port is placed directly on the node border, i.e. if the port side is north, the port&amp;rsquo;s south border touches the nodes&amp;rsquo;s north border; if the port side is east, the port&amp;rsquo;s west border touches the nodes&amp;rsquo;s east border; if the port side is south, the port&amp;rsquo;s north border touches the node&amp;rsquo;s south border; if the port side is west, the port&amp;rsquo;s east border touches the node&amp;rsquo;s west border.</description>
    </item>
    
    <item>
      <title>Port Constraints</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portConstraints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portConstraints.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.portConstraints Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortConstraints (Enum) Possible Values: UNDEFINED
FREE
FIXED_SIDE
FIXED_ORDER
FIXED_RATIO
FIXED_POS Default Value: PortConstraints.UNDEFINED (as defined in org.eclipse.elk) Applies To: nodes Description Defines constraints of the position of the ports of a node.</description>
    </item>
    
    <item>
      <title>Port Direction Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-portDirectionPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-portDirectionPenalty.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.alg.libavoid.portDirectionPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied to port selection choice when the other end of the connector being routed does not appear in any of the 90 degree visibility cones centered on the visibility directions for the port.</description>
    </item>
    
    <item>
      <title>Port Index</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-index2.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-index2.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.port.index Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: ports Containing Group: port Description The index of a port in the fixed order around a node. The order is assumed as clockwise, starting with the leftmost port on the top side. This option must be set if &amp;lsquo;Port Constraints&amp;rsquo; is set to FIXED_ORDER and no specific positions are given for the ports. Additionally, the option &amp;lsquo;Port Side&amp;rsquo; must be defined in this case.</description>
    </item>
    
    <item>
      <title>Port Label Placement</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-placement.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-placement.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.portLabels.placement Meta Data Provider: core.options.CoreOptions Value Type: java.util.EnumSet&amp;lt;org.eclipse.elk.core.options.PortLabelPlacement&amp;gt; Possible Values: OUTSIDE
INSIDE
NEXT_TO_PORT_IF_POSSIBLE
ALWAYS_SAME_SIDE
ALWAYS_OTHER_SAME_SIDE
SPACE_EFFICIENT Default Value: PortLabelPlacement.outside() (as defined in org.eclipse.elk) Applies To: nodes Containing Group: portLabels Description Decides on a placement method for port labels; if empty, the node label&amp;rsquo;s position is not modified.</description>
    </item>
    
    <item>
      <title>Port Labels Next to Port</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-nextToPortIfPossible.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-nextToPortIfPossible.html</guid>
      <description>Property Value Type: deprecated Identifier: org.eclipse.elk.portLabels.nextToPortIfPossible Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: nodes Containing Group: portLabels Description Use &amp;lsquo;portLabels.placement&amp;rsquo;: NEXT_TO_PORT_OF_POSSIBLE.</description>
    </item>
    
    <item>
      <title>Port Side</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-side.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-port-side.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.port.side Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.PortSide (Enum) Possible Values: UNDEFINED
NORTH
EAST
SOUTH
WEST Default Value: PortSide.UNDEFINED (as defined in org.eclipse.elk) Applies To: ports Containing Group: port Description The side of a node on which a port is situated. This option must be set if &amp;lsquo;Port Constraints&amp;rsquo; is set to FIXED_SIDE or FIXED_ORDER and no specific positions are given for the ports.</description>
    </item>
    
    <item>
      <title>Port Sorting Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-portSortingStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-portSortingStrategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.portSortingStrategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.PortSortingStrategy (Enum) Possible Values: INPUT_ORDER
PORT_DEGREE Default Value: PortSortingStrategy.INPUT_ORDER (as defined in org.eclipse.elk.layered) Applies To: parents Description Only relevant for nodes with FIXED_SIDE port constraints. Determines the way a node&amp;rsquo;s ports are distributed on the sides of a node if their order is not prescribed. The option is set on parent nodes.</description>
    </item>
    
    <item>
      <title>Port Spacing</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-portPort.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-portPort.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.portPort Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 10 (as defined in org.eclipse.elk) Lower Bound: 0.0 Applies To: parents, nodes Containing Group: spacing Description Spacing between pairs of ports of the same node.</description>
    </item>
    
    <item>
      <title>portAlignment</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-portAlignment.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-portAlignment.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.portAlignment Options Port Alignment Port Alignment (North) Port Alignment (South) Port Alignment (West) Port Alignment (East) </description>
    </item>
    
    <item>
      <title>portLabels</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-portLabels.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-portLabels.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.portLabels Options Port Label Placement Port Labels Next to Port Treat Port Labels as Group </description>
    </item>
    
    <item>
      <title>Position</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-position.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-position.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.position Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.math.KVector Applies To: nodes, ports, labels Description The position of a node, port, or label. This is used by the &amp;lsquo;Fixed Layout&amp;rsquo; algorithm to specify a pre-defined position.</description>
    </item>
    
    <item>
      <title>Position Choice Constraint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-positionChoiceConstraint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-positionChoiceConstraint.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.positionChoiceConstraint Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.lang.Integer Default Value: null (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: nodes Containing Group: crossingMinimization Description Allows to set a constraint regarding the position placement of a node in a layer. Assumed the layer in which the node placed includes n other nodes and i &amp;lt; n. If set to i, it expresses that the node should be placed at the i-th position.</description>
    </item>
    
    <item>
      <title>Position Constraint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-compaction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-compaction.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.compaction Meta Data Provider: options.MrTreeMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.mrtree) Applies To: parents Description Turns on Tree compaction which decreases the size of the whole tree by placing nodes of multiple levels in one large level</description>
    </item>
    
    <item>
      <title>Position Constraint</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-positionConstraint.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-positionConstraint.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.positionConstraint Meta Data Provider: options.MrTreeMetaDataProvider Value Type: int Default Value: -1 (as defined in org.eclipse.elk.mrtree) Applies To: nodes Description When set to a positive number this option will force the algorithm to place the node to the specified position within the trees layer if weighting is set to constraint</description>
    </item>
    
    <item>
      <title>Position ID</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-positionId.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-positionId.html</guid>
      <description>Property Value Type: output Identifier: org.eclipse.elk.layered.crossingMinimization.positionId Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: -1 (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: nodes Containing Group: crossingMinimization Description Position within a layer that was determined by ELK Layered for a node. This is only generated if interactiveLayot or generatePositionAndLayerIds is set.</description>
    </item>
    
    <item>
      <title>Post Compaction Constraint Calculation</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-postCompaction-constraints.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-postCompaction-constraints.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.compaction.postCompaction.constraints Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.ConstraintCalculationStrategy (Enum) Possible Values: QUADRATIC
SCANLINE Default Value: ConstraintCalculationStrategy.SCANLINE (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: compaction -&amp;gt; postCompaction Description Specifies whether and how post-process compaction is applied.</description>
    </item>
    
    <item>
      <title>Post Compaction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-postCompaction-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-compaction-postCompaction-strategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.compaction.postCompaction.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.GraphCompactionStrategy (Enum) Possible Values: NONE
LEFT
RIGHT
LEFT_RIGHT_CONSTRAINT_LOCKING
LEFT_RIGHT_CONNECTION_LOCKING
EDGE_LENGTH Default Value: GraphCompactionStrategy.NONE (as defined in org.eclipse.elk.layered) Applies To: parents Containing Group: compaction -&amp;gt; postCompaction Description Specifies whether and how post-process compaction is applied.</description>
    </item>
    
    <item>
      <title>priority</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-priority.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-priority.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.priority Options Direction Priority Shortness Priority Straightness Priority </description>
    </item>
    
    <item>
      <title>Priority</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.priority Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: nodes, edges Description Defines the priority of an object; its meaning depends on the specific layout algorithm and the context where it is used.</description>
    </item>
    
    <item>
      <title>Priority (ELK Box)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-box.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-box.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.priority Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: nodes, edges Description Defines the priority of an object; its meaning depends on the specific layout algorithm and the context where it is used.
Additional Documentation Priorities set on nodes determine the order in which they are placed: boxes with a higher priority will end up before boxes with a lower priority. Boxes with equal priorities are sorted from smaller to bigger unless the layout algorithm is set to interactive mode.</description>
    </item>
    
    <item>
      <title>Priority (ELK Force)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-force.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-force.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.priority Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: nodes, edges Description Defines the priority of an object; its meaning depends on the specific layout algorithm and the context where it is used.
Additional Documentation Priorities set on nodes determine the order in which connected components are placed: components with a higher sum of node priorities will end up before components with a lower sum.</description>
    </item>
    
    <item>
      <title>Priority (ELK Layered)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-layered.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-layered.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.priority Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: nodes, edges Description Defines the priority of an object; its meaning depends on the specific layout algorithm and the context where it is used.
Additional Documentation Used by the &amp;lsquo;simple row graph placer&amp;rsquo; to decide which connected components to place first. A component&amp;rsquo;s priority is the sum of the node priorities, and components with higher priorities will be placed before components with lower priorities.</description>
    </item>
    
    <item>
      <title>Priority (ELK Mr. Tree)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-mrtree.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-priority_org-eclipse-elk-mrtree.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.priority Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: nodes, edges Description Defines the priority of an object; its meaning depends on the specific layout algorithm and the context where it is used.
Additional Documentation Priorities set on nodes determine the order in which connected components are placed: components with a higher sum of node priorities will end up before components with a lower sum.</description>
    </item>
    
    <item>
      <title>processingOrder</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-processingOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-processingOrder.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.processingOrder Options Tree Construction Strategy Cost Function for Spanning Tree Root node for spanning tree construction Root selection for spanning tree </description>
    </item>
    
    <item>
      <title>Progress Bar</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-progressBar.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-progressBar.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.progressBar Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether a progress bar shall be displayed during layout computations.</description>
    </item>
    
    <item>
      <title>Radius</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-radius.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-radius.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.radius Meta Data Provider: options.RadialMetaDataProvider Value Type: double Default Value: 0.0 (as defined in org.eclipse.elk.radial) Applies To: parents Description The radius option can be used to set the initial radius for the radial layouter.</description>
    </item>
    
    <item>
      <title>Random Graph Generation</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/randomgraphs.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/randomgraphs.html</guid>
      <description>If you need to quickly create random graphs in .elkt or .elkg format, you can use our random graph creation DSL. This way you can save, store, comment and diff your random graph configurations easily!
To get started, create a file with the file ending .elkr. Now Eclipse will offer the usual features you know and love, such as code completion, syntax highlighting and validation. So remember Eclipse rule number 1: Ctrl+Space is your friend!</description>
    </item>
    
    <item>
      <title>Randomization Seed</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-randomSeed.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-randomSeed.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.randomSeed Meta Data Provider: core.options.CoreOptions Value Type: int Applies To: parents Description Seed used for pseudo-random number generators to control the layout algorithm. If the value is 0, the seed shall be determined pseudo-randomly (e.g. from the system time).</description>
    </item>
    
    <item>
      <title>Rectpacking</title>
      <link>https://www.eclipse.org/elk/blog/posts/2022/22-08-31-rectpacking.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2022/22-08-31-rectpacking.html</guid>
      <description>By Sören Domrös, August 31, 2022
The rectpacking algorithm was introduced to solved common problems with the box algorithm, which cannot stack boxes in a row. The idea is to form stacks with subrows inside rows, while the size of a row is always dominated by a highest rectangle to provide a visual anchor point to &amp;ldquo;read&amp;rdquo; the rows from left to right.
Since it was a common use case of the box algorithm to add a priority to order the rectangles rectpacking uses the model order (which corresponds to the input order of the rectangles) as a criterion.</description>
    </item>
    
    <item>
      <title>Releases</title>
      <link>https://www.eclipse.org/elk/downloads/releasenotes.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/downloads/releasenotes.html</guid>
      <description>The release notes only list some of the highlights of each release. The downloads section has links that send you to more detailed information.</description>
    </item>
    
    <item>
      <title>Repulsive Power</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-repulsivePower.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-force-repulsivePower.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.force.repulsivePower Meta Data Provider: options.ForceMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.force) Lower Bound: 0 Applies To: edges Description Determines how many bend points are added to the edge; such bend points are regarded as repelling particles in the force model</description>
    </item>
    
    <item>
      <title>Resolved Layout Algorithm</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-resolvedAlgorithm.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-resolvedAlgorithm.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.resolvedAlgorithm Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.data.LayoutAlgorithmData Applies To: parents Description Meta data associated with the selected algorithm.</description>
    </item>
    
    <item>
      <title>Reverse Direction Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-reverseDirectionPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-reverseDirectionPenalty.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.reverseDirectionPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied whenever a connector path travels in the direction opposite of the destination from the source endpoint. By default this penalty is set to zero. This shouldn&amp;rsquo;t be needed in most cases but can be useful if you use penalties such as crossingPenalty which cause connectors to loop around obstacles.</description>
    </item>
    
    <item>
      <title>Root node for spanning tree construction</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-preferredRoot.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-preferredRoot.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.processingOrder.preferredRoot Meta Data Provider: options.SporeMetaDataProvider Value Type: java.lang.String Default Value: null (as defined in org.eclipse.elk) Applies To: parents Dependencies: org.eclipse.elk.processingOrder.rootSelection (RootSelection.FIXED) Containing Group: processingOrder Description The identifier of the node that is preferred as the root of the spanning tree. If this is null, the first node is chosen.</description>
    </item>
    
    <item>
      <title>Root selection for spanning tree</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-rootSelection.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-rootSelection.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.processingOrder.rootSelection Meta Data Provider: options.SporeMetaDataProvider Value Type: org.eclipse.elk.alg.spore.options.RootSelection (Enum) Possible Values: FIXED
CENTER_NODE Default Value: RootSelection.CENTER_NODE (as defined in org.eclipse.elk) Applies To: parents Containing Group: processingOrder Description This sets the method used to select a root node for the construction of a spanning tree</description>
    </item>
    
    <item>
      <title>Rotate</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotate.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotate.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.rotate Meta Data Provider: options.RadialMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.radial) Applies To: parents Description The rotate option determines whether a rotation of the layout should be performed.</description>
    </item>
    
    <item>
      <title>rotation</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-radial-rotation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-radial-rotation.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.radial.rotation Options Target Angle Additional Wedge Space Outgoing Edge Angles </description>
    </item>
    
    <item>
      <title>Row Height Reevaluation</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-compaction-rowHeightReevaluation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-packing-compaction-rowHeightReevaluation.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.packing.compaction.rowHeightReevaluation Meta Data Provider: options.RectPackingMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: packing -&amp;gt; compaction Description During the compaction step the height of a row is normally not changed. If this options is set, the blocks of other rows might be added if they exceed the row height. If this is the case the whole row has to be packed again to be optimal regarding the new row height.</description>
    </item>
    
    <item>
      <title>Scale Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-scaleFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-scaleFactor.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.scaleFactor Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: nodes Description The scaling factor to be applied to the corresponding node in recursive layout. It causes the corresponding node&amp;rsquo;s size to be adjusted, and its ports and labels to be sized and placed accordingly after the layout of that node has been determined (and before the node itself and its siblings are arranged).</description>
    </item>
    
    <item>
      <title>Search Order</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-searchOrder.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-searchOrder.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.searchOrder Meta Data Provider: options.MrTreeMetaDataProvider Value Type: org.eclipse.elk.alg.mrtree.options.TreeifyingOrder (Enum) Possible Values: DFS
BFS Default Value: TreeifyingOrder.DFS (as defined in org.eclipse.elk.mrtree) Applies To: parents Description Which search order to use when computing a spanning tree.</description>
    </item>
    
    <item>
      <title>Segment Penalty</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-segmentPenalty.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-segmentPenalty.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.segmentPenalty Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 10 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This penalty is applied for each segment in the connector path beyond the first. This should always normally be set when doing orthogonal routing to prevent step-like connector paths.</description>
    </item>
    
    <item>
      <title>Self-Loop Distribution</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopDistribution.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopDistribution.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.edgeRouting.selfLoopDistribution Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.SelfLoopDistributionStrategy (Enum) Possible Values: EQUALLY
NORTH
NORTH_SOUTH Default Value: SelfLoopDistributionStrategy.NORTH (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: edgeRouting Description Alter the distribution of the loops around the node. It only takes effect for PortConstraints.FREE.</description>
    </item>
    
    <item>
      <title>Self-Loop Ordering</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopOrdering.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopOrdering.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.edgeRouting.selfLoopOrdering Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.SelfLoopOrderingStrategy (Enum) Possible Values: STACKED
REVERSE_STACKED
SEQUENCED Default Value: SelfLoopOrderingStrategy.STACKED (as defined in org.eclipse.elk.layered) Applies To: nodes Containing Group: edgeRouting Description Alter the ordering of the loops they can either be stacked or sequenced. It only takes effect for PortConstraints.FREE.</description>
    </item>
    
    <item>
      <title>Semi-Interactive Crossing Minimization</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-semiInteractive.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-crossingMinimization-semiInteractive.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.crossingMinimization.semiInteractive Meta Data Provider: options.LayeredMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.crossingMinimization.strategy (CrossingMinimizationStrategy.LAYER_SWEEP) Containing Group: crossingMinimization Description Preserves the order of nodes within a layer but still minimizes crossings between edges connecting long edge dummies. Derives the desired order from positions specified by the &amp;lsquo;org.eclipse.elk.position&amp;rsquo; layout option. Requires a crossing minimization strategy that is able to process &amp;lsquo;in-layer&amp;rsquo; constraints.</description>
    </item>
    
    <item>
      <title>Separate Connected Components</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-separateConnectedComponents.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-separateConnectedComponents.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.separateConnectedComponents Meta Data Provider: core.options.CoreOptions Value Type: boolean Applies To: parents Description Whether each connected component should be processed separately.</description>
    </item>
    
    <item>
      <title>Shape Buffer Distance</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-shapeBufferDistance.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-alg-libavoid-shapeBufferDistance.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.alg.libavoid.shapeBufferDistance Meta Data Provider: options.LibavoidMetaDataProvider Value Type: double Default Value: 4 (as defined in org.eclipse.elk.alg.libavoid) Applies To: parents Description This parameter defines the spacing distance that will be added to the sides of each shape when determining obstacle sizes for routing. This controls how closely connectors pass shapes, and can be used to prevent connectors overlapping with shape boundaries.</description>
    </item>
    
    <item>
      <title>Shift Last Placed.</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-lastPlaceShift.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-lastPlaceShift.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.widthApproximation.lastPlaceShift Meta Data Provider: options.RectPackingMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: widthApproximation Description When placing a rectangle behind or below the last placed rectangle in the first iteration, it is sometimes possible to shift the rectangle further to the left or right, resulting in less whitespace. True (default) enables the shift and false disables it. Disabling the shift produces a greater approximated area by the first iteration and a layout, when using ONLY the first iteration (default not the case), where it is sometimes impossible to implement a size transformation of rectangles that will fill the bounding box and eliminate empty spaces.</description>
    </item>
    
    <item>
      <title>Shortness Priority</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-shortness.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-shortness.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.priority.shortness Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: edges Containing Group: priority Description Defines how important it is to keep an edge as short as possible. This option is evaluated during the layering phase.</description>
    </item>
    
    <item>
      <title>Shortness Priority (ELK Layered)</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-shortness_org-eclipse-elk-layered.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-shortness_org-eclipse-elk-layered.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.priority.shortness Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: edges Containing Group: priority Description Defines how important it is to keep an edge as short as possible. This option is evaluated during the layering phase.
Additional Documentation Currently only supported by the network simplex layerer.</description>
    </item>
    
    <item>
      <title>Sloped Edge Zone Width</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-polyline-slopedEdgeZoneWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-polyline-slopedEdgeZoneWidth.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.edgeRouting.polyline.slopedEdgeZoneWidth Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 2.0 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.edgeRouting (EdgeRouting.POLYLINE) Containing Group: edgeRouting -&amp;gt; polyline Description Width of the strip to the left and to the right of each layer where the polyline edge router is allowed to refrain from ensuring that edges are routed horizontally. This prevents awkward bend points for nodes that extent almost to the edge of their layer.</description>
    </item>
    
    <item>
      <title>Sloppy Spline Layer Spacing Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-splines-sloppy-layerSpacingFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-splines-sloppy-layerSpacingFactor.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.edgeRouting.splines.sloppy.layerSpacingFactor Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Default Value: 0.2 (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.edgeRouting (EdgeRouting.SPLINES), org.eclipse.elk.layered.edgeRouting.splines.mode (SplineRoutingMode.SLOPPY) Containing Group: edgeRouting -&amp;gt; splines -&amp;gt; sloppy Description Spacing factor for routing area between layers when using sloppy spline routing.</description>
    </item>
    
    <item>
      <title>Sorter</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-sorter.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-sorter.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.sorter Meta Data Provider: options.RadialMetaDataProvider Value Type: org.eclipse.elk.alg.radial.options.SortingStrategy (Enum) Possible Values: NONE
POLAR_COORDINATE (@AdvancedPropertyValue)
ID Default Value: SortingStrategy.NONE (as defined in org.eclipse.elk.radial) Applies To: parents Description Sort the nodes per radius according to the sorting algorithm. The strategies are none, by the given order id, or sorting them by polar coordinates.</description>
    </item>
    
    <item>
      <title>spacing</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-spacing.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-spacing.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.spacing Options Spacing Base Value Edge Node Between Layers Spacing Edge Edge Between Layer Spacing Node Node Between Layers Spacing </description>
    </item>
    
    <item>
      <title>spacing</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-spacing.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-spacing.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.spacing Options Comment Comment Spacing Comment Node Spacing Components Spacing Edge Spacing Edge Label Spacing Edge Node Spacing Label Spacing Label Node Spacing Horizontal spacing between Label and Port Vertical spacing between Label and Port Node Spacing Node Self Loop Spacing Port Spacing Individual Spacing Additional Port Space </description>
    </item>
    
    <item>
      <title>Spacing Base Value</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-baseValue.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-spacing-baseValue.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.spacing.baseValue Meta Data Provider: options.LayeredMetaDataProvider Value Type: double Lower Bound: 0.0 Applies To: parents Containing Group: spacing Description An optional base value for all other layout options of the &amp;lsquo;spacing&amp;rsquo; group. It can be used to conveniently alter the overall &amp;lsquo;spaciousness&amp;rsquo; of the drawing. Whenever an explicit value is set for the other layout options, this base value will have no effect. The base value is not inherited, i.</description>
    </item>
    
    <item>
      <title>Spacing Options</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html</guid>
      <description>Space-able elements As far as the ELK core is concerned, the following elements can have spacing:
Nodes Ports Labels Edges Comments Connected components The first three can be thought of as boxes with some space to be left around them. Edges are not boxes, and connected components may be more complex than simple boxes.
ELK Layered adds the concept of layers, which leads to a few additional spacing values as described later.</description>
    </item>
    
    <item>
      <title>Spline Routing Mode</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-splines-mode.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-edgeRouting-splines-mode.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.layered.edgeRouting.splines.mode Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.SplineRoutingMode (Enum) Possible Values: CONSERVATIVE
CONSERVATIVE_SOFT
SLOPPY Default Value: SplineRoutingMode.SLOPPY (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.edgeRouting (EdgeRouting.SPLINES) Containing Group: edgeRouting -&amp;gt; splines Description Specifies the way control points are assembled for each individual edge. CONSERVATIVE ensures that edges are properly routed around the nodes but feels rather orthogonal at times. SLOPPY uses fewer control points to obtain curvier edge routes but may result in edges overlapping nodes.</description>
    </item>
    
    <item>
      <title>Straightness Priority</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-straightness.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-priority-straightness.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.priority.straightness Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.layered) Lower Bound: 0 Applies To: edges Containing Group: priority Description Defines how important it is to keep an edge straight, i.e. aligned with one of the two axes. This option is evaluated during node placement.</description>
    </item>
    
    <item>
      <title>Stress Epsilon</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-epsilon.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-stress-epsilon.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.stress.epsilon Meta Data Provider: options.StressMetaDataProvider Value Type: double Default Value: 10e-4 (as defined in org.eclipse.elk.stress) Applies To: parents Description Termination criterion for the iterative process.</description>
    </item>
    
    <item>
      <title>structure</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-structure.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-structure.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.structure Options Structure Extraction Strategy </description>
    </item>
    
    <item>
      <title>Structure Extraction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-structure-structureExtractionStrategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-structure-structureExtractionStrategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.structure.structureExtractionStrategy Meta Data Provider: options.SporeMetaDataProvider Value Type: org.eclipse.elk.alg.spore.options.StructureExtractionStrategy (Enum) Possible Values: DELAUNAY_TRIANGULATION Default Value: StructureExtractionStrategy.DELAUNAY_TRIANGULATION (as defined in org.eclipse.elk) Applies To: parents Containing Group: structure Description This option defines what kind of triangulation or other partitioning of the plane is applied to the vertices.</description>
    </item>
    
    <item>
      <title>Structuring Algorithms</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html</guid>
      <description>Often enough, algorithms can be divided into a number of layout phases which, executed as a pipeline, compute a layout. If your algorithm falls into this category, read on; ELK provides things you might want to use.
Layout Phases and Layout Processors Structuring the algorithm into phases has several advantages. First, it effectively divides the big layout problem into simpler sub problems. Second, it allows the algorithm to be more flexible.</description>
    </item>
    
    <item>
      <title>Target Angle</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-targetAngle.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-rotation-targetAngle.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.rotation.targetAngle Meta Data Provider: options.RadialMetaDataProvider Value Type: double Default Value: 0 (as defined in org.eclipse.elk.radial) Applies To: parents Dependencies: org.eclipse.elk.radial.rotate Containing Group: rotation Description The angle in radians that the layout should be rotated to after layout.</description>
    </item>
    
    <item>
      <title>Target Width</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-targetWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-targetWidth.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.widthApproximation.targetWidth Meta Data Provider: options.RectPackingMetaDataProvider Value Type: double Default Value: -1 (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: widthApproximation Description Option to place the rectangles in the given target width instead of approximating the width using the desired aspect ratio. The padding is not included in this. Meaning a drawing will have width of targetwidth + horizontal padding.</description>
    </item>
    
    <item>
      <title>Thoroughness</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-thoroughness.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-thoroughness.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.thoroughness Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 7 (as defined in org.eclipse.elk.layered) Lower Bound: 1 Applies To: parents Description How much effort should be spent to produce a nice layout.</description>
    </item>
    
    <item>
      <title>Tool Developers</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers.html</guid>
      <description>This section of the documentation is meant for tool developers who simply want to add automatic layout capabilities to their tool. Despite of its name, the Eclipse Layout Kernel is not limited to Eclipse-based applications, but can also be used in pure Java applications and even in JavaScript applications (although this is not yet available in ELK itself).
Three Layers of Layout Goodness At its basic, ELK is structured in three layers, each adding more convenience functionality to lower layers:</description>
    </item>
    
    <item>
      <title>Top-down Layout: Zoom in the Layout Process</title>
      <link>https://www.eclipse.org/elk/blog/posts/2023/23-04-11-topdown-layout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/blog/posts/2023/23-04-11-topdown-layout.html</guid>
      <description>By Maximilian Kasperowski, June 9, 2023
The coming update (ELK 0.9.0) introduces a new approach to layout hierarchical graphs. Instead of increasing the size of parent nodes to fit their content we apply scaling to the content to make it fit the parent. In this post I will go over the new properties provided to achieve this and what kinds of output can be produced using top-down layout.
Scaling In addition to the existing data assigned to graph elements during layout such as positions, nodes can now also have the additional property org.</description>
    </item>
    
    <item>
      <title>topdown</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdown.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdown.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.topdown Options Topdown Scale Factor Topdown Size Approximator Topdown Hierarchical Node Width Topdown Hierarchical Node Aspect Ratio Topdown Node Type Topdown Scale Cap </description>
    </item>
    
    <item>
      <title>Topdown Hierarchical Node Aspect Ratio</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-hierarchicalNodeAspectRatio.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-hierarchicalNodeAspectRatio.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdown.hierarchicalNodeAspectRatio Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1.414 (as defined in org.eclipse.elk) Applies To: parents, nodes Dependencies: org.eclipse.elk.topdown.nodeType Containing Group: topdown Description The fixed aspect ratio of a hierarchical node when using topdown layout. Default is 1/sqrt(2). If this value is set on a parallel node it applies to its children, when set on a hierarchical node it applies to the node itself.</description>
    </item>
    
    <item>
      <title>Topdown Hierarchical Node Width</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-hierarchicalNodeWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-hierarchicalNodeWidth.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdown.hierarchicalNodeWidth Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 150 (as defined in org.eclipse.elk) Applies To: parents, nodes Dependencies: org.eclipse.elk.topdown.nodeType Containing Group: topdown Description The fixed size of a hierarchical node when using topdown layout. If this value is set on a parallel node it applies to its children, when set on a hierarchical node it applies to the node itself.</description>
    </item>
    
    <item>
      <title>Topdown Layout</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownLayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownLayout.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdownLayout Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Dependencies: org.eclipse.elk.topdown.nodeType Description Turns topdown layout on and off. If this option is enabled, hierarchical layout will be computed first for the root node and then for its children recursively. Layouts are then scaled down to fit the area provided by their parents. Graphs must follow a certain structure for topdown layout to work properly.</description>
    </item>
    
    <item>
      <title>Topdown Node Type</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-nodeType.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-nodeType.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdown.nodeType Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.TopdownNodeTypes (Enum) Possible Values: PARALLEL_NODE
HIERARCHICAL_NODE
ROOT_NODE Default Value: null (as defined in org.eclipse.elk) Applies To: nodes Dependencies: org.eclipse.elk.nodeSize.fixedGraphSize Containing Group: topdown Description The different node types used for topdown layout. If the node type is set to {@link TopdownNodeTypes.PARALLEL_NODE} the algorithm must be set to a {@link TopdownLayoutProvider} such as {@link TopdownPacking}. The {@link nodeSize.fixedGraphSize} option is technically only required for hierarchical nodes.</description>
    </item>
    
    <item>
      <title>Topdown Scale Cap</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-scaleCap.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-scaleCap.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdown.scaleCap Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Applies To: parents Dependencies: org.eclipse.elk.topdown.nodeType (TopdownNodeTypes.HIERARCHICAL_NODE) Containing Group: topdown Description Determines the upper limit for the topdown scale factor. The default value is 1.0 which ensures that nested children never end up appearing larger than their parents in terms of unit sizes such as the font size. If the limit is larger, nodes will fully utilize the available space, but it is counteriniuitive for inner nodes to have a larger scale than outer nodes.</description>
    </item>
    
    <item>
      <title>Topdown Scale Factor</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-scaleFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-scaleFactor.html</guid>
      <description>Property Value Type: programmatic Identifier: org.eclipse.elk.topdown.scaleFactor Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Lower Bound: ExclusiveBounds.greaterThan(0) Applies To: parents Dependencies: org.eclipse.elk.topdown.nodeType (TopdownNodeTypes.HIERARCHICAL_NODE) Containing Group: topdown Description The scaling factor to be applied to the nodes laid out within the node in recursive topdown layout. The difference to &amp;lsquo;Scale Factor&amp;rsquo; is that the node itself is not scaled. This value has to be set on hierarchical nodes.</description>
    </item>
    
    <item>
      <title>Topdown Size Approximator</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-sizeApproximator.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdown-sizeApproximator.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.topdown.sizeApproximator Meta Data Provider: core.options.CoreOptions Value Type: org.eclipse.elk.core.options.TopdownSizeApproximator (Enum) Possible Values: COUNT_CHILDREN
LOOKAHEAD_LAYOUT Default Value: null (as defined in org.eclipse.elk) Applies To: nodes Dependencies: org.eclipse.elk.topdown.nodeType (TopdownNodeTypes.HIERARCHICAL_NODE) Containing Group: topdown Description The size approximator to be used to set sizes of hierarchical nodes during topdown layout. The default value is null, which results in nodes keeping whatever size is defined for them e.g. through parent parallel node or by manually setting the size.</description>
    </item>
    
    <item>
      <title>Translation Optimization</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-optimizationCriteria.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-radial-optimizationCriteria.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.radial.optimizationCriteria Meta Data Provider: options.RadialMetaDataProvider Value Type: org.eclipse.elk.alg.radial.options.RadialTranslationStrategy (Enum) Possible Values: NONE
EDGE_LENGTH
EDGE_LENGTH_BY_POSITION (@AdvancedPropertyValue)
CROSSING_MINIMIZATION_BY_POSITION Default Value: RadialTranslationStrategy.NONE (as defined in org.eclipse.elk.radial) Applies To: parents Description Find the optimal translation of the nodes of the first radii according to this criteria. For example edge crossings can be minimized.</description>
    </item>
    
    <item>
      <title>Treat Port Labels as Group</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-treatAsGroup.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-portLabels-treatAsGroup.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.portLabels.treatAsGroup Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: true (as defined in org.eclipse.elk) Applies To: nodes Containing Group: portLabels Description If this option is true (default), the labels of a port will be treated as a group when it comes to centering them next to their port. If this option is false, only the first label will be centered next to the port, with the others being placed below.</description>
    </item>
    
    <item>
      <title>Tree Construction Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-treeConstruction.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-processingOrder-treeConstruction.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.processingOrder.treeConstruction Meta Data Provider: options.SporeMetaDataProvider Value Type: org.eclipse.elk.alg.spore.options.TreeConstructionStrategy (Enum) Possible Values: MINIMUM_SPANNING_TREE
MAXIMUM_SPANNING_TREE Default Value: TreeConstructionStrategy.MINIMUM_SPANNING_TREE (as defined in org.eclipse.elk) Applies To: parents Containing Group: processingOrder Description Whether a minimum spanning tree or a maximum spanning tree should be constructed.</description>
    </item>
    
    <item>
      <title>Tree Level</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-treeLevel.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-treeLevel.html</guid>
      <description>Property Value Type: output Identifier: org.eclipse.elk.mrtree.treeLevel Meta Data Provider: options.MrTreeMetaDataProvider Value Type: int Default Value: 0 (as defined in org.eclipse.elk.mrtree) Lower Bound: 0 Applies To: nodes Description The index for the tree level the node is in</description>
    </item>
    
    <item>
      <title>Try box layout first</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-trybox.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-trybox.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.rectpacking.trybox Meta Data Provider: options.RectPackingMetaDataProvider Value Type: boolean Default Value: false (as defined in org.eclipse.elk.rectpacking) Applies To: parents Description Whether one should check whether the regions are stackable to see whether box layout would do the job. For example, nodes with the same height are not stackable inside a row. Therefore, box layout will perform better and faster.</description>
    </item>
    
    <item>
      <title>Underlying Layout Algorithm</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-underlyingLayoutAlgorithm.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-underlyingLayoutAlgorithm.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.underlyingLayoutAlgorithm Meta Data Provider: options.SporeMetaDataProvider Value Type: java.lang.String Applies To: parents Description A layout algorithm that is applied to the graph before it is compacted. If this is null, nothing is applied before compaction.</description>
    </item>
    
    <item>
      <title>Unit Tests</title>
      <link>https://www.eclipse.org/elk/documentation/algorithmdevelopers/unittesting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/algorithmdevelopers/unittesting.html</guid>
      <description>Layout algorithms are complex pieces of software and, thus, should probably be tested. Besides the usual plain JUnit tests, ELK provides a graph algorithm unit test framework based on JUnit 4. Tests written with that framework basically do three things:
Load one or more graphs.
Optionally provide a number of graph configurations. If a test class doesn&amp;rsquo;t specify configurations, a default configuration will be activated.
Run one ore more tests on each graph.</description>
    </item>
    
    <item>
      <title>Upper Bound On Width [MinWidth Layerer]</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-minWidth-upperBoundOnWidth.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-minWidth-upperBoundOnWidth.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.minWidth.upperBoundOnWidth Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 4 (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: parents Dependencies: org.eclipse.elk.layered.layering.strategy (LayeringStrategy.MIN_WIDTH) Containing Group: layering -&amp;gt; minWidth Description Defines a loose upper bound on the width of the MinWidth layerer. If set to &amp;lsquo;-1&amp;rsquo; multiple values are tested and the best result is selected.</description>
    </item>
    
    <item>
      <title>Upper Layer Estimation Scaling Factor [MinWidth Layerer]</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-minWidth-upperLayerEstimationScalingFactor.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-layering-minWidth-upperLayerEstimationScalingFactor.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.layering.minWidth.upperLayerEstimationScalingFactor Meta Data Provider: options.LayeredMetaDataProvider Value Type: int Default Value: 2 (as defined in org.eclipse.elk.layered) Lower Bound: -1 Applies To: parents Dependencies: org.eclipse.elk.layered.layering.strategy (LayeringStrategy.MIN_WIDTH) Containing Group: layering -&amp;gt; minWidth Description Multiplied with Upper Bound On Width for defining an upper bound on the width of layers which haven&amp;rsquo;t been determined yet, but whose maximum width had been (roughly) estimated by the MinWidth algorithm. Compensates for too high estimations.</description>
    </item>
    
    <item>
      <title>Upper limit for iterations of overlap removal</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-overlapRemoval-maxIterations.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-overlapRemoval-maxIterations.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.overlapRemoval.maxIterations Meta Data Provider: options.SporeMetaDataProvider Value Type: int Default Value: 64 (as defined in org.eclipse.elk) Applies To: parents Containing Group: overlapRemoval </description>
    </item>
    
    <item>
      <title>Using Algorithms Directly</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingalgorithmsdirectly.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingalgorithmsdirectly.html</guid>
      <description>All layout algorithms implemented in the Eclipse Layout Kernel can be called directly. To do so, you must have an instance of our ElkGraph data structure that can be fed to the layout algorithm, which will then compute a layout for the graph&amp;rsquo;s elements.
Warning: This is very low-level stuff. If you want to stay in the pure-Java domain (as opposed to building an Eclipse-based application), you will probably want to use the next layer of abstraction.</description>
    </item>
    
    <item>
      <title>Using Eclipse Layout</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingeclipselayout.html</guid>
      <description>Compared to the two more basic variants, using the Eclipse Layout Kernel within Eclipse introduces the largest amount of general magic to the process. This page explains the basics of how the layout kernel performs automatic layouts. Based on these information, sub-pages then describe how to integrate your tool with the layout kernel by filling in the details the overview decided to best leave out.
How Layout Works in Eclipse Layout in Eclipse revolves around the DiagramLayoutEngine, which in turn builds upon the IGraphLayoutEngine interface that layout in pure Java revolves around.</description>
    </item>
    
    <item>
      <title>Using Plain Java Layout</title>
      <link>https://www.eclipse.org/elk/documentation/tooldevelopers/usingplainjavalayout.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/documentation/tooldevelopers/usingplainjavalayout.html</guid>
      <description>While layout algorithms can be called directly, it is usually a better idea not to do so except in the simplest of cases. There are several reasons for that:
When calling a layout algorithm directly, your code is hardwired to that implementation and looses a bit of flexibility.
Most importantly, as graphs get more complex there are more and more details to be aware of when executing layout. This is especially true for compound graphs: graphs with nodes that contain further graphs themselves.</description>
    </item>
    
    <item>
      <title>Valid Indices for Wrapping</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-validify-forbiddenIndices.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-validify-forbiddenIndices.html</guid>
      <description> Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.validify.forbiddenIndices Meta Data Provider: options.LayeredMetaDataProvider Value Type: java.util.List&amp;lt;java.lang.Integer&amp;gt; Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.SINGLE_EDGE), org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping -&amp;gt; validify </description>
    </item>
    
    <item>
      <title>Validate Graph</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-validateGraph.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-validateGraph.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.validateGraph Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether the graph shall be validated before any layout algorithm is applied. If this option is enabled and at least one error is found, the layout process is aborted and a message is shown to the user.</description>
    </item>
    
    <item>
      <title>Validate Options</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-validateOptions.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-validateOptions.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.validateOptions Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: true (as defined in org.eclipse.elk) Applies To: parents Description Whether layout options shall be validated before any layout algorithm is applied. If this option is enabled and at least one error is found, the layout process is aborted and a message is shown to the user.</description>
    </item>
    
    <item>
      <title>Validification Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-validify-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-layered-wrapping-validify-strategy.html</guid>
      <description>Property Value Type: advanced Identifier: org.eclipse.elk.layered.wrapping.validify.strategy Meta Data Provider: options.LayeredMetaDataProvider Value Type: org.eclipse.elk.alg.layered.options.ValidifyStrategy (Enum) Possible Values: NO
GREEDY
LOOK_BACK Default Value: ValidifyStrategy.GREEDY (as defined in org.eclipse.elk.layered) Applies To: parents Dependencies: org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.SINGLE_EDGE), org.eclipse.elk.layered.wrapping.strategy (WrappingStrategy.MULTI_EDGE) Containing Group: wrapping -&amp;gt; validify Description When wrapping graphs, one can specify indices that are not allowed as split points. The validification strategy makes sure every computed split point is allowed.</description>
    </item>
    
    <item>
      <title>Vertical spacing between Label and Port</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelPortVertical.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-spacing-labelPortVertical.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.spacing.labelPortVertical Meta Data Provider: core.options.CoreOptions Value Type: double Default Value: 1 (as defined in org.eclipse.elk) Applies To: parents Containing Group: spacing Description Vertical spacing to be preserved between labels and the ports they are associated with. Note that the placement of a label is influenced by the &amp;lsquo;portlabels.placement&amp;rsquo; option.</description>
    </item>
    
    <item>
      <title>Weighting of Nodes</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-weighting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-mrtree-weighting.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.mrtree.weighting Meta Data Provider: options.MrTreeMetaDataProvider Value Type: org.eclipse.elk.alg.mrtree.options.OrderWeighting (Enum) Possible Values: MODEL_ORDER
DESCENDANTS
FAN
CONSTRAINT Default Value: OrderWeighting.MODEL_ORDER (as defined in org.eclipse.elk.mrtree) Applies To: parents Description Which weighting to use when computing a node order.</description>
    </item>
    
    <item>
      <title>Whether to run a supplementary scanline overlap check.</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-overlapRemoval-runScanline.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-overlapRemoval-runScanline.html</guid>
      <description> Property Value Type: advanced Identifier: org.eclipse.elk.overlapRemoval.runScanline Meta Data Provider: options.SporeMetaDataProvider Value Type: boolean Default Value: true (as defined in org.eclipse.elk) Applies To: parents Containing Group: overlapRemoval </description>
    </item>
    
    <item>
      <title>White Space Approximation Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-whiteSpaceElimination-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-whiteSpaceElimination-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.whiteSpaceElimination.strategy Meta Data Provider: options.RectPackingMetaDataProvider Value Type: org.eclipse.elk.alg.rectpacking.p3whitespaceelimination.WhiteSpaceEliminationStrategy (Enum) Possible Values: EQUAL_BETWEEN_STRUCTURES
TO_ASPECT_RATIO Applies To: parents Containing Group: whiteSpaceElimination Description Strategy for expanding nodes such that whitespace in the parent is eliminated.</description>
    </item>
    
    <item>
      <title>Whitespace elimination strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownpacking-whitespaceElimination-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-topdownpacking-whitespaceElimination-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.topdownpacking.whitespaceElimination.strategy Meta Data Provider: options.TopdownpackingMetaDataProvider Value Type: org.eclipse.elk.alg.topdownpacking.WhitespaceEliminationStrategy (Enum) Possible Values: BOTTOM_ROW_EQUAL_WHITESPACE_ELIMINATOR Default Value: WhitespaceEliminationStrategy.BOTTOM_ROW_EQUAL_WHITESPACE_ELIMINATOR (as defined in org.eclipse.elk.topdownpacking) Applies To: parents Containing Group: whitespaceElimination Description Strategy for whitespace elimination.</description>
    </item>
    
    <item>
      <title>whitespaceElimination</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdownpacking-whitespaceElimination.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-topdownpacking-whitespaceElimination.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.topdownpacking.whitespaceElimination Options Whitespace elimination strategy </description>
    </item>
    
    <item>
      <title>whiteSpaceElimination</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-whiteSpaceElimination.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-whiteSpaceElimination.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.rectpacking.whiteSpaceElimination Options White Space Approximation Strategy </description>
    </item>
    
    <item>
      <title>Width Approximation Strategy</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-strategy.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-rectpacking-widthApproximation-strategy.html</guid>
      <description>Property Value Identifier: org.eclipse.elk.rectpacking.widthApproximation.strategy Meta Data Provider: options.RectPackingMetaDataProvider Value Type: org.eclipse.elk.alg.rectpacking.p1widthapproximation.WidthApproximationStrategy (Enum) Possible Values: GREEDY
TARGET_WIDTH Default Value: WidthApproximationStrategy.GREEDY (as defined in org.eclipse.elk.rectpacking) Applies To: parents Containing Group: widthApproximation Description Strategy for finding an initial width of the drawing.</description>
    </item>
    
    <item>
      <title>widthApproximation</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-widthApproximation.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-rectpacking-widthApproximation.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.rectpacking.widthApproximation Options Width Approximation Strategy Target Width Optimization Goal Shift Last Placed. </description>
    </item>
    
    <item>
      <title>wrapping</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping Options Graph Wrapping Strategy Additional Wrapped Edges Spacing Correction Factor for Wrapping Subgroups cutting validify singleEdge multiEdge </description>
    </item>
    
    <item>
      <title>wrapping.cutting</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-cutting.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-cutting.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping.cutting Options Cutting Strategy Manually Specified Cuts Subgroups msd </description>
    </item>
    
    <item>
      <title>wrapping.cutting.msd</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-cutting-msd.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-cutting-msd.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping.cutting.msd Options MSD Freedom </description>
    </item>
    
    <item>
      <title>wrapping.multiEdge</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-multiEdge.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-multiEdge.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping.multiEdge Options Improve Cuts Distance Penalty When Improving Cuts Improve Wrapped Edges </description>
    </item>
    
    <item>
      <title>wrapping.singleEdge</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-singleEdge.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-singleEdge.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping.singleEdge </description>
    </item>
    
    <item>
      <title>wrapping.validify</title>
      <link>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-validify.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/groups/org-eclipse-elk-layered-wrapping-validify.html</guid>
      <description> Property Value Identifier: org.eclipse.elk.layered.wrapping.validify Options Validification Strategy Valid Indices for Wrapping </description>
    </item>
    
    <item>
      <title>Zoom to Fit</title>
      <link>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-zoomToFit.html</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      
      <guid>https://www.eclipse.org/elk/reference/options/org-eclipse-elk-zoomToFit.html</guid>
      <description>Property Value Type: global Identifier: org.eclipse.elk.zoomToFit Meta Data Provider: core.options.CoreOptions Value Type: boolean Default Value: false (as defined in org.eclipse.elk) Applies To: parents Description Whether the zoom level shall be set to view the whole diagram after layout.</description>
    </item>
    
  </channel>
</rss>
