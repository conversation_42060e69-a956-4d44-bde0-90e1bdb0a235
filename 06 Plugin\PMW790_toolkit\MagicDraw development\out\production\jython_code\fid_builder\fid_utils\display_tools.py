# -*- coding: utf-8 -*-
from fid_utils.md_utils import printer, isBlock, isPartProperty
from fid_services.datatools import sort_by_host
from fid_services.finders import find_and_delete_existing_diagram, get_host_asset, get_host_location, get_tag
from fid_core.imports import *

from com.nomagic.magicdraw.openapi.uml import PresentationElementsManager
from com.nomagic.magicdraw.properties import ChoiceProperty, PropertyID, PropertyManager;
from com.nomagic.magicdraw.uml.symbols.shapes import PartView
from com.nomagic.magicdraw.uml.symbols.shapes import ShapeElement, DiagramPropertiesShape
from java.awt import Rectangle, Color, Point
from com.nomagic.magicdraw.sysml.util import SysMLConstants
from java.text import SimpleDateFormat
from java.util import Date
from com.nomagic.magicdraw.uml.symbols.layout.orderedhier import OrderedHierarchicDiagramLayouter
from com.nomagic.magicdraw.core.options import HierarchicLayouterOptionsGroup
from com.nomagic.magicdraw.uml.symbols import CompartmentManager, CompartmentID;

def create_timestamped_diagram(diagram_type, owner_element, name_prefix):
    """
    Creates a diagram with timestamped name to avoid conflicts
    
    :param diagram_type: The type of diagram to create
    :param owner_element: The element that will own the diagram
    :param name_prefix: The prefix for the diagram name
    :return: The created diagram element
    """
    new_diagram = mem.getInstance().createDiagram(diagram_type, owner_element)
    
    timestamp_format = SimpleDateFormat("yyyyMMdd_HHmmss")
    timestamp = timestamp_format.format(Date())
    owner_name = owner_element.getName()
    diagram_name = owner_name + "_" + name_prefix + " " + timestamp
    
    new_diagram.setName(diagram_name)
    return new_diagram

def hideCompartmentInfo(symbol, tag_to_hide):
    if tag_to_hide is not None:
        CompartmentManager.hideCompartmentElement(symbol, CompartmentID.TAGGED_VALUES_ON_SHAPE, tag_to_hide)

def showCompartmentInfo(symbol, tag_to_show):
    CompartmentManager.showCompartmentElement(symbol, CompartmentID.TAGGED_VALUES_ON_SHAPE, tag_to_show)

def collect_all_pes_with_leaf_check(pe, seen, level):
    result = []
    subtree = []

    def recurse(pe, level):
        if pe in seen:
            return
        seen.add(pe)

        if pe.isVisible() and isinstance(pe, PartView):
            subtree.append((pe, level))

        for child in pe.getPresentationElements():
            next_level = level + 1 if isinstance(pe, PartView) else level
            recurse(child, next_level)

    # Traverse one root tree
    recurse(pe, 0)

    def is_leaf(pv):
        def has_child_partview_recursive(pe):
            for child in pe.getPresentationElements():
                if isinstance(child, PartView) and child.isVisible():
                    return True
                if has_child_partview_recursive(child):
                    return True
            return False

        return not has_child_partview_recursive(pv)
    
    for pe, lvl in subtree:
        result.append({
            "PE": pe,
            "Level": lvl,
            "Max_Depth": is_leaf(pe)
        })

    return result

def remove_classifier_from_presentation_element(presentationElement):
    o_prop = presentationElement.getProperty("SHOW_OBJECT_CLASS")
    c_prop = o_prop.clone()
    c_prop.setValue(False)
    pm = PropertyManager()
    pm.addProperty(c_prop)
    PresentationElementsManager.getInstance().setPresentationElementProperties(presentationElement,pm)

def addToFID(element, diagram, level, showClasses, showPorts, topLevel, connectors, connectedElements):
    if topLevel:
        for port in element.getOwnedPort():
            if port in connectedElements:
                df = diagram.getDiagramFrame()
                port_pe = PresentationElementsManager.getInstance().createShapeElement(port, df, connectors)
                
                # Set port properties - hide stereotypes like connectors
                pm_port = PropertyManager()
                show_stereotype = port_pe.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                if show_stereotype:
                    new_show_stereotype = show_stereotype.clone()
                    new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                    pm_port.addProperty(new_show_stereotype)
                
                if pm_port.getProperties():
                    PresentationElementsManager.getInstance().setPresentationElementProperties(port_pe, pm_port)

    for part in element.getOwnedAttribute():
        if part not in connectedElements:
            continue
        if isPartProperty(part):
            if level >= 1:
                outer = PresentationElementsManager.getInstance().createShapeElement(part, diagram, connectors)
                if not showClasses:
                    if part.getType().getGeneral():
                        classifierName = part.getType().getGeneral().get(0).getName()
                        ###needs to be updated to support req
                        if classifierName in ["Transceiver","Cable","Connector"]:
                            remove_classifier_from_presentation_element(outer)
                if showPorts:
                    if part.getType().getOwnedPort():
                        for port in part.getType().getOwnedPort():
                                if port in connectedElements:
                                    out = PresentationElementsManager.getInstance().createShapeElement(port, outer, connectors)
                                    
                                    # Set port properties - hide stereotypes like connectors
                                    pm_port = PropertyManager()
                                    show_stereotype = out.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                                    if show_stereotype:
                                        new_show_stereotype = show_stereotype.clone()
                                        new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                                        pm_port.addProperty(new_show_stereotype)
                                    
                                    if pm_port.getProperties():
                                        PresentationElementsManager.getInstance().setPresentationElementProperties(out, pm_port)
                if level > 1:
                    addToFID(part.getType(), outer, level - 1, showClasses, showPorts, False, connectors, connectedElements)
            else:
                printer("Selected element is not supported, please try another.")

def find_shape_element_for(model_element, diagram_presentation_element):
    """Find the presentation element for a given model element within a diagram.
    
    Parameters
    ----------
    model_element : Element
        The model element to find
    diagram_presentation_element : PresentationElement
        The diagram to search within
        
    Returns
    -------
    PresentationElement or None
        The presentation element if found, None otherwise
    """
    def recurse(pes):
        for pe in pes:
            if pe.getElement() == model_element:
                return pe
            nested_result = recurse(pe.getPresentationElements())
            if nested_result:
                return nested_result
        return None

    return recurse(diagram_presentation_element.getPresentationElements())

def fidCreator_v1(selectedElement, level_var, disp_cons_list, model_parts, show_ports):
    """
    Gather all information required to build an IBD without creating/rendering the diagram.
    Returns a spec dict containing owner, depth, parts to display, deduped connector endpoints, and flags.
    """
    if not isBlock(selectedElement):
        printer('ERROR: Please select a block element')
        return None

    # Deduplicate and collect connector endpoints for later creation
    connector_data = []
    seen_connectors = set()
    for connector in disp_cons_list:
        if connector in seen_connectors:
            continue
        seen_connectors.add(connector)
        ends = connector.getEnd()
        if len(ends) != 2:
            continue
        source_role = ends[0].getRole()
        target_role = ends[1].getRole()
        connector_data.append((connector, source_role, target_role))

    spec = {
        "owner": selectedElement,
        "depth": level_var,
        "display_parts": model_parts,
        "show_ports": show_ports,
        "connector_data": connector_data,
        "diagram_type": SysMLConstants.SYSML_INTERNAL_BLOCK_DIAGRAM,
        "name_prefix": "FID_L2",
    }
    return spec

def create_ibd_diagram_from_spec(spec):
    """
    Create the IBD diagram and render part/port shapes according to the provided spec.
    Does NOT create connectors and does NOT open the diagram. Returns the diagram presentation element.
    """
    project = Application.getInstance().getProject()
    owner = spec["owner"]
    depth = spec["depth"]
    show_ports = spec["show_ports"]
    display_parts = spec["display_parts"]

    sm.getInstance().createSession(project, 'Create IBD From Spec')
    try:
        # Ensure previous FID_L2 for this owner is removed
        find_and_delete_existing_diagram(project, owner, spec["diagram_type"], spec["name_prefix"])

        # Create new timestamped diagram
        new_diagram = create_timestamped_diagram(spec["diagram_type"], owner, spec["name_prefix"])
        ibd_ = project.getDiagram(new_diagram)

        # Add shapes (parts/ports) only
        addToFID(owner, ibd_, depth, False, show_ports, True, False, display_parts)

        # Style adjustments for leaves and top-levels (moved from original fidCreator_v1)
        seen = set()
        all_pes = []
        diagram_prop_pe = None
        for pe in ibd_.getPresentationElements():
            if isinstance(pe, PartView) and pe not in seen:
                all_pes.extend(collect_all_pes_with_leaf_check(pe, seen, 0))
            if isinstance(pe, DiagramPropertiesShape):
                diagram_prop_pe = pe

        for sel in all_pes:
            p_e = sel["PE"]
            class_view = p_e.getProperty(PropertyID.SHOW_OBJECT_CLASS)
            if class_view:
                no_cv = class_view.clone()
                no_cv.setValue("False")
                pm = PropertyManager()
                pm.addProperty(no_cv)
            else:
                pm = PropertyManager()

            if sel["Max_Depth"] == True:
                change_el = sel["PE"]
                bounds = change_el.getBounds()
                new_bounds = Rectangle(bounds.x, bounds.y, 200, 100)
                vis_st_disp = change_el.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                vis_struct = change_el.getProperty(PropertyID.SUPPRESS_STRUCTURE)
                if vis_st_disp.getValue() != "STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE":
                    new_vis = vis_st_disp.clone()
                    new_vis.setValue("STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE")
                    new_vis_2 = vis_struct.clone()
                    new_vis_2.setValue("True")
                    pm.addProperty(new_vis)
                    pm.addProperty(new_vis_2)
                    PresentationElementsManager.getInstance().setPresentationElementProperties(change_el, pm)
                    PresentationElementsManager.getInstance().reshapeShapeElement(change_el, new_bounds)
            else:
                same_el = sel["PE"]
                fill_color = same_el.getProperty(PropertyID.FILL_COLOR)
                if fill_color.getValue() != Color(255, 255, 255):
                    fc = fill_color.clone()
                    fc.setValue(Color(255, 255, 255))
                    pm.addProperty(fc)
                if sel["Level"] == 0:
                    border_color = same_el.getProperty(PropertyID.PEN_COLOR)
                    if border_color.getValue() != Color(0, 0, 255):
                        bc = border_color.clone()
                        bc.setValue(Color(0, 0, 255))
                        pm.addProperty(bc)
                if pm.getProperties():
                    PresentationElementsManager.getInstance().setPresentationElementProperties(same_el, pm)

        return ibd_
    finally:
        sm.getInstance().closeSession()


def group_elements_in_diagram(selected_ibd, systemCabinetMap=None):
    """
    Perform cabinet-based grouping and placement of part shapes within the provided diagram.
    This function does NOT apply ELK and does NOT (re)create connectors.
    """
    printer("DEBUG: Starting group_elements_in_diagram")
    part_props = {}
    diagram_size = selected_ibd.getBounds()

    # Build part properties mapping from current diagram shapes
    for pe in selected_ibd.getPresentationElements():
        if pe.getHumanType() == "Part Property":
            part_props[pe] = {
                "name": pe.getElement().getName(),
                "presentation_element": pe,
                "element": pe.getElement(),
                "host_asset": get_host_asset(pe)
            }

    printer("DEBUG: Found {} part properties in diagram".format(len(part_props)))
    printer("DEBUG: systemCabinetMap available: {}".format(systemCabinetMap is not None))

    try:
        if systemCabinetMap and len(part_props) > 0:
            printer("DEBUG: Using cabinet-based grouping with systemCabinetMap")
            validated_props = validate_grouping_with_cache(part_props, systemCabinetMap)
            printer("DEBUG: Validated {} properties".format(len(validated_props)))
            hierarchy = create_cabinet_hierarchy_grouping(validated_props)
            printer("DEBUG: Created hierarchy with {} systems".format(len(hierarchy)))
            geolocate_elements_hierarchical(hierarchy, diagram_size, selected_ibd)
            printer("DEBUG: Completed hierarchical geolocation")
        else:
            printer("DEBUG: Using basic host_asset grouping (fallback)")
            basic_part_props = {}
            for pe, prop_data in part_props.items():
                basic_part_props[pe] = {
                    "pe": pe,
                    "name": pe.getName(),
                    "bounds": pe.getBounds(),
                    "x": pe.getBounds().getX(),
                    "y": pe.getBounds().getY(),
                    "width": pe.getBounds().getWidth(),
                    "height": pe.getBounds().getHeight(),
                    "host_asset": prop_data["host_asset"]
                }
            chunked = sort_by_host(basic_part_props)
            printer("DEBUG: Created {} chunks for basic grouping".format(len(chunked)))
            geolocate_(chunked, diagram_size, selected_ibd)
            printer("DEBUG: Completed basic geolocation")
    except Exception as e:
        printer("ERROR: Exception in group_elements_in_diagram: {}".format(str(e)))
        import traceback
        printer("ERROR: Traceback: {}".format(traceback.format_exc()))
        # Fallback to original grouping on error
        basic_part_props = {}
        for pe, prop_data in part_props.items():
            basic_part_props[pe] = {
                "pe": pe,
                "name": pe.getName(),
                "bounds": pe.getBounds(),
                "x": pe.getBounds().getX(),
                "y": pe.getBounds().getY(),
                "width": pe.getBounds().getWidth(),
                "height": pe.getBounds().getHeight(),
                "host_asset": prop_data["host_asset"]
            }
        chunked = sort_by_host(basic_part_props)
        geolocate_(chunked, diagram_size, selected_ibd)
        printer("DEBUG: Completed fallback grouping")

    printer("DEBUG: Finished group_elements_in_diagram")

def create_connectors_after_rearrangement(ibd_, connector_data):
    """Create connectors after diagram rearrangement is complete"""
    if not connector_data:
        return
    
    project = Application.getInstance().getProject()
    sm.getInstance().createSession(project, 'Add Connectors After Rearrangement')
    
    try:
        # Check if connectors already exist in diagram to prevent duplication
        existing_connector_elements = set()
        for pe in ibd_.getPresentationElements():
            if pe.getHumanType() == "Connector":
                existing_connector_elements.add(pe.getElement())
        
        for i, (connector, source_role, target_role) in enumerate(connector_data):
            # Skip if connector presentation already exists in diagram
            if connector in existing_connector_elements:
                continue
                
            source_shape = find_shape_element_for(source_role, ibd_)
            target_shape = find_shape_element_for(target_role, ibd_)

            if source_shape and target_shape:
                try:
                    nc = PresentationElementsManager.getInstance().createPathElement(connector, source_shape, target_shape)
                    
                    tag_prop = get_tag(nc.getElement(), "connectorMetaName")
                    hideCompartmentInfo(nc, tag_prop)
                    
                    # Set connector properties
                    pm_connector = PropertyManager()
                    show_name = nc.getProperty(PropertyID.SHOW_NAME)
                    if show_name:
                        new_show_name = show_name.clone()
                        new_show_name.setValue(True)
                        pm_connector.addProperty(new_show_name)
                    
                    # Hide stereotypes - set to "Do Not Display"
                    show_stereotype = nc.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                    if show_stereotype:
                        new_show_stereotype = show_stereotype.clone()
                        new_show_stereotype.setValue("STEREOTYPE_DISPLAY_MODE_DO_NOT_DISPLAY_STEREOTYPES")
                        pm_connector.addProperty(new_show_stereotype)
                    
                    # Apply properties
                    if pm_connector.getProperties():
                        PresentationElementsManager.getInstance().setPresentationElementProperties(nc, pm_connector)
                        
                except Exception as e:
                    printer("Error creating connector: {}".format(str(e)))
                    
        sm.getInstance().closeSession()
        
    except Exception as e:
        printer("Error in create_connectors_after_rearrangement: {}".format(str(e)))
        sm.getInstance().closeSession()
        
    except Exception as e:
        printer("Error in create_connectors_after_rearrangement: {0}".format(str(e)))
        sm.getInstance().closeSession()

# ====== CABINET-BASED GROUPING FUNCTIONS ======
# Integrated from fid_rearrange_demo.py for enhanced cabinet/parent hierarchy grouping

def get_cached_cabinet_properties(systemCabinetMap):
    """
    Extract all part properties from cached cabinet data in systemCabinetMap.
    Uses two-pass lookup: named systems first, then "Unknown" system.
    Returns tuple: (named_cabinet_properties, unknown_cabinet_properties)
    """
    named_cabinet_properties = {}
    unknown_cabinet_properties = {}
    
    if not systemCabinetMap:
        printer("systemCabinetMap not available, falling back to basic grouping")
        return named_cabinet_properties, unknown_cabinet_properties
    
    try:
        # Import Java utilities for cabinet properties access
        from com.pmw790.functions import Utilities
        
        for system_name, system_cabinets in systemCabinetMap.items():
            for cabinet_name, cabinet_info in system_cabinets.items():
                # Get cached properties for this cabinet
                cached_props = Utilities.CabinetProperties.getCabinetPropertiesFromCache(cabinet_name)
                
                if cached_props:
                    part_props_in_cabinet = {}
                    for prop_name, prop_obj in cached_props.items():
                        # Filter for only PartProperty stereotypes
                        if prop_obj and isPartProperty(prop_obj):
                            part_props_in_cabinet[prop_name] = {
                                'property': prop_obj,
                                'system': system_name,
                                'cabinet': cabinet_name,
                                'room': cabinet_info.get('room', 'Unknown Room') if cabinet_info.get('room') else 'Unknown Room'
                            }
                    
                    if part_props_in_cabinet:
                        # Separate named systems from "Unknown" system
                        if system_name.lower() == "unknown":
                            unknown_cabinet_properties[cabinet_name] = part_props_in_cabinet
                        else:
                            named_cabinet_properties[cabinet_name] = part_props_in_cabinet

        return named_cabinet_properties, unknown_cabinet_properties
        
    except Exception as e:
        printer("Error accessing cached cabinet properties: {0}".format(str(e)))
        return named_cabinet_properties, unknown_cabinet_properties

def find_element_by_name(element_name, part_props):
    """
    Find a presentation element by its name in the current part properties
    """
    for pe, prop_data in part_props.items():
        if prop_data["name"] == element_name:
            return pe
    return None

def validate_grouping_with_cache(part_props, systemCabinetMap):
    """
    Cross-reference current diagram part properties with cached cabinet data
    to determine accurate cabinet relationships.
    Uses hierarchical host_asset resolution for complete grouping.
    """
    validated_grouping = {}
    named_cabinet_props, unknown_cabinet_props = get_cached_cabinet_properties(systemCabinetMap)
    
    # Combine both named and unknown cabinets for host_asset matching
    all_cabinet_names = set(named_cabinet_props.keys()) | set(unknown_cabinet_props.keys())
    
    # Track elements that need hierarchical resolution
    unresolved_elements = {}
    total_elements = len(part_props)
    
    element_counter = 0
    for pe, prop_data in part_props.items():
        element_counter += 1
        element_name = prop_data["name"]
        current_host_asset = prop_data["host_asset"]
        
        # First pass: Check if host_asset is found directly in "Unknown" system
        found_in_cache = False
        for cabinet_name, cabinet_props in unknown_cabinet_props.items():
            if current_host_asset in cabinet_props:
                cache_info = cabinet_props[current_host_asset]
                validated_grouping[pe] = prop_data.copy()
                validated_grouping[pe].update({
                    "validated_cabinet": cabinet_name,
                    "system": cache_info['system'],
                    "room": cache_info['room'],
                    "source": "cache_unknown"
                })
                found_in_cache = True
                break
        
        # Second pass: Check if host_asset is found directly in named systems
        if not found_in_cache:
            for cabinet_name, cabinet_props in named_cabinet_props.items():
                if current_host_asset in cabinet_props:
                    cache_info = cabinet_props[current_host_asset]
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": cabinet_name,
                        "system": cache_info['system'],
                        "room": cache_info['room'],
                        "source": "cache"
                    })
                    found_in_cache = True
                    break
        
        # Third pass: Check if current host_asset matches any known cabinet directly
        if not found_in_cache:
            if current_host_asset in all_cabinet_names:
                # Determine if the cabinet is in unknown or named system (prioritize unknown)
                if current_host_asset in unknown_cabinet_props:
                    # Get system info from first property in this cabinet
                    sample_prop = next(iter(unknown_cabinet_props[current_host_asset].values()))
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": current_host_asset,
                        "system": sample_prop['system'],
                        "room": sample_prop['room'],
                        "source": "host_asset_match_unknown"
                    })
                    found_in_cache = True
                elif current_host_asset in named_cabinet_props:
                    # Get system info from first property in this cabinet
                    sample_prop = next(iter(named_cabinet_props[current_host_asset].values()))
                    validated_grouping[pe] = prop_data.copy()
                    validated_grouping[pe].update({
                        "validated_cabinet": current_host_asset,
                        "system": sample_prop['system'],
                        "room": sample_prop['room'],
                        "source": "host_asset_match"
                    })
                    found_in_cache = True
            else:
                pass
        
        # If still not found, add to unresolved for hierarchical resolution
        if not found_in_cache:
            unresolved_elements[pe] = prop_data
    
    # Handle unresolved elements: group by host_asset
    for pe, prop_data in unresolved_elements.items():
        element_name = prop_data["name"]
        current_host_asset = prop_data["host_asset"]
        
        # Get room information directly from the element's host_location property
        host_location = get_host_location(pe)
        room_name = host_location if host_location != "None" else "Unknown Room"
        
        # Use host_asset as the grouping cabinet
        validated_grouping[pe] = prop_data.copy()
        validated_grouping[pe].update({
            "validated_cabinet": current_host_asset,
            "system": None,
            "room": room_name,
            "source": "host_asset_fallback"
        })
    
    return validated_grouping

def create_cabinet_hierarchy_grouping(validated_props):
    """
    Create hierarchical grouping: System -> Cabinet -> Elements
    """
    hierarchy = {}
    
    for pe, prop_data in validated_props.items():
        system = prop_data.get("system", "Unknown System")
        cabinet = prop_data.get("validated_cabinet", "Unknown Cabinet")
        room = prop_data.get("room", "Unknown Room")
        
        if system not in hierarchy:
            hierarchy[system] = {}
        
        if cabinet not in hierarchy[system]:
            hierarchy[system][cabinet] = {
                "room": room,
                "elements": []
            }
        
        hierarchy[system][cabinet]["elements"].append(prop_data)
    
    return hierarchy

def relocate_pe(pe, x, y):
    """Move a presentation element to new coordinates"""
    try:
        new_bounds = Rectangle(int(x), int(y), pe.getBounds().width, pe.getBounds().height)
        PresentationElementsManager.getInstance().reshapeShapeElement(pe, new_bounds)
    except Exception as e:
        printer("Warning: Could not relocate element: {0}".format(str(e)))

def redraw_connectors(connector_props, diagram):
    """Restore connectors to the diagram"""
    try:
        pem = PresentationElementsManager.getInstance()
        for conn in connector_props:
            # Re-add the connector presentation element back to the diagram
            diagram.addPresentationElement(conn)
    except Exception as e:
        printer("Warning: Could not restore connectors: {0}".format(str(e)))

def create_rectangular_separator(diagram, label, x, y, width, height):
    """Create visual rectangular separator with styled border and label"""
    try:
        # Get project instance and managers
        project = Application.getInstance().getProject()
        pem = PresentationElementsManager.getInstance()
        
        # Create rectangular shape (same approach as fid_utils.py)
        rec_sep = pem.createRectangularShape(diagram, Point(int(x), int(y)))
        pem.reshapeShapeElement(rec_sep, Rectangle(int(x), int(y), int(width), int(height)))
        rec_sep.setName(str(label))
        
        # Apply styling to the rectangular shape
        shape_pm = PropertyManager()
        
        # Gray border color
        border_color_prop = rec_sep.getProperty(PropertyID.PEN_COLOR)
        if border_color_prop:
            new_border_color = border_color_prop.clone()
            new_border_color.setValue(Color(128, 128, 128))
            shape_pm.addProperty(new_border_color)
        
        # Transparent fill color
        fill_color_prop = rec_sep.getProperty(PropertyID.FILL_COLOR)
        if fill_color_prop:
            new_fill_color = fill_color_prop.clone()
            new_fill_color.setValue(Color(255, 255, 255, 0))  # Transparent
            shape_pm.addProperty(new_fill_color)
        
        # Apply styling
        if shape_pm.getProperties():
            pem.setPresentationElementProperties(rec_sep, shape_pm)

        return rec_sep
        
    except Exception as e:
        printer("Error creating visual separator: {0}".format(str(e)))
    
    return None

def create_cabinet_separator(diagram, cabinet_info, x, y, width, height):
    """
    Create a styled rectangular separator for cabinet groupings with enhanced labeling
    """
    system = cabinet_info.get("system", "Unknown System")
    cabinet = cabinet_info.get("cabinet", "Unknown Cabinet")
    room = cabinet_info.get("room", "Unknown Room")

    system_label = "SYSTEM: {}".format("UNKNOWN" if system in [None, "Unknown System"] else system.upper() if system == "Unknown" else system)
    cabinet_label = "OWNING ASSET: {}".format(cabinet if cabinet != "Unknown Cabinet" else "NONE")
    room_label = "ROOM: {}".format(room if room != "Unknown Room" else "NONE")
    
    label = "{} | {} | {}".format(system_label, cabinet_label, room_label)
    
    create_rectangular_separator(diagram, label, x, y, width, height)

def geolocate_elements_hierarchical(hierarchy, diagram_size, diagram, selected_element=None):
    """
    Enhanced layout function that arranges cabinet groups using same grid layout as geolocate_
    """
    diagram_len = diagram_size.getWidth()
    diagram_height = diagram_size.getHeight()

    # Use same layout parameters as geolocate_ for consistency
    blocks_per_row = 4          # Maximum blocks in a row
    block_spacing_x = 100       # Horizontal spacing between blocks
    block_spacing_y = 100       # Vertical spacing between rows
    column_spacing = 20         # Space between columns within a block
    row_spacing = 30           # Space between rows of elements
    columns_per_block = 2      # Elements per block in 2 columns
    top_margin = 100
    left_margin = 100
    padding = 20

    current_x_offset = 0
    current_y_offset = top_margin
    row_max_height = 0
    block_counter = 0

    # Flatten hierarchy into blocks (System -> Cabinet combinations)
    cabinet_blocks = []
    for system_name, system_cabinets in hierarchy.items():
        for cabinet_name, cabinet_data in system_cabinets.items():
            # Format with clear prefixes
            system_label = "SYSTEM: {}".format("UNKNOWN" if system_name in [None, "Unknown System"] else system_name.upper() if system_name == "Unknown" else system_name)
            cabinet_label = "OWNING ASSET: {}".format(cabinet_name if cabinet_name != "Unknown Cabinet" else "NONE")
            room_label = "ROOM: {}".format(cabinet_data["room"] if cabinet_data["room"] != "Unknown Room" else "NONE")
            
            block_key = "{} | {} | {}".format(system_label, cabinet_label, room_label)
            cabinet_blocks.append((block_key, cabinet_data["elements"]))

    for block_key, items_list in cabinet_blocks:
        col_heights = [0] * columns_per_block
        max_col_widths = [0] * columns_per_block
        arranged_positions = []

        for i, item in enumerate(items_list):
            pe = item.get("pe") or item.get("presentation_element")
            # Calculate width and height from bounds if not available
            if "width" in item and "height" in item:
                width = item["width"]
                height = item["height"]
            else:
                bounds = pe.getBounds()
                width = bounds.getWidth()
                height = bounds.getHeight()
            col = i % columns_per_block

            x_local = sum(max_col_widths[:col]) + col * column_spacing
            y_local = col_heights[col]

            arranged_positions.append((pe, x_local, y_local))
            col_heights[col] += height + row_spacing
            max_col_widths[col] = max(max_col_widths[col], width)

        block_width = sum(max_col_widths) + (columns_per_block - 1) * column_spacing
        block_height = max(col_heights)

        x_start = left_margin + current_x_offset
        y_start = current_y_offset

        for pe, x_local, y_local in arranged_positions:
            x = x_start + x_local
            y = y_start + y_local
            relocate_pe(pe, x, y)

        # Only create labeled separator box for groups with multiple elements
        # Individual elements (whether matched or fallback) don't need grouping boxes
        should_draw_separator = len(items_list) > 1
        
        if should_draw_separator:
            separator_x = x_start - padding
            separator_y = y_start - padding
            separator_width = block_width + padding * 2
            separator_height = block_height + padding * 2
            create_rectangular_separator(diagram, block_key, separator_x, separator_y, separator_width, separator_height)

        # Update X offset and max height in current row (same as geolocate_)
        current_x_offset += block_width + block_spacing_x
        row_max_height = max(row_max_height, block_height + padding * 2)

        block_counter += 1

        # Wrap to new row every 4 blocks (same as geolocate_)
        if block_counter % blocks_per_row == 0:
            current_x_offset = 0
            current_y_offset += row_max_height + block_spacing_y
            row_max_height = 0

def geolocate_(chunked_part_props, diagram_size, diagram):
    """
    Original layout function - kept for backward compatibility
    """
    diagram_len = diagram_size.getWidth()
    diagram_height = diagram_size.getHeight()

    blocks_per_row = 4
    block_spacing_x = 100
    block_spacing_y = 100
    column_spacing = 20
    row_spacing = 30
    columns_per_block = 2
    top_margin = 100
    left_margin = 100
    padding = 20

    current_x_offset = 0
    current_y_offset = top_margin
    row_max_height = 0
    block_counter = 0

    for chunk_key in chunked_part_props:
        items_list = chunked_part_props[chunk_key]

        col_heights = [0] * columns_per_block
        max_col_widths = [0] * columns_per_block
        arranged_positions = []

        for i, item in enumerate(items_list):
            pe = item.get("pe") or item.get("presentation_element")
            # Calculate width and height from bounds if not available
            if "width" in item and "height" in item:
                width = item["width"]
                height = item["height"]
            else:
                bounds = pe.getBounds()
                width = bounds.getWidth()
                height = bounds.getHeight()
            col = i % columns_per_block

            x_local = sum(max_col_widths[:col]) + col * column_spacing
            y_local = col_heights[col]

            arranged_positions.append((pe, x_local, y_local))
            col_heights[col] += height + row_spacing
            max_col_widths[col] = max(max_col_widths[col], width)

        block_width = sum(max_col_widths) + (columns_per_block - 1) * column_spacing
        block_height = max(col_heights)

        x_start = left_margin + current_x_offset
        y_start = current_y_offset

        for pe, x_local, y_local in arranged_positions:
            x = x_start + x_local
            y = y_start + y_local
            relocate_pe(pe, x, y)

        separator_x = x_start - padding
        separator_y = y_start - padding
        separator_width = block_width + padding * 2
        separator_height = block_height + padding * 2

        create_rectangular_separator(diagram, chunk_key, separator_x, separator_y, separator_width, separator_height)

        current_x_offset += block_width + block_spacing_x
        row_max_height = max(row_max_height, block_height + padding * 2)

        block_counter += 1

        if block_counter % blocks_per_row == 0:
            current_x_offset = 0
            current_y_offset += row_max_height + block_spacing_y
            row_max_height = 0

# ====== END CABINET-BASED GROUPING FUNCTIONS ======

def apply_elk_layout_after_grouping(selected_ibd):
    """Apply ELK layout for improved FID presentation after cabinet grouping is complete"""
    printer("DEBUG: Starting apply_elk_layout_after_grouping")
    elk_layout_applied = False

    # TEMPORARY: Skip ELK to test cabinet grouping
    printer("DEBUG: TEMPORARILY SKIPPING ELK - Testing cabinet grouping only")
    elk_layout_applied = False

    # Uncomment this block to re-enable ELK testing
    # try:
    #     from com.pmw790.jython import jythonFunctions
    #     jf = jythonFunctions(None, None)  # Minimal instance for layout access
    #
    #     if jf.isELKAvailable():
    #         printer("DEBUG: ELK is available, applying ELK layout for improved FID presentation after cabinet grouping...")
    #         jf.applyELKFIDLayout(selected_ibd)
    #         elk_layout_applied = True
    #         printer("DEBUG: ELK layout applied successfully")
    #     else:
    #         printer("DEBUG: ELK layout not available - using standard MagicDraw layout after cabinet grouping")
    # except Exception as elk_ex:
    #     printer("ERROR: ELK layout failed after cabinet grouping: " + str(elk_ex) + " - falling back to standard layout")
    #     import traceback
    #     printer("ERROR: ELK traceback: {}".format(traceback.format_exc()))

    # Fallback to standard layout if ELK not applied
    if not elk_layout_applied:
        printer("DEBUG: Applying fallback standard layout")
        try:
            from com.nomagic.magicdraw.core import Application
            from com.nomagic.magicdraw.ui.dialogs import MDDialogParentProvider
            from com.nomagic.magicdraw.uml.symbols.layouts import OrderedHierarchicDiagramLayouter
            from com.nomagic.magicdraw.uml.symbols.layouts import HierarchicLayouterOptionsGroup

            project = Application.getInstance().getProject()
            # Apply standard layout first
            printer("DEBUG: Applying basic diagram layout")
            project.getDiagram(selected_ibd.getDiagram()).layout(True)

            try:
                printer("DEBUG: Applying OrderedHierarchicDiagramLayouter with LEFT_TO_RIGHT orientation")
                layouter = OrderedHierarchicDiagramLayouter()
                options = HierarchicLayouterOptionsGroup()
                options.setOrientation("LEFT_TO_RIGHT")
                project.getDiagram(selected_ibd.getDiagram()).layout(True, layouter, options)
                printer("DEBUG: OrderedHierarchicDiagramLayouter applied successfully")
            except Exception as ex:
                printer("WARNING: Could not apply OrderedHierarchicDiagramLayouter with left-to-right orientation after grouping: " + str(ex))
                try:
                    printer("DEBUG: Trying default OrderedHierarchicDiagramLayouter")
                    project.getDiagram(selected_ibd.getDiagram()).layout(True, OrderedHierarchicDiagramLayouter())
                    printer("DEBUG: Default OrderedHierarchicDiagramLayouter applied successfully")
                except Exception as ex2:
                    printer("WARNING: Could not apply default OrderedHierarchicDiagramLayouter after grouping: " + str(ex2))
        except Exception as layout_ex:
            printer("WARNING: Could not apply any layout after cabinet grouping: " + str(layout_ex))

    printer("DEBUG: Finished apply_elk_layout_after_grouping")

def rearrange_items(selected_ibd, systemCabinetMap=None):
    """Enhanced rearrangement using cabinet-based grouping with systemCabinetMap"""
    part_props = {}
    connector_props = []
    pem = PresentationElementsManager.getInstance()
    diagram_size = selected_ibd.getBounds()

    element_count = 0
    for pe in selected_ibd.getPresentationElements():
        element_count += 1
        if pe.getHumanType() == "Part Property":
            part_props[pe] = {
                "name": pe.getElement().getName(),
                "presentation_element": pe,
                "element": pe.getElement(),
                "host_asset": get_host_asset(pe)
            }
        if pe.getHumanType() == "Connector":
            connector_props.append(pe)

    try:
        if systemCabinetMap and len(part_props) > 0:
            # Validate grouping using cached cabinet data
            validated_props = validate_grouping_with_cache(part_props, systemCabinetMap)

            # Create hierarchical grouping
            hierarchy = create_cabinet_hierarchy_grouping(validated_props)

            # Apply hierarchical layout using extension
            geolocate_elements_hierarchical(hierarchy, diagram_size, selected_ibd)
            
        else:
            # Convert part_props format for basic grouping
            basic_part_props = {}
            for pe, prop_data in part_props.items():
                basic_part_props[pe] = {
                    "pe": pe,
                    "name": pe.getName(),
                    "bounds": pe.getBounds(),
                    "x": pe.getBounds().getX(),
                    "y": pe.getBounds().getY(),
                    "width": pe.getBounds().getWidth(),
                    "height": pe.getBounds().getHeight(),
                    "host_asset": prop_data["host_asset"]
                }
            
            # Fallback to original grouping method
            chunked = sort_by_host(basic_part_props)
            geolocate_(chunked, diagram_size, selected_ibd)
            
        # Apply ELK layout for improved FID presentation after cabinet grouping is complete
        apply_elk_layout_after_grouping(selected_ibd)
            
    except Exception as e:
        import traceback
        
        # Convert part_props format for fallback grouping
        basic_part_props = {}
        for pe, prop_data in part_props.items():
            basic_part_props[pe] = {
                "pe": pe,
                "name": pe.getName(),
                "bounds": pe.getBounds(),
                "x": pe.getBounds().getX(),
                "y": pe.getBounds().getY(),
                "width": pe.getBounds().getWidth(),
                "height": pe.getBounds().getHeight(),
                "host_asset": prop_data["host_asset"]
            }
        
        # Fallback to original grouping method
        chunked = sort_by_host(basic_part_props)
        geolocate_(chunked, diagram_size, selected_ibd)
        
        # Apply ELK layout for improved FID presentation after cabinet grouping fallback
        apply_elk_layout_after_grouping(selected_ibd)