<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Validification Strategy (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../documentation.html">Documentation</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../reference.html">Reference <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>Validification Strategy</h1>

    <table>
<thead>
<tr>
<th>Property</th>
<th>Value</th>
</tr>
</thead>
<tbody>
<tr>
<td><em>Type:</em></td>
<td>advanced</td>
</tr>
<tr>
<td><em>Identifier:</em></td>
<td><code>org.eclipse.elk.layered.wrapping.validify.strategy</code></td>
</tr>
<tr>
<td><em>Meta Data Provider:</em></td>
<td><code>options.LayeredMetaDataProvider</code></td>
</tr>
<tr>
<td><em>Value Type:</em></td>
<td><code>org.eclipse.elk.alg.layered.options.ValidifyStrategy</code> (Enum)</td>
</tr>
<tr>
<td><em>Possible Values:</em></td>
<td><code>NO</code><br><code>GREEDY</code><br><code>LOOK_BACK</code></td>
</tr>
<tr>
<td><em>Default Value:</em></td>
<td><code>ValidifyStrategy.GREEDY</code> (as defined in <code>org.eclipse.elk.layered</code>)</td>
</tr>
<tr>
<td><em>Applies To:</em></td>
<td>parents</td>
</tr>
<tr>
<td><em>Dependencies:</em></td>
<td><a href="../../reference/options/org-eclipse-elk-layered-wrapping-strategy.html">org.eclipse.elk.layered.wrapping.strategy</a> (<code>WrappingStrategy.SINGLE_EDGE</code>), <a href="../../reference/options/org-eclipse-elk-layered-wrapping-strategy.html">org.eclipse.elk.layered.wrapping.strategy</a> (<code>WrappingStrategy.MULTI_EDGE</code>)</td>
</tr>
<tr>
<td><em>Containing Group:</em></td>
<td><a href="../../reference/groups/org-eclipse-elk-layered-wrapping.html">wrapping</a> -&gt; <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-validify.html">validify</a></td>
</tr>
</tbody>
</table>
<h3 id="description">Description</h3>
<p>When wrapping graphs, one can specify indices that are not allowed as split points. The validification strategy makes sure every computed split point is allowed.</p>

  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
    
      
        




  
  <a href="../../reference/algorithms.html">
    <li class="navlevel-1">
      Algorithms
    </li>
  </a>
  
    
    




  
  <a href="../../reference/algorithms/org-eclipse-elk-conn-gmf-layouter-Draw2D.html">
    <li class="navlevel-2">
      Draw2D Layout
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-box.html">
    <li class="navlevel-2">
      ELK Box
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-disco.html">
    <li class="navlevel-2">
      ELK DisCo
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-fixed.html">
    <li class="navlevel-2">
      ELK Fixed
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-force.html">
    <li class="navlevel-2">
      ELK Force
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-layered.html">
    <li class="navlevel-2">
      ELK Layered
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-mrtree.html">
    <li class="navlevel-2">
      ELK Mr. Tree
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-radial.html">
    <li class="navlevel-2">
      ELK Radial
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-random.html">
    <li class="navlevel-2">
      ELK Randomizer
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-rectpacking.html">
    <li class="navlevel-2">
      ELK Rectangle Packing
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-sporeCompaction.html">
    <li class="navlevel-2">
      ELK SPOrE Compaction
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-sporeOverlap.html">
    <li class="navlevel-2">
      ELK SPOrE Overlap Removal
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-stress.html">
    <li class="navlevel-2">
      ELK Stress
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-topdownpacking.html">
    <li class="navlevel-2">
      ELK Top-down Packing
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-graphviz-circo.html">
    <li class="navlevel-2">
      Graphviz Circo
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-graphviz-dot.html">
    <li class="navlevel-2">
      Graphviz Dot
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-graphviz-fdp.html">
    <li class="navlevel-2">
      Graphviz FDP
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-graphviz-neato.html">
    <li class="navlevel-2">
      Graphviz Neato
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-graphviz-twopi.html">
    <li class="navlevel-2">
      Graphviz Twopi
    </li>
  </a>
  

  
  <a href="../../reference/algorithms/org-eclipse-elk-alg-libavoid.html">
    <li class="navlevel-2">
      Libavoid
    </li>
  </a>
  


  

  
  <a href="../../reference/options.html">
    <li class="navlevel-1">
      Layout Options
    </li>
  </a>
  
    
    




  
  <a href="../../reference/options/org-eclipse-elk-insideSelfLoops-activate.html">
    <li class="navlevel-2">
      Activate Inside Self Loops
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-adaptPortPositions.html">
    <li class="navlevel-2">
      Adapt Port Positions
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-unnecessaryBendpoints.html">
    <li class="navlevel-2">
      Add Unnecessary Bendpoints
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-portsSurrounding.html">
    <li class="navlevel-2">
      Additional Port Space
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-rotation-computeAdditionalWedgeSpace.html">
    <li class="navlevel-2">
      Additional Wedge Space
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-additionalEdgeSpacing.html">
    <li class="navlevel-2">
      Additional Wrapped Edges Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alignment.html">
    <li class="navlevel-2">
      Alignment
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-allowNonFlowPortsToSwitchSides.html">
    <li class="navlevel-2">
      Allow Non-Flow Ports To Switch Sides
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-anglePenalty.html">
    <li class="navlevel-2">
      Angle Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-animate.html">
    <li class="navlevel-2">
      Animate
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-animTimeFactor.html">
    <li class="navlevel-2">
      Animation Time Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-wedgeCriteria.html">
    <li class="navlevel-2">
      Annulus Wedge Criteria
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-aspectRatio.html">
    <li class="navlevel-2">
      Aspect Ratio
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-bendPoints.html">
    <li class="navlevel-2">
      Bend Points
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-bk-edgeStraightening.html">
    <li class="navlevel-2">
      BK Edge Straightening
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-bk-fixedAlignment.html">
    <li class="navlevel-2">
      BK Fixed Alignment
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-box-packingMode.html">
    <li class="navlevel-2">
      Box Layout Mode
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-centerOnRoot.html">
    <li class="navlevel-2">
      Center On Root
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-childAreaHeight.html">
    <li class="navlevel-2">
      Child Area Height
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-childAreaWidth.html">
    <li class="navlevel-2">
      Child Area Width
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-clusterCrossingPenalty.html">
    <li class="navlevel-2">
      Cluster Crossing Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-commentBox.html">
    <li class="navlevel-2">
      Comment Box
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-commentComment.html">
    <li class="navlevel-2">
      Comment Comment Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-commentNode.html">
    <li class="navlevel-2">
      Comment Node Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-compactor.html">
    <li class="navlevel-2">
      Compaction
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-packing-compaction-iterations.html">
    <li class="navlevel-2">
      Compaction iterations
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-compactionStepSize.html">
    <li class="navlevel-2">
      Compaction Step Size
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-compaction-compactionStrategy.html">
    <li class="navlevel-2">
      Compaction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-packing-strategy.html">
    <li class="navlevel-2">
      Compaction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-componentComponent.html">
    <li class="navlevel-2">
      Components Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-concentrate.html">
    <li class="navlevel-2">
      Concentrate Edges
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-compaction-connectedComponents.html">
    <li class="navlevel-2">
      Connected Components Compaction
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-disco-componentCompaction-strategy.html">
    <li class="navlevel-2">
      Connected Components Compaction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-disco-componentCompaction-componentLayoutAlgorithm.html">
    <li class="navlevel-2">
      Connected Components Layout Algorithm
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-strategy.html">
    <li class="navlevel-2">
      Consider Model Order
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-components.html">
    <li class="navlevel-2">
      Consider Model Order for Components
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-portModelOrder.html">
    <li class="navlevel-2">
      Consider Port Order
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-contentAlignment.html">
    <li class="navlevel-2">
      Content Alignment
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-correctionFactor.html">
    <li class="navlevel-2">
      Correction Factor for Wrapping
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-processingOrder-spanningTreeCostFunction.html">
    <li class="navlevel-2">
      Cost Function for Spanning Tree
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterNodeInfluence.html">
    <li class="navlevel-2">
      Crossing Counter Node Order Influence
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-crossingCounterPortInfluence.html">
    <li class="navlevel-2">
      Crossing Counter Port Order Influence
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-strategy.html">
    <li class="navlevel-2">
      Crossing Minimization Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-crossingPenalty.html">
    <li class="navlevel-2">
      Crossing Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-currentPosition.html">
    <li class="navlevel-2">
      Current position of a node in the order of nodes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-cutting-strategy.html">
    <li class="navlevel-2">
      Cutting Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-cycleBreaking-strategy.html">
    <li class="navlevel-2">
      Cycle Breaking Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-disco-debug-discoGraph.html">
    <li class="navlevel-2">
      DCGraph
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-debugMode.html">
    <li class="navlevel-2">
      Debug Mode
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-stress-desiredEdgeLength.html">
    <li class="navlevel-2">
      Desired Edge Length
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-desiredPosition.html">
    <li class="navlevel-2">
      Desired index of node
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-direction.html">
    <li class="navlevel-2">
      Direction
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-directionCongruency.html">
    <li class="navlevel-2">
      Direction Congruency
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-priority-direction.html">
    <li class="navlevel-2">
      Direction Priority
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-neatoModel.html">
    <li class="navlevel-2">
      Distance Model
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-multiEdge-distancePenalty.html">
    <li class="navlevel-2">
      Distance Penalty When Improving Cuts 
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-force-repulsion.html">
    <li class="navlevel-2">
      Eades Repulsion
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeLabels-centerLabelPlacementStrategy.html">
    <li class="navlevel-2">
      Edge Center Label Placement Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-spacing-edgeEdgeBetweenLayers.html">
    <li class="navlevel-2">
      Edge Edge Between Layer Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-edgeEndTextureLength.html">
    <li class="navlevel-2">
      Edge End Texture Length
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-edgeLabels-placement.html">
    <li class="navlevel-2">
      Edge Label Placement
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeLabels-sideSelection.html">
    <li class="navlevel-2">
      Edge Label Side Selection
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-edgeLabel.html">
    <li class="navlevel-2">
      Edge Label Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-spacing-edgeNodeBetweenLayers.html">
    <li class="navlevel-2">
      Edge Node Between Layers Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-edgeNode.html">
    <li class="navlevel-2">
      Edge Node Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-edgeRouting.html">
    <li class="navlevel-2">
      Edge Routing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-edgeRoutingMode.html">
    <li class="navlevel-2">
      Edge Routing Mode
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-edgeEdge.html">
    <li class="navlevel-2">
      Edge Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-edge-thickness.html">
    <li class="navlevel-2">
      Edge Thickness
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-edge-type.html">
    <li class="navlevel-2">
      Edge Type
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-enableHyperedgesFromCommonSource.html">
    <li class="navlevel-2">
      Enable Hyperedges From Common Source
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-epsilon.html">
    <li class="navlevel-2">
      Epsilon
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-expandNodes.html">
    <li class="navlevel-2">
      Expand Nodes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-favorStraightEdges.html">
    <li class="navlevel-2">
      Favor Straight Edges Over Balancing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-feedbackEdges.html">
    <li class="navlevel-2">
      Feedback Edges
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-polyomino-fill.html">
    <li class="navlevel-2">
      Fill Polyominoes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeSize-fixedGraphSize.html">
    <li class="navlevel-2">
      Fixed Graph Size
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-stress-fixed.html">
    <li class="navlevel-2">
      Fixed Position
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-fixedSharedPathPenalty.html">
    <li class="navlevel-2">
      Fixed Shared Path Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-font-name.html">
    <li class="navlevel-2">
      Font Name
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-font-size.html">
    <li class="navlevel-2">
      Font Size
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-force-model.html">
    <li class="navlevel-2">
      Force Model
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-forceNodeModelOrder.html">
    <li class="navlevel-2">
      Force Node Model Order
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-force-temperature.html">
    <li class="navlevel-2">
      FR Temperature
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-generatePositionAndLayerIds.html">
    <li class="navlevel-2">
      Generate Position and Layer IDs
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-strategy.html">
    <li class="navlevel-2">
      Graph Wrapping Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-activationThreshold.html">
    <li class="navlevel-2">
      Greedy Switch Activation Threshold
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitch-type.html">
    <li class="navlevel-2">
      Greedy Switch Crossing Minimization
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical-type.html">
    <li class="navlevel-2">
      Greedy Switch Crossing Minimization (hierarchical)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-hierarchicalSweepiness.html">
    <li class="navlevel-2">
      Hierarchical Sweepiness
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-hierarchyHandling.html">
    <li class="navlevel-2">
      Hierarchy Handling
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-hierarchyHandling_org-eclipse-elk-graphviz-dot.html">
    <li class="navlevel-2">
      Hierarchy Handling (Graphviz Dot)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-highDegreeNodes-treeHeight.html">
    <li class="navlevel-2">
      High Degree Node Maximum Tree Height
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-highDegreeNodes-threshold.html">
    <li class="navlevel-2">
      High Degree Node Threshold
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-highDegreeNodes-treatment.html">
    <li class="navlevel-2">
      High Degree Node Treatment
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-labelPortHorizontal.html">
    <li class="navlevel-2">
      Horizontal spacing between Label and Port
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-hypernode.html">
    <li class="navlevel-2">
      Hypernode
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-idealNudgingDistance.html">
    <li class="navlevel-2">
      Ideal Nudging Distance
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveCuts.html">
    <li class="navlevel-2">
      Improve Cuts
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingJunctions.html">
    <li class="navlevel-2">
      Improve Hyperedge Routes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-improveHyperedgeRoutesMovingAddingAndDeletingJunctions.html">
    <li class="navlevel-2">
      Improve Hyperedge Routes Add/Delete
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-multiEdge-improveWrappedEdges.html">
    <li class="navlevel-2">
      Improve Wrapped Edges
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerPredOf.html">
    <li class="navlevel-2">
      In Layer Predecessor of
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-inLayerSuccOf.html">
    <li class="navlevel-2">
      In Layer Successor of
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-inNewRow.html">
    <li class="navlevel-2">
      In new Row
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-individual.html">
    <li class="navlevel-2">
      Individual Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-individual_org-eclipse-elk-layered.html">
    <li class="navlevel-2">
      Individual Spacing (ELK Layered)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-edgeLabels-inline.html">
    <li class="navlevel-2">
      Inline Edge Labels
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-insideSelfLoops-yo.html">
    <li class="navlevel-2">
      Inside Self Loop
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-interactive.html">
    <li class="navlevel-2">
      Interactive
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-interactiveLayout.html">
    <li class="navlevel-2">
      interactive Layout
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-interactiveReferencePoint.html">
    <li class="navlevel-2">
      Interactive Reference Point
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-stress-iterationLimit.html">
    <li class="navlevel-2">
      Iteration Limit
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-force-iterations.html">
    <li class="navlevel-2">
      Iterations
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-iterationsFactor.html">
    <li class="navlevel-2">
      Iterations Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-junctionPoints.html">
    <li class="navlevel-2">
      Junction Points
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-labelAngle.html">
    <li class="navlevel-2">
      Label Angle
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-labelDistance.html">
    <li class="navlevel-2">
      Label Distance
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-labelManager.html">
    <li class="navlevel-2">
      Label Manager
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-labels-labelManager.html">
    <li class="navlevel-2">
      Label Manager
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-labelNode.html">
    <li class="navlevel-2">
      Label Node Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-labelLabel.html">
    <li class="navlevel-2">
      Label Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-coffmanGraham-layerBound.html">
    <li class="navlevel-2">
      Layer Bound
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-layerChoiceConstraint.html">
    <li class="navlevel-2">
      Layer Choice Constraint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-layerConstraint.html">
    <li class="navlevel-2">
      Layer Constraint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-layerId.html">
    <li class="navlevel-2">
      Layer ID
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-layerSpacingFactor.html">
    <li class="navlevel-2">
      Layer Spacing Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-algorithm.html">
    <li class="navlevel-2">
      Layout Algorithm
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layoutAncestors.html">
    <li class="navlevel-2">
      Layout Ancestors
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-stress-dimension.html">
    <li class="navlevel-2">
      Layout Dimension
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-partitioning-partition.html">
    <li class="navlevel-2">
      Layout Partition
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-partitioning-activate.html">
    <li class="navlevel-2">
      Layout Partitioning
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-linearSegments-deflectionDampening.html">
    <li class="navlevel-2">
      Linear Segments Deflection Dampening
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-disco-debug-discoPolys.html">
    <li class="navlevel-2">
      List of Polyominoes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-longEdgeStrategy.html">
    <li class="navlevel-2">
      Long Edge Ordering Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-cutting-cuts.html">
    <li class="navlevel-2">
      Manually Specified Cuts
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-margins.html">
    <li class="navlevel-2">
      Margins
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-isCluster.html">
    <li class="navlevel-2">
      Marks a node as a cluster
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-nodePromotion-maxIterations.html">
    <li class="navlevel-2">
      Max Node Promotion Iterations
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-maxiter.html">
    <li class="navlevel-2">
      Max. Iterations
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-maxAnimTime.html">
    <li class="navlevel-2">
      Maximal Animation Time
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-mergeEdges.html">
    <li class="navlevel-2">
      Merge Edges
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-mergeHierarchyEdges.html">
    <li class="navlevel-2">
      Merge Hierarchy-Crossing Edges
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-minAnimTime.html">
    <li class="navlevel-2">
      Minimal Animation Time
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-cutting-msd-freedom.html">
    <li class="navlevel-2">
      MSD Freedom
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-noLayout.html">
    <li class="navlevel-2">
      No Layout
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-considerModelOrder-noModelOrder.html">
    <li class="navlevel-2">
      No Model Order
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdownpacking-nodeArrangement-strategy.html">
    <li class="navlevel-2">
      Node arrangement strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html">
    <li class="navlevel-2">
      Node Flexibility
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility-default.html">
    <li class="navlevel-2">
      Node Flexibility Default
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeLabels-padding.html">
    <li class="navlevel-2">
      Node Label Padding
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeLabels-placement.html">
    <li class="navlevel-2">
      Node Label Placement
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-strategy.html">
    <li class="navlevel-2">
      Node Layering Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-spacing-nodeNodeBetweenLayers.html">
    <li class="navlevel-2">
      Node Node Between Layers Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-nodePlacement-strategy.html">
    <li class="navlevel-2">
      Node Placement Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-nodePromotion-strategy.html">
    <li class="navlevel-2">
      Node Promotion Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-nodeSelfLoop.html">
    <li class="navlevel-2">
      Node Self Loop Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeSize-constraints.html">
    <li class="navlevel-2">
      Node Size Constraints
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeSize-minimum.html">
    <li class="navlevel-2">
      Node Size Minimum
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-nodeSize-options.html">
    <li class="navlevel-2">
      Node Size Options
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-nodeNode.html">
    <li class="navlevel-2">
      Node Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalSegmentsConnectedToShapes.html">
    <li class="navlevel-2">
      Nudge Orthogonal Segments
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-nudgeOrthogonalTouchingColinearSegments.html">
    <li class="navlevel-2">
      Nudge Orthogonal Touching Colinear Segments
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-nudgeSharedPathsWithCommonEndPoint.html">
    <li class="navlevel-2">
      Nudge Shared Paths With Common Endpoint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-omitNodeMicroLayout.html">
    <li class="navlevel-2">
      Omit Node Micro Layout
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-widthApproximation-optimizationGoal.html">
    <li class="navlevel-2">
      Optimization Goal
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-orderId.html">
    <li class="navlevel-2">
      Order ID 
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-compaction-orthogonal.html">
    <li class="navlevel-2">
      Orthogonal Compaction
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-rotation-outgoingEdgeAngles.html">
    <li class="navlevel-2">
      Outgoing Edge Angles
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-graphviz-overlapMode.html">
    <li class="navlevel-2">
      Overlap Removal
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-padding.html">
    <li class="navlevel-2">
      Padding
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-penaliseOrthogonalSharedPathsAtConnEnds.html">
    <li class="navlevel-2">
      Penalise Orthogonal Shared Paths
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-performUnifyingNudgingPreprocessingStep.html">
    <li class="navlevel-2">
      Perform Unifying Nudging Preprocessing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-polyomino-highLevelSort.html">
    <li class="navlevel-2">
      Polyomino Primary Sorting Criterion
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-polyomino-lowLevelSort.html">
    <li class="navlevel-2">
      Polyomino Secondary Sorting Criterion
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-polyomino-traversalStrategy.html">
    <li class="navlevel-2">
      Polyomino Traversal Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portAlignment-default.html">
    <li class="navlevel-2">
      Port Alignment
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portAlignment-east.html">
    <li class="navlevel-2">
      Port Alignment (East)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portAlignment-north.html">
    <li class="navlevel-2">
      Port Alignment (North)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portAlignment-south.html">
    <li class="navlevel-2">
      Port Alignment (South)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portAlignment-west.html">
    <li class="navlevel-2">
      Port Alignment (West)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-port-anchor.html">
    <li class="navlevel-2">
      Port Anchor Offset
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-port-borderOffset.html">
    <li class="navlevel-2">
      Port Border Offset
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portConstraints.html">
    <li class="navlevel-2">
      Port Constraints
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-portDirectionPenalty.html">
    <li class="navlevel-2">
      Port Direction Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-port-index2.html">
    <li class="navlevel-2">
      Port Index
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portLabels-placement.html">
    <li class="navlevel-2">
      Port Label Placement
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portLabels-nextToPortIfPossible.html">
    <li class="navlevel-2">
      Port Labels Next to Port
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-port-side.html">
    <li class="navlevel-2">
      Port Side
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-portSortingStrategy.html">
    <li class="navlevel-2">
      Port Sorting Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-portPort.html">
    <li class="navlevel-2">
      Port Spacing
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-position.html">
    <li class="navlevel-2">
      Position
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-positionChoiceConstraint.html">
    <li class="navlevel-2">
      Position Choice Constraint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-compaction.html">
    <li class="navlevel-2">
      Position Constraint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-positionConstraint.html">
    <li class="navlevel-2">
      Position Constraint
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-positionId.html">
    <li class="navlevel-2">
      Position ID
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-compaction-postCompaction-constraints.html">
    <li class="navlevel-2">
      Post Compaction Constraint Calculation
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-compaction-postCompaction-strategy.html">
    <li class="navlevel-2">
      Post Compaction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-priority.html">
    <li class="navlevel-2">
      Priority
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-priority_org-eclipse-elk-box.html">
    <li class="navlevel-2">
      Priority (ELK Box)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-priority_org-eclipse-elk-force.html">
    <li class="navlevel-2">
      Priority (ELK Force)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-priority_org-eclipse-elk-layered.html">
    <li class="navlevel-2">
      Priority (ELK Layered)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-priority_org-eclipse-elk-mrtree.html">
    <li class="navlevel-2">
      Priority (ELK Mr. Tree)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-progressBar.html">
    <li class="navlevel-2">
      Progress Bar
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-radius.html">
    <li class="navlevel-2">
      Radius
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-randomSeed.html">
    <li class="navlevel-2">
      Randomization Seed
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-force-repulsivePower.html">
    <li class="navlevel-2">
      Repulsive Power
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-resolvedAlgorithm.html">
    <li class="navlevel-2">
      Resolved Layout Algorithm
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-reverseDirectionPenalty.html">
    <li class="navlevel-2">
      Reverse Direction Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-processingOrder-preferredRoot.html">
    <li class="navlevel-2">
      Root node for spanning tree construction
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-processingOrder-rootSelection.html">
    <li class="navlevel-2">
      Root selection for spanning tree
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-rotate.html">
    <li class="navlevel-2">
      Rotate
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-packing-compaction-rowHeightReevaluation.html">
    <li class="navlevel-2">
      Row Height Reevaluation
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-scaleFactor.html">
    <li class="navlevel-2">
      Scale Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-searchOrder.html">
    <li class="navlevel-2">
      Search Order
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-segmentPenalty.html">
    <li class="navlevel-2">
      Segment Penalty
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopDistribution.html">
    <li class="navlevel-2">
      Self-Loop Distribution
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeRouting-selfLoopOrdering.html">
    <li class="navlevel-2">
      Self-Loop Ordering
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-crossingMinimization-semiInteractive.html">
    <li class="navlevel-2">
      Semi-Interactive Crossing Minimization
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-separateConnectedComponents.html">
    <li class="navlevel-2">
      Separate Connected Components
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-alg-libavoid-shapeBufferDistance.html">
    <li class="navlevel-2">
      Shape Buffer Distance
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-widthApproximation-lastPlaceShift.html">
    <li class="navlevel-2">
      Shift Last Placed.
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-priority-shortness.html">
    <li class="navlevel-2">
      Shortness Priority
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-priority-shortness_org-eclipse-elk-layered.html">
    <li class="navlevel-2">
      Shortness Priority (ELK Layered)
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeRouting-polyline-slopedEdgeZoneWidth.html">
    <li class="navlevel-2">
      Sloped Edge Zone Width
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeRouting-splines-sloppy-layerSpacingFactor.html">
    <li class="navlevel-2">
      Sloppy Spline Layer Spacing Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-sorter.html">
    <li class="navlevel-2">
      Sorter
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-spacing-baseValue.html">
    <li class="navlevel-2">
      Spacing Base Value
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-edgeRouting-splines-mode.html">
    <li class="navlevel-2">
      Spline Routing Mode
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-priority-straightness.html">
    <li class="navlevel-2">
      Straightness Priority
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-stress-epsilon.html">
    <li class="navlevel-2">
      Stress Epsilon
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-structure-structureExtractionStrategy.html">
    <li class="navlevel-2">
      Structure Extraction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-rotation-targetAngle.html">
    <li class="navlevel-2">
      Target Angle
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-widthApproximation-targetWidth.html">
    <li class="navlevel-2">
      Target Width
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-thoroughness.html">
    <li class="navlevel-2">
      Thoroughness
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-hierarchicalNodeAspectRatio.html">
    <li class="navlevel-2">
      Topdown Hierarchical Node Aspect Ratio
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-hierarchicalNodeWidth.html">
    <li class="navlevel-2">
      Topdown Hierarchical Node Width
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdownLayout.html">
    <li class="navlevel-2">
      Topdown Layout
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-nodeType.html">
    <li class="navlevel-2">
      Topdown Node Type
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-scaleCap.html">
    <li class="navlevel-2">
      Topdown Scale Cap
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-scaleFactor.html">
    <li class="navlevel-2">
      Topdown Scale Factor
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdown-sizeApproximator.html">
    <li class="navlevel-2">
      Topdown Size Approximator
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-radial-optimizationCriteria.html">
    <li class="navlevel-2">
      Translation Optimization
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-portLabels-treatAsGroup.html">
    <li class="navlevel-2">
      Treat Port Labels as Group
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-processingOrder-treeConstruction.html">
    <li class="navlevel-2">
      Tree Construction Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-treeLevel.html">
    <li class="navlevel-2">
      Tree Level
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-trybox.html">
    <li class="navlevel-2">
      Try box layout first
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-underlyingLayoutAlgorithm.html">
    <li class="navlevel-2">
      Underlying Layout Algorithm
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-minWidth-upperBoundOnWidth.html">
    <li class="navlevel-2">
      Upper Bound On Width [MinWidth Layerer]
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-layering-minWidth-upperLayerEstimationScalingFactor.html">
    <li class="navlevel-2">
      Upper Layer Estimation Scaling Factor [MinWidth Layerer]
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-overlapRemoval-maxIterations.html">
    <li class="navlevel-2">
      Upper limit for iterations of overlap removal
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-validify-forbiddenIndices.html">
    <li class="navlevel-2">
      Valid Indices for Wrapping
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-validateGraph.html">
    <li class="navlevel-2">
      Validate Graph
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-validateOptions.html">
    <li class="navlevel-2">
      Validate Options
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-layered-wrapping-validify-strategy.html">
    <li class="navlevel-2 active">
      Validification Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-spacing-labelPortVertical.html">
    <li class="navlevel-2">
      Vertical spacing between Label and Port
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-mrtree-weighting.html">
    <li class="navlevel-2">
      Weighting of Nodes
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-overlapRemoval-runScanline.html">
    <li class="navlevel-2">
      Whether to run a supplementary scanline overlap check.
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-whiteSpaceElimination-strategy.html">
    <li class="navlevel-2">
      White Space Approximation Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-topdownpacking-whitespaceElimination-strategy.html">
    <li class="navlevel-2">
      Whitespace elimination strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-rectpacking-widthApproximation-strategy.html">
    <li class="navlevel-2">
      Width Approximation Strategy
    </li>
  </a>
  

  
  <a href="../../reference/options/org-eclipse-elk-zoomToFit.html">
    <li class="navlevel-2">
      Zoom to Fit
    </li>
  </a>
  


  

  
  <a href="../../reference/groups.html">
    <li class="navlevel-1">
      Layout Option Groups
    </li>
  </a>
  
    
    




  
  <a href="../../reference/groups/org-eclipse-elk-box.html">
    <li class="navlevel-2">
      box
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-compaction.html">
    <li class="navlevel-2">
      compaction
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-compaction.html">
    <li class="navlevel-2">
      compaction
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-compaction-postCompaction.html">
    <li class="navlevel-2">
      compaction.postCompaction
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-disco-componentCompaction.html">
    <li class="navlevel-2">
      componentCompaction
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-considerModelOrder.html">
    <li class="navlevel-2">
      considerModelOrder
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-crossingMinimization.html">
    <li class="navlevel-2">
      crossingMinimization
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitch.html">
    <li class="navlevel-2">
      crossingMinimization.greedySwitch
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-crossingMinimization-greedySwitchHierarchical.html">
    <li class="navlevel-2">
      crossingMinimization.greedySwitchHierarchical
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-cycleBreaking.html">
    <li class="navlevel-2">
      cycleBreaking
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-disco-debug.html">
    <li class="navlevel-2">
      debug
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-edge.html">
    <li class="navlevel-2">
      edge
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-edgeLabels.html">
    <li class="navlevel-2">
      edgeLabels
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-edgeLabels.html">
    <li class="navlevel-2">
      edgeLabels
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-edgeRouting.html">
    <li class="navlevel-2">
      edgeRouting
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-edgeRouting-polyline.html">
    <li class="navlevel-2">
      edgeRouting.polyline
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-edgeRouting-splines.html">
    <li class="navlevel-2">
      edgeRouting.splines
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-edgeRouting-splines-sloppy.html">
    <li class="navlevel-2">
      edgeRouting.splines.sloppy
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-font.html">
    <li class="navlevel-2">
      font
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-highDegreeNodes.html">
    <li class="navlevel-2">
      highDegreeNodes
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-insideSelfLoops.html">
    <li class="navlevel-2">
      insideSelfLoops
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-layering.html">
    <li class="navlevel-2">
      layering
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-layering-coffmanGraham.html">
    <li class="navlevel-2">
      layering.coffmanGraham
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-layering-minWidth.html">
    <li class="navlevel-2">
      layering.minWidth
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-layering-nodePromotion.html">
    <li class="navlevel-2">
      layering.nodePromotion
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-topdownpacking-nodeArrangement.html">
    <li class="navlevel-2">
      nodeArrangement
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-nodeLabels.html">
    <li class="navlevel-2">
      nodeLabels
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-nodePlacement.html">
    <li class="navlevel-2">
      nodePlacement
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-nodePlacement-bk.html">
    <li class="navlevel-2">
      nodePlacement.bk
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-nodePlacement-linearSegments.html">
    <li class="navlevel-2">
      nodePlacement.linearSegments
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex.html">
    <li class="navlevel-2">
      nodePlacement.networkSimplex
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-nodePlacement-networkSimplex-nodeFlexibility.html">
    <li class="navlevel-2">
      nodePlacement.networkSimplex.nodeFlexibility
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-nodeSize.html">
    <li class="navlevel-2">
      nodeSize
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-overlapRemoval.html">
    <li class="navlevel-2">
      overlapRemoval
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-packing.html">
    <li class="navlevel-2">
      packing
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-rectpacking-packing-compaction.html">
    <li class="navlevel-2">
      packing.compaction
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-partitioning.html">
    <li class="navlevel-2">
      partitioning
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-polyomino.html">
    <li class="navlevel-2">
      polyomino
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-port.html">
    <li class="navlevel-2">
      port
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-portAlignment.html">
    <li class="navlevel-2">
      portAlignment
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-portLabels.html">
    <li class="navlevel-2">
      portLabels
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-priority.html">
    <li class="navlevel-2">
      priority
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-processingOrder.html">
    <li class="navlevel-2">
      processingOrder
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-radial-rotation.html">
    <li class="navlevel-2">
      rotation
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-spacing.html">
    <li class="navlevel-2">
      spacing
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-spacing.html">
    <li class="navlevel-2">
      spacing
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-structure.html">
    <li class="navlevel-2">
      structure
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-topdown.html">
    <li class="navlevel-2">
      topdown
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-rectpacking-whiteSpaceElimination.html">
    <li class="navlevel-2">
      whiteSpaceElimination
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-topdownpacking-whitespaceElimination.html">
    <li class="navlevel-2">
      whitespaceElimination
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-rectpacking-widthApproximation.html">
    <li class="navlevel-2">
      widthApproximation
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping.html">
    <li class="navlevel-2">
      wrapping
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-cutting.html">
    <li class="navlevel-2">
      wrapping.cutting
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-cutting-msd.html">
    <li class="navlevel-2">
      wrapping.cutting.msd
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-multiEdge.html">
    <li class="navlevel-2">
      wrapping.multiEdge
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-singleEdge.html">
    <li class="navlevel-2">
      wrapping.singleEdge
    </li>
  </a>
  

  
  <a href="../../reference/groups/org-eclipse-elk-layered-wrapping-validify.html">
    <li class="navlevel-2">
      wrapping.validify
    </li>
  </a>
  


  


      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
