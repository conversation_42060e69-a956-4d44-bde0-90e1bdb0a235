import json, difflib, sys, os, re

from datetime import datetime, date, time
from com.nomagic.magicdraw.core import Application
from com.nomagic.magicdraw.openapi.uml import ModelElementsManager as mem
from com.nomagic.uml2.impl import ElementsFactory as ef
from com.nomagic.magicdraw.uml import Finder as f
from com.nomagic.magicdraw.uml.symbols.shapes import DiagramFrameView as DFV
from com.nomagic.magicdraw.uml2 import Profiles
from com.nomagic.magicdraw.openapi.uml import SessionManager
from com.nomagic.magicdraw.sysml.util import SysMLProfile, MDCustomizationForSysMLProfile
from com.nomagic.magicdraw.sysml.util import SysMLConstants
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import AggregationKindEnum
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import VisibilityKindEnum
from com.nomagic.magicdraw.openapi.uml import SessionManager as sm
from com.nomagic.uml2.ext.jmi.helpers import StereotypesHelper as sh
from com.nomagic.uml2.ext.magicdraw.mdprofiles import Stereotype
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Package
from com.nomagic.text.html import HtmlTextUtils
from com.nomagic.uml2.ext.jmi.helpers import ModelHelper as mh
from com.nomagic.uml2.ext.magicdraw.classes.mdkernel import Element, Class, Property
from com.nomagic.uml2.ext.magicdraw.compositestructures.mdports import Port
from com.nomagic.magicdraw.openapi.uml import PresentationElementsManager
from com.nomagic.magicdraw.uml.symbols import PresentationElement;
from com.nomagic.uml2.ext.jmi.helpers import CoreHelper

from com.nomagic.magicdraw.properties import ChoiceProperty, PropertyID, PropertyManager;
from com.nomagic.magicdraw.uml.symbols.shapes import ClassifierView;
from com.nomagic.magicdraw.uml.symbols import ConverterToShape;

from com.nomagic.magicdraw.uml.symbols.shapes import ShapeElement, DiagramPropertiesShape
from com.nomagic.magicdraw.uml.symbols.shapes import PartView
from com.nomagic.magicdraw.uml.symbols.layout.orderedhier import OrderedHierarchicDiagramLayouter
from com.nomagic.magicdraw.core.options import HierarchicLayouterOptionsGroup

from itertools import combinations
from java.util import HashMap
from java.awt import Rectangle, Color, Point
from java.text import SimpleDateFormat
from java.util import Date

import sys
import os

# Add current directory to path for importing fid_utils
standalone_dir = globals().get("standalone_dir", None)
if not standalone_dir:
    raise RuntimeError("standalone_dir is not valid. Function exited execution.)")
if standalone_dir not in sys.path:
    sys.path.insert(0, standalone_dir)

from fid_utils import printer, find_shape_element_for, safe_session_start, isPartProperty, isPort, isBlock, find_generalization_classes, get_recursive_owner_array, get_classifier_description, get_stereotypes, get_metaname, get_name, relocate_pe, create_rectangular_separator, get_host_asset, redraw_connectors

#_________________________________
'''
Finder and get functions
'''
# region
def find_classifiers(element, visited=None):
    """
    Recursively collect the names of all classifiers (superclasses)
    reachable via Generalization relationships.
    """
    if visited is None:
        visited = set()
    classifiers = []

    for rel in element.get_relationshipOfRelatedElement():
        if rel.getHumanType() == "Generalization":
            for target in rel.getTarget():
                tid = target.getID()
                if tid not in visited:
                    visited.add(tid)
                    # add this immediate classifier
                    classifiers.append(target.getName())
                    # recurse to find *its* classifiers
                    classifiers.extend(find_classifiers(target, visited))

    return classifiers

def find_nested_connectors(element, connectors, nested, seen_ids = None):
    if seen_ids is None:
        seen_ids = set()

    for subel in element.getOwnedMember():
        if subel.getHumanType()=="Connector":
            conn_id = subel.getID()
            if conn_id not in seen_ids:
                seen_ids.add(conn_id)
                connector_data = {
                    'connector_obj': subel,
                    'connector_id': subel.getID(),
                    'connection_name' : subel.getName() if subel.getName() else None,
                    'connection_info': get_con_end_info(subel),
                    'connection_stereotypes': get_stereotypes(subel),
                    'connection_metaname': get_metaname(subel)
                }
                connectors.append(connector_data)
        if nested and subel.getHumanType()=="Part Property" and all(x not in find_generalization_classes(subel.getType()) for x in ["Building", "Floor", "Level", "Room"]):
                find_nested_connectors(subel.getType(), connectors, True, seen_ids)
    return connectors

def find_nested_elements(element, target_elements, nested, seen_ids = None):
    if seen_ids is None:
        seen_ids = set()

    for subel in element.getOwnedMember():
        if nested and subel.getHumanType()=="Part Property":
                el_id = subel.getType().getID()
                if el_id in seen_ids:
                    continue
                seen_ids.add(el_id)
                gen_classes = find_generalization_classes(subel.getType())
                if "System" in gen_classes:
                    target_elements[subel.getType()] = {
                        "gen_classes": gen_classes,
                        "part" : subel
                    }
                    find_nested_elements(subel.getType(), target_elements, True, seen_ids)

    return target_elements

def find_association_owning_block(element):
    owning_el = None
    rel = element.get_relationshipOfRelatedElement()
    assoc_rel = [rels for rels in rel if rels.getHumanType() == "Association"]
    for rel_ in assoc_rel:
        for ix in rel_.getMemberEnd():
            if ix.getAggregation().toString() == "none":
                if "Site" in find_generalization_classes(ix.getType()) and ix.getType() != element:
                    owning_el = ix.getType()
    return owning_el

def find_and_delete_existing_diagram(project, owner_element, diagram_type, name_prefix):
    """
    Finds and deletes existing diagrams of the specified type owned by the given element
    with matching name prefix to distinguish between different diagram sources
    
    :param project: The MagicDraw project
    :param owner_element: The element that owns the diagram
    :param diagram_type: The type of diagram to find and delete
    :param name_prefix: The prefix to match in diagram names (e.g., "FID_L2")
    :return: True if existing diagram was found and deleted, False otherwise
    """
    found = False
    diagrams = project.getDiagrams()
    
    for diagram in diagrams:
        diagram_element = diagram.getElement()
        
        if (diagram_element is not None and 
            diagram_element.getOwner() == owner_element and 
            diagram.getDiagramType().getType() == diagram_type):
            
            diagram_name = diagram_element.getName()
            if diagram_name is not None and ("_" + name_prefix) in diagram_name:
                try:
                    mem.getInstance().removeElement(diagram_element)
                    found = True
                except Exception as e:
                    printer("Error deleting existing diagram: " + str(e))
                break  # Only delete the first matching diagram
    
    return found


def find_sys_ports(element):
    ports = {}
    for ele in element.getOwnedElement():
        if ele.getHumanType() != "Part Property":
            continue
        if "System" in find_generalization_classes(ele.getType()):
            op = ele.getType().getOwnedPort()
            for p_ in op:                    
                ports[p_] = {
                        'name' : p_.getName(),
                        'prop_path' : tuple([ele, p_]),
                        'owning_sys' : ele.getType(),
                    }
    return ports

def find_or_create_fidl2_port(element, port_name):
    owned_ports = element.getOwnedPort() or []
    p_n_fidl2 = "FIDL2: " + port_name
    port_ret = None
    for p_ in owned_ports:
        if p_n_fidl2 == p_.getName():
            port_ret = p_
            break

    if port_ret is None:
        port_ret = create_fidl2_port(element,port_name)

    return port_ret

def find_cross_system_ports(site_block, classifiers_list, nc_list):
    connectors_ob = []
    ob_cons = find_nested_connectors(site_block, connectors_ob, False)
    pure_part = []
    
    for conn in ob_cons:
        new_conn = dict(conn)
        new_conn['connection_info'] = dict(conn['connection_info'])
        new_conn['connection_info']['connected_elements'] = conn['connection_info']['connected_parts']
        pure_part.append(new_conn)

    element_ob_d_pp = combine_part_dicts(pure_part)
    con_ob_pairs = [ob_con['connection_info']['connected_elements'] for ob_con in pure_part]
    ob_results = traverse_connections(con_ob_pairs, element_ob_d_pp, classifiers_list, nc_list)

    return ob_results

def find_common_el(var1, var2, selectedElement):
    common_el = None
    if not isinstance(var1, tuple):
        common_el = var1.getOwner()
    elif not isinstance(var2, tuple):
        common_el = var2.getOwner()
    else:
        for item in var1:
            if item in var2:
                common_el  = item
                if isinstance(common_el, Property):
                    common_el = common_el.getType()
        if not common_el:
            if var1[0].getOwner() == var2[0].getOwner():
                common_el = var1[0].getOwner()
            else:
                common_el = climb_el(var1[0],var2[0], selectedElement)
    return common_el

def find_stereotype(stereotype_name, top_package, project):
    for s in sh.getAllStereotypes(project):
        if s.getName() == stereotype_name and top_package in get_recursive_owner_array(s):
            return s
        
#climbs the owner chain of an element until it hits the model


def get_con_end_info(connector):
        all_data = {}
        connected_elements = []
        connected_parts = []
        element_dict = {}
        part_dict = {}
        for end in connector.getEnd():
            end_info = {}
            nested_c = False
            chain = []
            classifiers = []
            prop_path = []
            part_desc = None
            b_port_check = False
            sys_owner = None
            for x in sh.getStereotypes(end):
                if x.getName() == "NestedConnectorEnd":
                    chain = sh.getTaggedValue(end, "propertyPath").getValue()
                    connected_parts.append(end.getRole())
                    if "port" not in end.getRole().getHumanType().lower():
                        chain = chain + [end.getRole()]
                    
                    if "System" in find_generalization_classes(end.getRole().getOwner()):
                        b_port_check = True
                        sys_owner = end.getRole().getOwner()

                    classifiers = find_classifiers(chain[-1].getType())
                    part_desc = get_classifier_description(chain[-1].getType())

                    connected_elements.append(chain[-1])
                    end_identifier = chain[-1]
                    prop_path = chain
                    nested_c = True
                    full_name = ".".join([c.getName() for c in chain])

            if not nested_c:
                prop_path = [end.getRole()]
                connected_elements.append(end.getRole())
                connected_parts.append(end.getRole())
                end_identifier = end.getRole()
                if "port" not in end_identifier.getHumanType().lower():
                    classifiers = find_classifiers(end.getRole().getType())
                    part_desc = get_classifier_description(end.getRole().getType())
                else:
                    classifiers = end_identifier.getHumanType()
                    if "System" in find_generalization_classes(end.getRole().getOwner()):
                        b_port_check = True
                        sys_owner = end.getRole().getOwner()
                full_name = end.getRole().getName()

            end_info = {
                    'prop_path' : prop_path,
                    'classifiers' : classifiers,
                    'full_name' : full_name,
                    'is_boundary_port': b_port_check,
                    'sys_owner': sys_owner,
                    'part_desc': part_desc
                }
            element_dict[end_identifier] = end_info
            part_dict[end.getRole()] = end_info

        all_data = {
            'connected_elements': connected_elements,
            'element_dict': element_dict,
            'connected_parts': connected_parts,
            'part_dict' : part_dict
        }   
        return all_data

    


def get_part_prop_chain(topLevelElement, target):
    path = []

    def recurse(current_element, trail):
        if hasattr(current_element, 'getOwnedMember'):
            for sub in current_element.getOwnedMember():
                if sub.getHumanType() == "Part Property":
                    if sub == target:
                        path.extend(trail + [sub])
                        return True
                    next_type = sub.getType()
                    if recurse(next_type, trail + [sub]):
                        return True
        return False
    
    recurse(topLevelElement,[])
    return path

def collect_all_pes_with_leaf_check(pe, seen, level):
    result = []
    subtree = []

    def recurse(pe, level):
        if pe in seen:
            return
        seen.add(pe)

        if pe.isVisible() and isinstance(pe, PartView):
            subtree.append((pe, level))

        for child in pe.getPresentationElements():
            next_level = level + 1 if isinstance(pe, PartView) else level
            recurse(child, next_level)

    # Traverse one root tree
    recurse(pe, 0)

    def is_leaf(pv):
        def has_child_partview_recursive(pe):
            for child in pe.getPresentationElements():
                if isinstance(child, PartView) and child.isVisible():
                    return True
                if has_child_partview_recursive(child):
                    return True
            return False

        return not has_child_partview_recursive(pv)
    
    for pe, lvl in subtree:
        result.append({
            "PE": pe,
            "Level": lvl,
            "Max_Depth": is_leaf(pe)
        })

    return result

def get_property_values(element, property_list):
    prop_values = {}
    good_props = []

    for prop in element.getAttribute():
        if prop.getName() in property_list:
            prop_values[prop.getName()] = prop.getDefaultValue().getValue()
            good_props.append(prop.getName())

    missing_props = list(set(property_list) - set(good_props))
    if missing_props:
        for prop in element.getInheritedMember():
            if prop.getName() in missing_props:
                prop_values[prop.getName()] = prop.getDefaultValue().getValue()

    return prop_values
# endregion

'''
creation functions
'''
# region
def create_timestamped_diagram(diagram_type, owner_element, name_prefix):
    """
    Creates a diagram with timestamped name to avoid conflicts
    
    :param diagram_type: The type of diagram to create
    :param owner_element: The element that will own the diagram
    :param name_prefix: The prefix for the diagram name
    :return: The created diagram element
    """
    new_diagram = mem.getInstance().createDiagram(diagram_type, owner_element)
    
    timestamp_format = SimpleDateFormat("yyyyMMdd_HHmmss")
    timestamp = timestamp_format.format(Date())
    owner_name = owner_element.getName()
    diagram_name = owner_name + "_" + name_prefix + " " + timestamp
    
    new_diagram.setName(diagram_name)
    return new_diagram

def createSimpleNestedConnections(connectionOwner,sourcePart,targetPart):
    connector = Application.getInstance().getProject().getElementsFactory().createConnectorInstance()
    conEndStereotype = SysMLProfile.getInstance(connector).getNestedConnectorEnd()

    if isinstance(sourcePart, tuple):
        sourceElement = sourcePart[-1]
        parentParts = sourcePart[:-1]
        mh.setClientElement(connector, sourceElement)
        end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
        end1 = mh.getFirstEnd(connector)
        sh.addStereotype(end1,end)
        sh.setStereotypePropertyValue(end1, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parentParts)
    else:
        mh.setClientElement(connector,sourcePart)

    if isinstance(targetPart, tuple):
        targetElement = targetPart[-1]
        parentParts = targetPart[:-1]
        mh.setSupplierElement(connector, targetElement)
        end = SysMLProfile.getInstance(connector).getNestedConnectorEnd()
        end2 = mh.getSecondEnd(connector)
        sh.addStereotype(end2,end)
        sh.setStereotypePropertyValue(end2, end, SysMLProfile.ELEMENTPROPERTYPATH_PROPERTYPATH_PROPERTY, parentParts)
    else:
        mh.setSupplierElement(connector,targetPart)
    
    connector.setOwner(connectionOwner)
    return connector

def create_fidl2_port(element, port_name):
    new_port = project.getElementsFactory().createPortInstance()
    new_port.setOwner(element)
    new_port.setName("FIDL2: " + port_name)
    return new_port

def populate_connector_stereotype_values(connector, cable_part_property, stereotype):
    prop_names = ["cable__cable_type__name", "cable__length", "cable__length_unit__name", "cable__network_speed"]
    prop_values = get_property_values(cable_part_property.getType(), prop_names)
    #this part could use some work
    if prop_values["cable__cable_type__name"] is not None and prop_values["cable__cable_type__name"] != "-":
        sh.setStereotypePropertyValue(connector, stereotype, "cableType", prop_values["cable__cable_type__name"])
    if prop_values["cable__length"] is not None and prop_values["cable__length"] != "-" and prop_values["cable__length_unit__name"] is not None and prop_values["cable__length_unit__name"] != "-":
        cable_length = prop_values["cable__length"] + "_" + prop_values["cable__length_unit__name"]
        sh.setStereotypePropertyValue(connector, stereotype, "cableLength", cable_length)
    if prop_values["cable__network_speed"] is not None and prop_values["cable__network_speed"] != "-":
        sh.setStereotypePropertyValue(connector, stereotype, "transmissionSpeed", prop_values["cable__network_speed"])
# endregion

'''
data manipulation functions
'''
# region
def combine_el_dicts(dicts):
    combined_dict = {}
    for sub_dict in dicts:
        element_dict = sub_dict['connection_info'].get(('element_dict'),{})
        combined_dict.update(element_dict)

    return combined_dict

def combine_part_dicts(dicts):
    combined_dict = {}
    for sub_dict in dicts:
        part_dict = sub_dict['connection_info'].get(('part_dict'),{})
        combined_dict.update(part_dict)

    return combined_dict

def html_table_builder_1_col(items):
    html = ['<html>', '<body>',
        '<table border="1" cellpadding="4" cellspacing="0" '
        'style="border-collapse:collapse;">',
        # header row
        '<tr><th>Removed Items</th></tr>']
    for it in items:
        html.append('<tr><td>%s</td></tr>' % str(it))
    html.extend(['</table>', '</body>', '</html>'])
    return '\n'.join(html)

# endregion

'''
'graph' style function cluster
'''
# region
def find_all_ends(start, adjacency, connector_end_dict, classifiers_list, visited_edges, nc_list):
    stack = [(start, start, set([start]), None)]  # (origin, current, visited_path, cable)
    results = []
    cable_map = {}

    while stack:
        origin, current, visited, cable_node = stack.pop()

        for neighbor in adjacency.get(current, []):
            if neighbor in visited:
                continue
            visited_new = visited | {neighbor}

            edge_key = tuple(sorted((origin, neighbor)))
            if edge_key in visited_edges:
                continue

            visited_edges.add(edge_key)

            if "Cable" in connector_end_dict[neighbor]['classifiers']:
                stack.append((origin, neighbor, visited_new, neighbor))
            elif any(classifier in connector_end_dict[neighbor]['classifiers'] for classifier in classifiers_list) or any(nc.lower() in connector_end_dict[neighbor]['full_name'].lower() for nc in nc_list):
                stack.append((origin, neighbor, visited_new, cable_node))
            else:
                if connector_end_dict[origin]['sys_owner'] and connector_end_dict[neighbor]['sys_owner']:
                    if connector_end_dict[origin]['sys_owner'] == connector_end_dict[neighbor]['sys_owner']:
                        continue

                key = (origin, neighbor)
                results.append(key)
                cable_map[key] = cable_node
                #results.append((origin, neighbor, cable_node))

    return results, cable_map

def traverse_connections(connections, connector_end_dict, classifiers_list, nc_list):
    adjacency = {}

    # Build undirected graph
    for node1, node2 in connections:
        adjacency.setdefault(node1, set()).add(node2)
        adjacency.setdefault(node2, set()).add(node1)

    results = []
    cables = {}
    seen_starts = set()
    visited_edges = set()

    for node in adjacency:
        # Only start traversal if node is not in classifiers list and not visited yet
        if node in seen_starts:
            continue
        if any(classifier in connector_end_dict[node]['classifiers'] for classifier in classifiers_list) or any(nc.lower() in connector_end_dict[node]['full_name'].lower() for nc in nc_list):
            continue

        ends, cable_map = find_all_ends(node, adjacency, connector_end_dict, classifiers_list, visited_edges, nc_list)
        results.extend(ends)
        cables.update(cable_map)
        seen_starts.add(node)

    if not results and not cables:
        return None

    return results, cables

def climb_el(el1, el2, topLevelElement):
    p1 = get_part_prop_chain(topLevelElement, el1)
    p2 = get_part_prop_chain(topLevelElement, el2)
    common_el = next((a for a, b in zip(p1, p2) if a == b), None)
    common_el = common_el.getType()
    return common_el
# endregion

'''
diagram manipulation functions
'''
# region
def remove_classifier_from_presentation_element(presentationElement):
    o_prop = presentationElement.getProperty("SHOW_OBJECT_CLASS")
    c_prop = o_prop.clone()
    c_prop.setValue(False)
    pm = PropertyManager()
    pm.addProperty(c_prop)
    PresentationElementsManager.getInstance().setPresentationElementProperties(presentationElement,pm)


#creates the actual FID diagram
def fidCreator_v1(selectedElement, level_var, disp_cons_list, model_parts, show_ports):
    sm.getInstance().createSession(project,'New IBD')
    if isBlock(selectedElement):
        # Delete any existing FID L2 diagram for this element
        find_and_delete_existing_diagram(project, selectedElement, SysMLConstants.SYSML_INTERNAL_BLOCK_DIAGRAM, "FID_L2")
        
        # Create new timestamped diagram
        newIbd = create_timestamped_diagram(SysMLConstants.SYSML_INTERNAL_BLOCK_DIAGRAM, selectedElement, "FID_L2")
        ibd_ = project.getDiagram(newIbd)
        ibd__ = ibd_.getDiagram()

        addToFID(selectedElement,ibd_, level_var, False, show_ports, True, False, model_parts)

        seen = set()
        all_pes = []

        pm_diagram = PropertyManager()
        diagram_prop = ibd_.getProperty(PropertyID.SHOW_DIAGRAM_INFO)
        n_diagram_prop = diagram_prop.clone()
        n_diagram_prop.setValue("True")
        pm_diagram.addProperty(n_diagram_prop)
        PresentationElementsManager.getInstance().setPresentationElementProperties(ibd_, pm_diagram)
        
        for pe in ibd_.getPresentationElements():
            if isinstance(pe, PartView) and pe not in seen:
                all_pes.extend(collect_all_pes_with_leaf_check(pe, seen, 0))
            if isinstance(pe, DiagramPropertiesShape):
                diagram_prop_pe = pe

        for connector in disp_cons_list:
            ends = connector.getEnd()
            if len(ends) !=2:
                continue

            source_role = ends[0].getRole()
            target_role = ends[1].getRole()

            source_shape = find_shape_element_for(source_role, ibd_)
            target_shape = find_shape_element_for(target_role, ibd_)

            if source_shape and target_shape:
                nc = PresentationElementsManager.getInstance().createPathElement(connector, source_shape,target_shape)
                '''
                show_name = nc.getProperty(PropertyID.SHOW_NAME)
                no_show_name = show_name.clone()
                no_show_name.setValue("False")
                pm_nc = PropertyManager()
                pm_nc.addProperty(no_show_name)
                PresentationElementsManager.getInstance().setPresentationElementProperties(nc, pm_nc)                
                '''
                
        ibd_.open()
        #app.getProject().getDiagram(newIbd).layout(True)

        for sel in all_pes:

            p_e = sel["PE"]
            class_view = p_e.getProperty(PropertyID.SHOW_OBJECT_CLASS)
            no_cv = class_view.clone()
            no_cv.setValue("False")
            pm = PropertyManager()
            pm.addProperty(no_cv)
            
            if sel["Max_Depth"] == True:
                min_h = 60
                min_w = 100
                
                change_el = sel["PE"]

                bounds = change_el.getBounds()
                new_bounds = Rectangle(bounds.x, bounds.y, 200,100)

                vis_st_disp = change_el.getProperty(PropertyID.STEREOTYPES_DISPLAY_MODE)
                vis_struct = change_el.getProperty(PropertyID.SUPPRESS_STRUCTURE)
                #class_view = change_el.getProperty(PropertyID.SHOW_OBJECT_CLASS)
                                
                if vis_st_disp.getValue() != "STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE":
                    #vis_struct.getValue() != "True":
                    new_vis = vis_st_disp.clone()
                    new_vis.setValue("STEREOTYPE_DISPLAY_MODE_SHAPE_IMAGE")
                    new_vis_2 = vis_struct.clone()
                    new_vis_2.setValue("True")
                    #pm = PropertyManager()
                    pm.addProperty(new_vis)
                    pm.addProperty(new_vis_2)

                    PresentationElementsManager.getInstance().setPresentationElementProperties(change_el, pm)
                    PresentationElementsManager.getInstance().reshapeShapeElement(change_el, new_bounds)
            
            if sel["Max_Depth"] == False:
                same_el = sel["PE"]
                fill_color = same_el.getProperty(PropertyID.FILL_COLOR)
                #pm = PropertyManager()
                if fill_color.getValue() != Color(255, 255, 255):
                    fc = fill_color.clone()
                    fc.setValue(Color(255,255,255))
                    pm.addProperty(fc)
                if sel["Level"] == 0:
                    border_color = same_el.getProperty(PropertyID.PEN_COLOR)
                    if border_color.getValue() != Color(0, 0, 255):
                        bc = border_color.clone()
                        bc.setValue(Color(0,0,255))
                        pm.addProperty(bc)

                PresentationElementsManager.getInstance().setPresentationElementProperties(same_el,pm)
        
        sm.getInstance().closeSession()     
        
        # Apply standard layout first
        app.getProject().getDiagram(newIbd).layout(True)

        try:
            layouter = OrderedHierarchicDiagramLayouter()
            options = HierarchicLayouterOptionsGroup()
            options.setOrientation("LEFT_TO_RIGHT")
            app.getProject().getDiagram(newIbd).layout(True, layouter, options)
        except Exception as ex:
            printer("Warning: Could not apply OrderedHierarchicDiagramLayouter with left-to-right orientation: " + str(ex))
            try:
                app.getProject().getDiagram(newIbd).layout(True, OrderedHierarchicDiagramLayouter())
            except Exception as ex2:
                printer("Warning: Could not apply default OrderedHierarchicDiagramLayouter: " + str(ex2))
        
        if diagram_prop_pe:
            newLocation = Point(50,50)
            PresentationElementsManager.getInstance().movePresentationElement(diagram_prop_pe,newLocation)
            #sh.
        return ibd_

    else:
        printer('ERROR: Please select a block element')
        sm.getInstance().closeSession()

def sort_by_host(part_props):
    """Sort part properties by their host asset."""
    sorted_props = {}
    for prop in part_props:
        host_asset = part_props[prop]["host_asset"]
        if host_asset not in sorted_props:
            sorted_props[host_asset] = []
        sorted_props[host_asset].append(part_props[prop])
    return sorted_props

def geolocate_(chunked_part_props, diagram_size, diagram):
    """Arrange elements by host asset in a grid layout."""
    diagram_len = diagram_size.getWidth()
    diagram_height = diagram_size.getHeight()

    blocks_per_row = 4  # Maximum blocks in a row
    block_spacing_x = 100
    block_spacing_y = 100
    column_spacing = 20
    row_spacing = 30
    columns_per_block = 2
    top_margin = 100
    left_margin = 100
    padding = 20

    current_x_offset = 0
    current_y_offset = top_margin
    row_max_height = 0
    block_counter = 0

    for chunk_key in chunked_part_props:
        items_list = chunked_part_props[chunk_key]

        col_heights = [0] * columns_per_block
        max_col_widths = [0] * columns_per_block
        arranged_positions = []

        for i, item in enumerate(items_list):
            pe = item["pe"]
            width = item["width"]
            height = item["height"]
            col = i % columns_per_block

            x_local = sum(max_col_widths[:col]) + col * column_spacing
            y_local = col_heights[col]

            arranged_positions.append((pe, x_local, y_local))
            col_heights[col] += height + row_spacing
            max_col_widths[col] = max(max_col_widths[col], width)

        block_width = sum(max_col_widths) + (columns_per_block - 1) * column_spacing
        block_height = max(col_heights)

        x_start = left_margin + current_x_offset
        y_start = current_y_offset

        for pe, x_local, y_local in arranged_positions:
            x = x_start + x_local
            y = y_start + y_local
            relocate_pe(pe, x, y)

        # Draw labeled separator box
        separator_x = x_start - padding
        separator_y = y_start - padding
        separator_width = block_width + padding * 2
        separator_height = block_height + padding * 2

        create_rectangular_separator(diagram, chunk_key, separator_x, separator_y, separator_width, separator_height)

        # Update X offset and max height in current row
        current_x_offset += block_width + block_spacing_x
        row_max_height = max(row_max_height, block_height + padding * 2)

        block_counter += 1

        # Wrap to new row every 4 blocks
        if block_counter % blocks_per_row == 0:
            current_x_offset = 0
            current_y_offset += row_max_height + block_spacing_y
            row_max_height = 0  # Reset for next row

def rearrange_items(selected_ibd):
    """Enhanced rearrangement using cabinet-based grouping with systemCabinetMap via fid_rearrange_demo extension"""
    
    part_props = {}
    connector_props = []
    pem = PresentationElementsManager.getInstance()
    diagram_size = selected_ibd.getBounds()

    element_count = 0
    for pe in selected_ibd.getPresentationElements():
        element_count += 1
        if pe.getHumanType() == "Part Property":
            part_props[pe] = {
                "name": pe.getElement().getName(),
                "presentation_element": pe,
                "element": pe.getElement(),
                "host_asset": get_host_asset(pe)
            }
        if pe.getHumanType() == "Connector":
            connector_props.append(pe)
    

    # Remove connectors temporarily
    for conn in connector_props:
        pem.deletePresentationElement(conn)

    
    try:
        if 'systemCabinetMap' in globals() and systemCabinetMap and len(part_props) > 0:
            
            # Using grouping functions from fid_rearrange_demo
            from fid_rearrange_demo import (get_cached_cabinet_properties, validate_grouping_with_cache, 
                                          create_cabinet_hierarchy_grouping, geolocate_elements_hierarchical)
            
            # Validate grouping using cached cabinet data
            validated_props = validate_grouping_with_cache(part_props, systemCabinetMap)
            
            # Create hierarchical grouping
            hierarchy = create_cabinet_hierarchy_grouping(validated_props)
            
            # Print grouping summary
            printer("Enhanced Grouping Summary:")
            for system, cabinets in hierarchy.items():
                printer("  System: {0} ({1} cabinets)".format(system, len(cabinets)))
                for cabinet, data in cabinets.items():
                    printer("    Cabinet: {0} - Room: {1} ({2} elements)".format(
                        cabinet, data["room"], len(data["elements"])))
                    
                    # Show traversal paths for hierarchically resolved elements
                    for element in data["elements"]:
                        if element.get("traversal_path"):
                            printer("      {0}: {1}".format(element["name"], element["traversal_path"]))
            
            # Apply hierarchical layout using extension
            geolocate_elements_hierarchical(hierarchy, diagram_size, selected_ibd)
            
        else:
            
            # Convert part_props format for basic grouping
            basic_part_props = {}
            for pe, prop_data in part_props.items():
                basic_part_props[pe] = {
                    "pe": pe,
                    "name": pe.getName(),
                    "bounds": pe.getBounds(),
                    "x": pe.getBounds().getX(),
                    "y": pe.getBounds().getY(),
                    "width": pe.getBounds().getWidth(),
                    "height": pe.getBounds().getHeight(),
                    "host_asset": prop_data["host_asset"]
                }
            
            # Fallback to original grouping method
            chunked = sort_by_host(basic_part_props)
            geolocate_(chunked, diagram_size, selected_ibd)
            
    except Exception as e:
        import traceback
        
        # Convert part_props format for fallback grouping
        basic_part_props = {}
        for pe, prop_data in part_props.items():
            basic_part_props[pe] = {
                "pe": pe,
                "name": pe.getName(),
                "bounds": pe.getBounds(),
                "x": pe.getBounds().getX(),
                "y": pe.getBounds().getY(),
                "width": pe.getBounds().getWidth(),
                "height": pe.getBounds().getHeight(),
                "host_asset": prop_data["host_asset"]
            }
        
        # Fallback to original grouping method
        chunked = sort_by_host(basic_part_props)
        geolocate_(chunked, diagram_size, selected_ibd)
    
    # Restore connectors
    redraw_connectors(connector_props, selected_ibd)

def addToFID(element, diagram, level, showClasses, showPorts, topLevel, connectors, connectedElements):
    if topLevel:
        for port in element.getOwnedPort():
            if port in connectedElements:
                df = diagram.getDiagramFrame()
                PresentationElementsManager.getInstance().createShapeElement(port, df, connectors)

    for part in element.getOwnedAttribute():
        if part not in connectedElements:
            continue
        if isPartProperty(part):
            if level >= 1:
                outer = PresentationElementsManager.getInstance().createShapeElement(part, diagram, connectors)
                if not showClasses:
                    if part.getType().getGeneral():
                        classifierName = part.getType().getGeneral().get(0).getName()
                        ###needs to be updated to support req
                        if classifierName in ["Transceiver","Cable","Connector"]:
                            remove_classifier_from_presentation_element(outer)
                if showPorts:
                    if part.getType().getOwnedPort():
                        for port in part.getType().getOwnedPort():
                                if port in connectedElements:
                                    out = PresentationElementsManager.getInstance().createShapeElement(port, outer, connectors)
                if level > 1:
                    addToFID(part.getType(), outer, level - 1, showClasses, showPorts, False, connectors, connectedElements)
            else:
                printer("Selected element is not supported, please try another.")
# endregion

#builds out the FID L2 elements and diagrams
def fid_l2_sys(element, site_connections, nested, build_diagram, disp_cons, disp_parts, site_level, depth = 10):
    safe_session_start("FID L2" + element.getName())

    fid_connector_stereotype = find_stereotype("FIDConnectorInfo","FID Connector Metadata", project)

    classifiers_list = ["Cable", "Connection", "Transceiver", "Connector"]
    nc_list = ["Patch Panel","Chassis"]

    connectors = []

    cons = find_nested_connectors(element, connectors, nested)

    connected_pairs = []
    connected_pairs = [con['connection_info']['connected_elements'] for con in cons]
    connection_names = [con['connection_name'] for con in cons if con['connection_name'] != None]
    fid_connections = [con['connection_metaname'] for con in cons if con.get('connection_stereotypes') and "FIDConnectorInfo" in [st.getName() for st in con['connection_stereotypes']]]

    initial_elements = {elem for pair in connected_pairs for elem in pair}

    display_cons = []
    element_dicts = combine_el_dicts(cons)
    boundary_ports = [el for el in element_dicts.values() if el['sys_owner']]

    results, cables = traverse_connections(connected_pairs, element_dicts, classifiers_list, nc_list)

    model_parts = set()
    common_el = None
    port_key = {}
    new_con_names = []

    if site_level:

        for pair in results:
            sys1_name = pair[0].getType().getName()
            sys2_name = pair[1].getType().getName()
            sys1_port_path = None
            sys2_port_path = None
            sys1 = pair[0].getType()
            sys2 = pair[1].getType()
            p1 = None
            p2 = None

            con_name = None
            cable_node = cables.get(pair, None)
            if cable_node:
                con_name = cable_node.getName()

            bp_ret = find_sys_ports(element)

            for x in bp_ret:
                if bp_ret[x]['owning_sys'] == sys1 and bp_ret[x]['name'] == ("FIDL2: " + sys2_name):
                    sys1_port_path = bp_ret[x]['prop_path']
                    p1 = x
                if bp_ret[x]['owning_sys'] == sys2 and bp_ret[x]['name'] == ("FIDL2: " + sys1_name):
                    sys2_port_path = bp_ret[x]['prop_path']
                    p2 = x

            if sys1_port_path and sys2_port_path:
                #con_name = sys2_name
                meta_con_name = "FIDL2_"+sys1_name+"__"+sys2_name
                if meta_con_name not in fid_connections and meta_con_name not in new_con_names:
                    newCon = createSimpleNestedConnections(element,sys1_port_path,sys2_port_path)
                    sh.addStereotype(newCon, fid_connector_stereotype)
                    sh.setStereotypePropertyValue(newCon, fid_connector_stereotype, "connectorMetaName", meta_con_name)
                    if cable_node:
                        populate_connector_stereotype_values(newCon, cable_node, fid_connector_stereotype)
                        newCon.setName(con_name)
                    display_cons.append(newCon)
                    new_con_names.append(meta_con_name)
                elif meta_con_name in new_con_names:
                    exist_con = next(c['connector_obj'] for c in cons if c.get('connection_metaname') == meta_con_name)
                    display_cons.append(exist_con)
                disp_parts.add(p1)
                disp_parts.add(p2)

    if not site_level:

        if boundary_ports and site_connections:
            
            sys_con_count = {}

            for pair in site_connections:
                node1 = pair[0]
                node2 = pair[1]
                if isinstance(node1, Port):
                    node1_owner = node1.getOwner().getName()
                else:
                    node1_owner = node1.getName()
                if isinstance(node2, Port):
                    node2_owner = node2.getOwner().getName()
                else:
                    node2_owner = node2.getName()
                
                if node1_owner != element.getName() and node2_owner != element.getName():
                    continue
                if node1.getOwner().getName() == element.getName():
                    _port = node1
                    sys_end = node2_owner
                else:
                    _port = node2
                    sys_end = node1_owner

                port_key[_port] = sys_end
                if sys_end not in sys_con_count and sys_end != element.getName():
                    sys_con_count[sys_end] = {'count': 0}
                if sys_end != element.getName():
                    sys_con_count[sys_end]['count'] += 1

            for sys in sys_con_count.keys():
                sys_con_count[sys]['port'] = find_or_create_fidl2_port(element, sys)

        for pair in results:

            con_name = None
            cable_node = cables.get(pair, None)
            if cable_node:
                con_name = cable_node.getName()

            p1_name = element_dicts[pair[0]]['full_name']
            p2_name = element_dicts[pair[1]]['full_name']

            if port_key and pair[0] in port_key:
                    model_parts.add(sys_con_count[port_key[pair[0]]]['port'])
                    path1 = sys_con_count[port_key[pair[0]]]['port']
                    p1_name = port_key[pair[0]]
            
            elif len(element_dicts[pair[0]]['prop_path']) > 1: 
                path1 = tuple(element_dicts[pair[0]]['prop_path'])
                for el in element_dicts[pair[0]]['prop_path']:
                    model_parts.add(el)
            else:
                path1 = element_dicts[pair[0]]['prop_path'][0]
                model_parts.add(path1)

            if port_key and pair[1] in port_key:
                    model_parts.add(sys_con_count[port_key[pair[1]]]['port'])
                    path2 = sys_con_count[port_key[pair[1]]]['port']
                    p2_name = port_key[pair[1]]

            elif len(element_dicts[pair[1]]['prop_path']) > 1: 
                path2 = tuple(element_dicts[pair[1]]['prop_path'])
                for el in element_dicts[pair[1]]['prop_path']:
                    model_parts.add(el)
            else:
                path2 = element_dicts[pair[1]]['prop_path'][0]
                model_parts.add(path2)

            common_el = find_common_el(path1, path2, element)
            if not common_el: 
                common_el = element

            #con_name = p2_name
            meta_con_name = "FIDL2_"+p1_name+"__"+p2_name
            if meta_con_name not in fid_connections and meta_con_name not in new_con_names:
                newCon = createSimpleNestedConnections(common_el,path1,path2)
                sh.addStereotype(newCon, fid_connector_stereotype)
                sh.setStereotypePropertyValue(newCon, fid_connector_stereotype, "connectorMetaName", meta_con_name)
                if cable_node:
                    populate_connector_stereotype_values(newCon, cable_node, fid_connector_stereotype)
                    newCon.setName(con_name)
                display_cons.append(newCon)
                new_con_names.append(meta_con_name)
            elif meta_con_name not in new_con_names:
                exist_con = next(c['connector_obj'] for c in cons if c.get('connection_metaname') == meta_con_name)
                display_cons.append(exist_con)

    disp_cons.extend(display_cons)
    disp_parts.update(model_parts)

    sm.getInstance().closeSession()

    if build_diagram:

        removed_elements = initial_elements - disp_parts

        removed_elements_named = [get_name(e) for e in removed_elements]

        table = html_table_builder_1_col(removed_elements_named)

        if site_level:
            fid = fidCreator_v1(element, depth, disp_cons, disp_parts, True)
        else:
            fid = fidCreator_v1(element, depth, disp_cons, disp_parts, False)
        
        # Apply rearrangement after IBD creation
        if fid:
            sm.getInstance().createSession("Rearranging IBD Elements")
            try:
                rearrange_items(fid)
                sm.getInstance().closeSession()
            except Exception as e:
                printer("Error during rearrangement: " + str(e))
                sm.getInstance().closeSession()

        comments = fid.getModelElement().getOwnedComment()
        if comments:
            comm = comments[0]
        else:
            nc = Application.getInstance().getProject().getElementsFactory().createCommentInstance()
            nc.setBody("Removed Elements")
            fid.getModelElement().getOwnedComment().add(nc)
            comm = nc
        CoreHelper.setComment(comm, table)

    return disp_cons, disp_parts

# Main function
def fid_l2_nested(selectedElement, depth = 10):
    sm.getInstance().createSession(project,"FID L2 _ Nested")

    gen_classes = find_generalization_classes(selectedElement)

    classifiers_list = ["Cable", "Connection", "Transceiver", "Connector"]
    nc_list = ["Patch Panel","Chassis"]

    target_els = {}
    all_cons = []
    all_parts = set()

    if "Site" in gen_classes:
        t_cs = find_nested_elements(selectedElement, target_els, True)
        t_cs[selectedElement] = {
            "gen_classes": gen_classes,
            "part": selectedElement
            }
        ss_cons = find_cross_system_ports(selectedElement, classifiers_list, nc_list)

        for sys in t_cs:
            if "System" in t_cs[sys]['gen_classes']:
                disp_cons, disp_parts = fid_l2_sys(sys, ss_cons, True, True, [], set(), False)
                all_cons.extend(disp_cons)
                all_parts.update(disp_parts)
                all_parts.add(t_cs[sys]['part'])

        for site in t_cs:
            if "Site" in t_cs[site]['gen_classes']:
                fid_l2_sys(site, ss_cons, False, True, all_cons, all_parts, True)

    elif "System" in gen_classes:
        assoc_ob = find_association_owning_block(selectedElement)
        ss_cons = find_cross_system_ports(assoc_ob, classifiers_list, nc_list)
        fid_l2_sys(selectedElement, ss_cons, True, True, all_cons, all_parts, False)

    else:
        printer("Neither System nor Site selected. FIDL2 not generated.")
        sm.getInstance().closeSession()