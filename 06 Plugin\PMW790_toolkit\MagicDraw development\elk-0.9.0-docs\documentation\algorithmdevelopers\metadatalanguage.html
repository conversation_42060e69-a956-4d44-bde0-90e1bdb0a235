<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>ELK Metadata Language (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../documentation.html">Documentation <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>ELK Metadata Language</h1>

    <p>As described <a href="../../documentation/tooldevelopers/usingplainjavalayout.html">in other parts of the documentation</a>, the <em>Eclipse Layout Kernel</em> relies on metadata about all available layout algorithms and the layout options they support. Supplying metadata for your layout algorithm is done by writing an <em>ELK Metadata File</em> in our textual metadata language. The file is used by the ELK SDK to generate the following Java classes:</p>
<ul>
<li>An <code>ILayoutMetaDataProvider</code> that contains <code>IProperty</code> objects for each layout option you declare, along with a method that registers these options and layout algorithm categories with the <code>LayoutMetaDataService</code>. It is this class that needs to be registered through the <code>META-INF/services/ILayoutMetaDataProvider</code> file.</li>
<li>One <code>ILayoutMetaDataProvider</code> for each algorithm you declare. This contains one <code>IProperty</code> object for each layout option your algorithm supports (with the configured default value), as well as a method that registers your algorithm and its supported options with the <code>LayoutMetaDataService</code>. You should use the <code>IProperty</code> objects in this class to retrieve layout option values to ensure that you get correct defaults if an option is not set. These classes do not have to be registered with ELK&rsquo;s extension point because they are automatically registered through the main metadata class.</li>
</ul>
<p>This page explains how to write a metadata file. See the end of the page for an example metadata file of a very simple layout algorithm. (Pro-tip: Reading the previous sentence in action-movie-trailer-narrator voice makes it more compelling.)</p>
<h2 id="creating-and-registering-your-metadata-file">Creating and Registering Your Metadata File</h2>
<p>Follow these steps to add a metadata file to your layout algorithm project:</p>
<ol>
<li>
<p>Right-click your project&rsquo;s base package (which should have the same name as your plug-in project), and click <em>New - File</em>. Note that the base package cannot be empty (of course, you would never use the default empty package anyway since that&rsquo;s bad style now, would you&hellip;).</p>
</li>
<li>
<p>Give your file a sensible name (which is usually the name of your algorithm), and add the file extension <code>.melk</code>. Click <em>Finish</em>.</p>
</li>
<li>
<p>If Eclipse asks you whether you want to add the <em>Xtext nature</em> to your project, click <em>Yes</em>.</p>
</li>
<li>
<p>The ELK SDK needs some basic information in your file to generate your
<code>ILayoutMetaDataProvider</code>:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">package package.the.melk.file.is.in

bundle {
    // Change the following line according to what you want the
    // package and name of your meta data provider to be
    metadataClass options.MetaDataProvider
}
</code></pre><p>See below for details on what this is doing.</p>
</li>
<li>
<p>Add a folder <code>META-INF/services</code> inside one of your project&rsquo;s source folders (by default, we put it right inside of the <code>src</code> folder).</p>
</li>
<li>
<p>In that folder, create a file named <code>ILayoutMetaDataProvider</code>.</p>
</li>
<li>
<p>In that file, add a single line of text which contains the fully qualified name of the <code>ILayoutMetaDataProvider</code> that was generated for you by the ELK SDK.</p>
</li>
<li>
<p>Save and close the editor.</p>
</li>
</ol>
<h2 id="the-metadata-language">The Metadata Language</h2>
<p>The ELK metadata language contains everything you need to make the following information available to ELK:</p>
<ul>
<li>Which layout algorithms your plug-in provides, and which classes implement them.</li>
<li>Which layout options your plug-in contributes, and which layout options your algorithms support.</li>
<li>Which layout algorithm categories your plug-in contributes, and which of them your algorithms belong to.</li>
</ul>
<p>The basic layout of a metadata file looks something like this:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-plain" data-lang="plain"><span style="display:flex;"><span>&lt;package declaration&gt;
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>&lt;imports section&gt;
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>&lt;bundle information&gt;
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>&lt;metadata declarations&gt;
</span></span></code></pre></div><p>The package declaration and imports section work the same way as they do in Java: the package declaration indicates where the metadata file lies, and the imports import classes used in the file&rsquo;s declarations.The rest of this page will walk you through the remaining parts.</p>
<p><strong>Note:</strong> Feel free to use comments. The metadata language supports Java-style single-line comments (<code>//</code>) and multi-line comments (<code>/* */</code>).</p>
<h3 id="bundle-information">Bundle Information</h3>
<p>To be able to turn your metadata file into an <code>ILayoutMetaDataProvider</code>, ELK needs some basic information. This is what the bundle information section is all about. Here&rsquo;s what it would look like for a typical metadata file:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">/**
 * An optional bunch of Javadoc that will end up as the Javadoc for the
 * generated class.
 */
bundle {
    // The sub-package and name of the ILayoutMetaDataProvider that will be
    // generated for you. This is required for the generation process to
    // work.
    metadataClass properties.AwesomeOptions

    // A prefix that will automatically be added to the IDs of new layout
    // options declared in this metadata file. To save your a bit of typing.
    // Not strictly required, but highly recommended.
    idPrefix org.eclipse.elk.awesome
}
</code></pre><h3 id="options">Options</h3>
<p>While <a href="../../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">this section of our documentation</a> describes what layout options are and how to use them, this section focuses on making their mere existence known to the world. Doing so will cause the ELK SDK to generate <code>IProperty</code> constants in your generated <code>ILayoutMetaDataProvider</code> that you can use <a href="../../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">as described</a> to set layout option values. An option declaration can become rather complex, so here&rsquo;s what it generally looks like (we will go into the missing details below):</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">// The option&#39;s ID, appended to the idPrefix declared in the bundle information
&lt;deprecated&gt; &lt;modifier&gt; option myLayoutOptionId : &lt;type&gt; {
    // A user-readable label for the layout option
    label &#34;Wobblyness Factor&#34;

    // A short user-readable description. This will also become the property
    // constant&#39;s Javadoc comment.
    description
        &#34;Controls the overall wobblyness of edges when connected
         to wobbles.&#34;

    // An optional default value used when the option is not configured
    // for a given graph. Of course, the given expression must evaluate
    // to the layout option&#39;s type as specified above.
    default = Direction.UNDEFINED

    // Optional lower and upper bounds option values must satisfy to be
    // valid. These must be comparable to the layout option&#39;s type as
    // specified above.
    lowerBound = 3
    upperBound = 5

    // Which graph elements the option can be meaningfully applied to.
    targets &lt;targets&gt;

    // An optional list of an arbitrary number of alternate IDs your
    // layout option should be accessible under. You should probably
    // not use this. We introduced this feature to ease the process of
    // transitioning when our project became the Eclipse Layout Kernel,
    // which caused all layout option IDs to change.
    legacyIds the.first.old.id.of.our.option,
              the.second.old.id.of.our.option

    // Some layout options only make sense if another option has a certain
    // value. Add a &#39;requires&#39; declaration to each such dependency.
    requires some.other.layout.option.id == 42
}
</code></pre><p>Let&rsquo;s go through what the sample code didn&rsquo;t explain:</p>
<ul>
<li>
<p><code>&lt;deprecated&gt;</code></p>
<p>Prepending the option declaration with the <code>deprecated</code> keyword will mark the generated property constant as being deprecated in its Javadoc comment.</p>
</li>
<li>
<p><code>&lt;modifier&gt;</code></p>
<p>Option declarations support the following modifiers, of which one can be used:</p>
<table>
<thead>
<tr>
<th>Modifier</th>
<th>Meaning</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>advanced</code></td>
<td> Advanced layout options are not shown in the <em>Layout</em> view by default. The user has to explicitly instruct the view to show advanced layout options.</td>
</tr>
<tr>
<td><code>programmatic</code></td>
<td>Programmatic layout options are not shown in the <em>Layout</em> view at all, but only set through programmatic layout configuration. The reason is often that they are either highly specialized, or that values of their data type cannot be properly entered in the layout view.</td>
</tr>
<tr>
<td><code>output</code></td>
<td>Output options are not really layout options at all, but are used by layout algorithms to annotate the input graph with further layout information that could not be returned otherwise. Not being input options at all, these options are of course not shown in the layout view.</td>
</tr>
<tr>
<td><code>global</code></td>
<td>A layout option that is global can be understood as a layout option that guides ELK&rsquo;s implementation of the layout process rather than influencing what a single layout algorithm does. You should not need to declare global options.</td>
</tr>
</tbody>
</table>
</li>
<li>
<p><code>&lt;type&gt;</code></p>
<p>The data type of a layout option. This can be any valid and visible Java type. Note that primitive types will be replaced by their object counterparts (for example, <code>int</code> will become <code>Integer</code>).</p>
</li>
<li>
<p><code>&lt;targets&gt;</code></p>
<p>Defines which graph elements a layout option can meaningfully be applied to. This mainly influences which layout options the <em>Layout</em> view shows depending on the currently selected diagram element. An option can have zero or more targets, defined as a comma-separated list. The following are valid targets:</p>
<table>
<thead>
<tr>
<th>Target</th>
<th>Meaning</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>parents</code></td>
<td>The option makes sense for nodes that contain further nodes themselves (<em>parent nodes</em>).</td>
</tr>
<tr>
<td><code>nodes</code></td>
<td>The option makes sense for nodes (which also includes parent nodes).</td>
</tr>
<tr>
<td><code>edges</code></td>
<td>The option makes sense for edges.</td>
</tr>
<tr>
<td><code>ports</code></td>
<td>The option makes sense for ports.</td>
</tr>
<tr>
<td><code>labels</code></td>
<td>The option makes sense for labels.</td>
</tr>
</tbody>
</table>
<p>Note that there is nothing that stops programmers from setting a port option on a node. All you are saying with your declaration is that doing so won&rsquo;t make any difference because you will only interpret the option when it is set on ports.</p>
</li>
</ul>
<h4 id="grouping-layout-options">Grouping Layout Options</h4>
<p>Everyone likes things to be neat and tidy. Well, alright, <em>we</em> like things to be neat and tidy, which is why related options can be grouped together:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">group thegroupname {
    // Your options and nested groups go here
}
</code></pre><p>Groups do not just make the metadata file more readable, but also have an effect on the identifiers of your options: all the names of groups your options are in will be placed between the option ID prefix and the option&rsquo;s actual name, thus reflecting the grouping in option IDs as well.</p>
<h4 id="a-note-on-enumerations">A Note on Enumerations</h4>
<p>Layout options may well have an enumeration value type. Just as layout options can be declared to be advanced, this may also be true for enumeration values. Take for example a layout algorithm that offers different implementations of a certain feature. The implementation to be used is specified through a layout option that enumerates all possibilities. However, let us assume that some of the implementations are not quite ready for the public. (Homework assignment: work out five reasons why this could be the case.) The corresponding enumeration values can be annotated to make this clear:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-java" data-lang="java"><span style="display:flex;"><span><span style="color:#66d9ef">public</span> <span style="color:#66d9ef">enum</span> Strategy <span style="color:#f92672">{</span>
</span></span><span style="display:flex;"><span>    WORKS_ALWAYS<span style="color:#f92672">,</span>
</span></span><span style="display:flex;"><span>    WORKS_ON_SUNDAYS<span style="color:#f92672">,</span>
</span></span><span style="display:flex;"><span>    <span style="color:#a6e22e">@AdvancedPropertyValue</span>
</span></span><span style="display:flex;"><span>    WORKS_IF_YOU_ARE_CAREFUL<span style="color:#f92672">,</span>
</span></span><span style="display:flex;"><span>    <span style="color:#a6e22e">@ExperimentalPropertyValue</span>
</span></span><span style="display:flex;"><span>    WORKS_IN_EXPERIMENTAL_CONDITIONS
</span></span><span style="display:flex;"><span><span style="color:#f92672">}</span>
</span></span></code></pre></div><h3 id="categories">Categories</h3>
<p>Each layout algorithm can be assigned to a layout algorithm category. The categorization is for example used by the layout algorithm selection dialog or the documentation. While ELK already provides a number of predefined categories, this is how you would define your own:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">// The category&#39;s ID, appended to the idPrefix declared in the bundle information
category awesome {
    // A short category label that can be shown to users.
    label &#34;Awesome Algorithms&#34;

    description
        &#34;A not-too-long but totally meaningful description of what this category
         of layout algorithms is all about. Will be shown to users.&#34;
}
</code></pre><h3 id="algorithms">Algorithms</h3>
<p>Of course, everything we have done so far was simply in support of the main act: declaring the layout algorithms you have worked hard to develop (just to find out that there&rsquo;s hundreds of little details you got wrong; we&rsquo;ve been there&hellip;). Here&rsquo;s what an algorithm declaration looks like:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">// The algorithm&#39;s ID
algorithm algorithmid(&lt;class&gt;) {
    // User-readable short name and description for the algorithm
    label &#34;Excellent Algorithm&#34;

    description
        &#34;A brief and completely sensible description of what this algorithm
         does. Will be shown to users.&#34;

    // Optional name of the ILayoutMetaDataProvider class generated for this
    // algorithm
    metadataClass &lt;metadataClass&gt;

    // Optional validator class called by ELK before actually running the
    // layout algorithm.
    validator &lt;validatorClass&gt;

    // Optional path to an image that provides a preview of the kinds of
    // layouts this algorithm will produce. Relative to the plug-in&#39;s
    // root folder.
    preview path/to/preview/image.png

    // Optional ID of a category of layout algorithms this algorithm belongs to.
    category the.category.id

    // List of graph features this algorithm explicitly supports (see below)
    features &lt;features&gt;

    // Declarations for each layout option this algorithm supports (see below)
    &lt;supportedOptions&gt;
}
</code></pre><p>Three details deserve more explanation:</p>
<ul>
<li>
<p><code>class</code></p>
<p>The main class that provides the entry to your layout algorithm. This must be a subclass of <code>AbstractLayoutProvider</code> and may have to be imported in your metadata file&rsquo;s import section. <code>AbstractLayoutProvider</code> has an <code>initialize(String)</code> method that expects a <code>String</code> parameter to customize its behavior. You can define that parameter&rsquo;s value for your algorithm like this:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">algorithm myAlgorithm(TheAbstractLayoutProviderSubclass#TheParamterValue)
</code></pre></li>
<li>
<p><code>metadataClass</code></p>
<p>The metadata class will contain the property constants you should use to access your algorithm&rsquo;s property values. If no name is specified, a class with a default name will be generated in the package specified by the metadata file. <em>This class name must be distinct from any other metadata class name, both for algorithms and for the bundle itself.</em></p>
</li>
<li>
<p><code>validatorClass</code></p>
<p>Layout algorithms may have certain requirements concerning the graphs they are run on, including connectivity, hierarchy, or simlpy configuration. Instead of checking the input graph&rsquo;s validity in the algorithm itself, a validator class can be specified that is run automatically when layout is invoked through the <code>DiagramLayoutEngine</code>. The class must implement the <code>org.eclipse.elk.core.validation.IValidatingGraphElementVisitor</code> interface.</p>
</li>
<li>
<p><code>features</code></p>
<p>This is a comma-separated list of structural graph features that an algorithm explicitly supports. Most of these declarations are purely informational, but graph layout engines might decide to change the layout graph passed to your algorithm depending on whether it supports the original graph&rsquo;s features or not. At the time of writing, these are the possible graph features:</p>
<table>
<thead>
<tr>
<th>Feature</th>
<th>Meaning</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>clusters</code></td>
<td> <strong>TODO: Document</strong></td>
</tr>
<tr>
<td><code>compound</code></td>
<td>Whether an algorithm supports the layout of hierarchical graphs. If it does not, the ELK graph layout engine will only ever give the algorithm a single level of hierarchy to be laid out at a time, regardless of what the user requests.</td>
</tr>
<tr>
<td><code>disconnected</code></td>
<td>Whether an algorithm has explicit support for disconnected components (groups of nodes without connection to other groups of nodes).</td>
</tr>
<tr>
<td><code>edge_labels</code></td>
<td> Whether an algorithm knows how to place edge labels or not. A graph layout engine may decide to run a post-processing on your algorithm&rsquo;s result to place edge labels afterwards.</td>
</tr>
<tr>
<td><code>inside_self_loops</code></td>
<td>Whether an algorithm supports routing a self loop through the inside of a node instead of around it.</td>
</tr>
<tr>
<td><code>multi_edges</code></td>
<td>Whether an algorithm supports edges that have the same source and target.</td>
</tr>
<tr>
<td><code>ports</code></td>
<td>Whether an algorithm supports edges connected to nodes through explicit ports.</td>
</tr>
<tr>
<td><code>self_loops</code></td>
<td>Whether an algorithm supports self loops (edges that connect a node to itself).</td>
</tr>
</tbody>
</table>
</li>
<li>
<p><code>supportedOptions</code></p>
<p>Algorithms declare which layout options they support, either with their default values as originally declared or with overridden default values. Each supported option ends up as an <code>IProperty</code> constant in the algorithm&rsquo;s layout meta data provider, complete with correct default values. These constants should be used to retrieve option values from within the algorithm code.</p>
<p>An option support declaration looks like this:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">supports the.option.id          // Standard default value
supports the.option.id = 42     // Overridden default value
</code></pre></li>
</ul>
<h2 id="example">Example</h2>
<p>The following is an example metadata file for a very simple layout algorithm:</p>
<pre tabindex="0"><code class="language-melk" data-lang="melk">package cds.layout.simple

import cds.layout.simple.SimpleLayoutProvider
import org.eclipse.elk.core.math.ElkPadding

bundle {
    metadataClass options.SimpleMetaDataProvider
    idPrefix cds.layout.simple
}

option reverseInput : boolean {
    label &#34;Reverse Input&#34;
    description
        &#34;True if nodes should be placed in reverse order of their
        appearance in the graph.&#34;
    default = false
    targets parents
}

algorithm simple(SimpleLayoutProvider) {
    label &#34;Simple Layout Algorithm&#34;
    description
        &#34;This layout provider places nodes along a horizontal or a vertical line.&#34;
    metadataClass options.SimpleOptions

    supports reverseInput
    supports org.eclipse.elk.padding = new ElkPadding(10)
    supports org.eclipse.elk.spacing.edgeEdge = 5
    supports org.eclipse.elk.spacing.edgeNode = 10
    supports org.eclipse.elk.spacing.nodeNode = 10
}
</code></pre><p>Note how we override the default value of several properties for this algorithm.</p>

  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
        




  
  <a href="../../documentation/tooldevelopers.html">
    <li class="navlevel-1">
      Tool Developers
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/graphdatastructure.html">
    <li class="navlevel-2">
      Graph Data Structure
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/graphdatastructure/coordinatesystem.html">
    <li class="navlevel-3">
      Coordinate System
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">
    <li class="navlevel-3">
      Layout Options
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html">
    <li class="navlevel-3">
      Spacing Options
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/jsonformat.html">
    <li class="navlevel-3">
      JSON Format
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/graphdatastructure/elktextformat.html">
    <li class="navlevel-3">
      ELK Text Format
    </li>
  </a>
  


  

  
  <a href="../../documentation/tooldevelopers/usingalgorithmsdirectly.html">
    <li class="navlevel-2">
      Using Algorithms Directly
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingplainjavalayout.html">
    <li class="navlevel-2">
      Using Plain Java Layout
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout.html">
    <li class="navlevel-2">
      Using Eclipse Layout
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html">
    <li class="navlevel-3">
      Connecting to ELK
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html">
    <li class="navlevel-3">
      Advanced Configuration
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html">
    <li class="navlevel-3">
      Layout View Support
    </li>
  </a>
  

  
  <a href="../../documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html">
    <li class="navlevel-3">
      Dependency Injection
    </li>
  </a>
  


  


  

  
  <a href="../../documentation/algorithmdevelopers.html">
    <li class="navlevel-1">
      Algorithm Developers
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/gettingeclipseready.html">
    <li class="navlevel-2">
      Getting Eclipse Ready
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/creatinganewproject.html">
    <li class="navlevel-2">
      Creating a New Project
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/metadatalanguage.html">
    <li class="navlevel-2 active">
      ELK Metadata Language
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html">
    <li class="navlevel-3">
      Automatic Builds
    </li>
  </a>
  


  

  
  <a href="../../documentation/algorithmdevelopers/algorithmimplementation.html">
    <li class="navlevel-2">
      Algorithm Implementation
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html">
    <li class="navlevel-3">
      Structuring Algorithms
    </li>
  </a>
  


  

  
  <a href="../../documentation/algorithmdevelopers/algorithmdebugging.html">
    <li class="navlevel-2">
      Algorithm Debugging
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/randomgraphs.html">
    <li class="navlevel-2">
      Random Graph Generation
    </li>
  </a>
  

  
  <a href="../../documentation/algorithmdevelopers/unittesting.html">
    <li class="navlevel-2">
      Unit Tests
    </li>
  </a>
  


  

  
  <a href="../../documentation/contributors.html">
    <li class="navlevel-1">
      ELK Contributors
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/contributors/developmentsetup.html">
    <li class="navlevel-2">
      Development Setup
    </li>
  </a>
  

  
  <a href="../../documentation/contributors/developmentworkflow.html">
    <li class="navlevel-2">
      Development Workflow
    </li>
  </a>
  
    
    




  
  <a href="../../documentation/contributors/developmentworkflow/installingwithoomph.html">
    <li class="navlevel-3">
      Installing With Oomph
    </li>
  </a>
  


  

  
  <a href="../../documentation/contributors/buildingelk.html">
    <li class="navlevel-2">
      Building ELK
    </li>
  </a>
  


  


      
    
      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
