<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" type="image/svg+xml" href="img/elk_fav.svg">

    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/elk.css">
    <link rel="stylesheet" href="https://www.eclipse.org/elk/css/prism.css">

    <title>Automatic Builds (ELK)</title>

    
    
  </head>
  <body>

<nav class="navbar navbar-expand-lg navbar-dark">
  <button class="navbar-toggler navbar-toggler-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>
  <a class="navbar-brand" href="https://www.eclipse.org/elk/">
    <img src="img/elk_small_light.svg" height="30" class="d-inline-block align-top mr-1" alt="">
    Eclipse Layout Kernel&trade;
  </a>
  <div class="collapse navbar-collapse" id="navbarCollapse">
    <ul class="navbar-nav mr-auto">

      
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../downloads.html">Downloads</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../gettingstarted.html">Getting Started</a>
        </li>
      
        
        <li class="nav-item active">
          <a class="nav-link" href="../../../documentation.html">Documentation <span class="sr-only">(current)</span></a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../reference.html">Reference</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../support.html">Support</a>
        </li>
      
        
        <li class="nav-item">
          <a class="nav-link" href="../../../blog.html">Blog Posts</a>
        </li>
      

      <li class="nav-item">
        <a class="nav-link" href="https://github.com/eclipse/elk">GitHub</a>
      </li>

    </ul>
  </div>
</nav>


<div class="container px-3 py-5">


<div class="row">
  <div class="col-sm-9">
    <h1>Automatic Builds</h1>

    <p>Since <code>.melk</code> files result in code being generated, you may not want to check that code into your repository. Instead, the code should probably be generated as part of your automatic build. Indeed, the ELK metadata language compiler is available through a Maven repository. You can find the repository URLs in our Downloads section. Note that we provide a separate repository for nightly builds and for each release.</p>
<p>To use the compiler, add the following to your <code>pom.xml</code>:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-xml" data-lang="xml"><span style="display:flex;"><span><span style="color:#f92672">&lt;pluginRepositories&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#75715e">&lt;!-- Xtext is required to invoke our compiler. --&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;pluginRepository&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;id&gt;</span>xtend<span style="color:#f92672">&lt;/id&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;name&gt;</span>Xtend Maven Repository<span style="color:#f92672">&lt;/name&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;layout&gt;</span>default<span style="color:#f92672">&lt;/layout&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;url&gt;</span>http://build.eclipse.org/common/xtend/maven<span style="color:#f92672">&lt;/url&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;/pluginRepository&gt;</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span>  <span style="color:#75715e">&lt;!-- This is where our compiler is. --&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;pluginRepository&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;id&gt;</span>eclipse-elk-meta<span style="color:#f92672">&lt;/id&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;name&gt;</span>ELK Meta Data Language Compiler Maven Repository<span style="color:#f92672">&lt;/name&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;url&gt;</span>http://the.repository.url.you.want/see.download.page.for.details<span style="color:#f92672">&lt;/url&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;/pluginRepository&gt;</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">&lt;/pluginRepositories&gt;</span>
</span></span><span style="display:flex;"><span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">&lt;build&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;plugins&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#75715e">&lt;!-- Configure the Xtext plugin to invoke our compiler. --&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;plugin&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;groupId&gt;</span>org.eclipse.xtext<span style="color:#f92672">&lt;/groupId&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;artifactId&gt;</span>xtext-maven-plugin<span style="color:#f92672">&lt;/artifactId&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;version&gt;</span>2.7.2<span style="color:#f92672">&lt;/version&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;executions&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;execution&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;goals&gt;</span>
</span></span><span style="display:flex;"><span>            <span style="color:#f92672">&lt;goal&gt;</span>generate<span style="color:#f92672">&lt;/goal&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;/goals&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;/execution&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;/executions&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;configuration&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;languages&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;language&gt;</span>
</span></span><span style="display:flex;"><span>            <span style="color:#f92672">&lt;setup&gt;</span>org.eclipse.elk.core.meta.MetaDataStandaloneSetup<span style="color:#f92672">&lt;/setup&gt;</span>
</span></span><span style="display:flex;"><span>            <span style="color:#f92672">&lt;outputConfigurations&gt;</span>
</span></span><span style="display:flex;"><span>              <span style="color:#f92672">&lt;outputConfiguration&gt;</span>
</span></span><span style="display:flex;"><span>                <span style="color:#f92672">&lt;outputDirectory&gt;</span>${basedir}/src-gen/<span style="color:#f92672">&lt;/outputDirectory&gt;</span>
</span></span><span style="display:flex;"><span>              <span style="color:#f92672">&lt;/outputConfiguration&gt;</span>
</span></span><span style="display:flex;"><span>            <span style="color:#f92672">&lt;/outputConfigurations&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;/language&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;/languages&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;/configuration&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;dependencies&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;dependency&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;groupId&gt;</span>org.eclipse.elk<span style="color:#f92672">&lt;/groupId&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;artifactId&gt;</span>org.eclipse.elk.graph<span style="color:#f92672">&lt;/artifactId&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;version&gt;</span>THE_VERSION_YOU_WANT-SNAPSHOT<span style="color:#f92672">&lt;/version&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;/dependency&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;dependency&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;groupId&gt;</span>org.eclipse.elk<span style="color:#f92672">&lt;/groupId&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;artifactId&gt;</span>org.eclipse.elk.core.meta<span style="color:#f92672">&lt;/artifactId&gt;</span>
</span></span><span style="display:flex;"><span>          <span style="color:#f92672">&lt;version&gt;</span>THE_VERSION_YOU_WANT-SNAPSHOT<span style="color:#f92672">&lt;/version&gt;</span>
</span></span><span style="display:flex;"><span>        <span style="color:#f92672">&lt;/dependency&gt;</span>
</span></span><span style="display:flex;"><span>      <span style="color:#f92672">&lt;/dependencies&gt;</span>
</span></span><span style="display:flex;"><span>    <span style="color:#f92672">&lt;/plugin&gt;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">&lt;/plugins&gt;</span>
</span></span><span style="display:flex;"><span><span style="color:#f92672">&lt;/build&gt;</span>
</span></span></code></pre></div>
  </div>

  <div class="secnav col-sm-3">
  <ul>
    
    
      
    
      
    
      
        




  
  <a href="../../../documentation/tooldevelopers.html">
    <li class="navlevel-1">
      Tool Developers
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/tooldevelopers/graphdatastructure.html">
    <li class="navlevel-2">
      Graph Data Structure
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/tooldevelopers/graphdatastructure/coordinatesystem.html">
    <li class="navlevel-3">
      Coordinate System
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/graphdatastructure/layoutoptions.html">
    <li class="navlevel-3">
      Layout Options
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/graphdatastructure/spacingdocumentation.html">
    <li class="navlevel-3">
      Spacing Options
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/graphdatastructure/jsonformat.html">
    <li class="navlevel-3">
      JSON Format
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/graphdatastructure/elktextformat.html">
    <li class="navlevel-3">
      ELK Text Format
    </li>
  </a>
  


  

  
  <a href="../../../documentation/tooldevelopers/usingalgorithmsdirectly.html">
    <li class="navlevel-2">
      Using Algorithms Directly
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/usingplainjavalayout.html">
    <li class="navlevel-2">
      Using Plain Java Layout
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/usingeclipselayout.html">
    <li class="navlevel-2">
      Using Eclipse Layout
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/tooldevelopers/usingeclipselayout/connectingtoelk.html">
    <li class="navlevel-3">
      Connecting to ELK
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/usingeclipselayout/advancedconfiguration.html">
    <li class="navlevel-3">
      Advanced Configuration
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/usingeclipselayout/layoutviewsupport.html">
    <li class="navlevel-3">
      Layout View Support
    </li>
  </a>
  

  
  <a href="../../../documentation/tooldevelopers/usingeclipselayout/dependencyinjection.html">
    <li class="navlevel-3">
      Dependency Injection
    </li>
  </a>
  


  


  

  
  <a href="../../../documentation/algorithmdevelopers.html">
    <li class="navlevel-1">
      Algorithm Developers
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/algorithmdevelopers/gettingeclipseready.html">
    <li class="navlevel-2">
      Getting Eclipse Ready
    </li>
  </a>
  

  
  <a href="../../../documentation/algorithmdevelopers/creatinganewproject.html">
    <li class="navlevel-2">
      Creating a New Project
    </li>
  </a>
  

  
  <a href="../../../documentation/algorithmdevelopers/metadatalanguage.html">
    <li class="navlevel-2">
      ELK Metadata Language
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/algorithmdevelopers/metadatalanguage/automaticbuilds.html">
    <li class="navlevel-3 active">
      Automatic Builds
    </li>
  </a>
  


  

  
  <a href="../../../documentation/algorithmdevelopers/algorithmimplementation.html">
    <li class="navlevel-2">
      Algorithm Implementation
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/algorithmdevelopers/algorithmimplementation/algorithmstructure.html">
    <li class="navlevel-3">
      Structuring Algorithms
    </li>
  </a>
  


  

  
  <a href="../../../documentation/algorithmdevelopers/algorithmdebugging.html">
    <li class="navlevel-2">
      Algorithm Debugging
    </li>
  </a>
  

  
  <a href="../../../documentation/algorithmdevelopers/randomgraphs.html">
    <li class="navlevel-2">
      Random Graph Generation
    </li>
  </a>
  

  
  <a href="../../../documentation/algorithmdevelopers/unittesting.html">
    <li class="navlevel-2">
      Unit Tests
    </li>
  </a>
  


  

  
  <a href="../../../documentation/contributors.html">
    <li class="navlevel-1">
      ELK Contributors
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/contributors/developmentsetup.html">
    <li class="navlevel-2">
      Development Setup
    </li>
  </a>
  

  
  <a href="../../../documentation/contributors/developmentworkflow.html">
    <li class="navlevel-2">
      Development Workflow
    </li>
  </a>
  
    
    




  
  <a href="../../../documentation/contributors/developmentworkflow/installingwithoomph.html">
    <li class="navlevel-3">
      Installing With Oomph
    </li>
  </a>
  


  

  
  <a href="../../../documentation/contributors/buildingelk.html">
    <li class="navlevel-2">
      Building ELK
    </li>
  </a>
  


  


      
    
      
    
      
    
      
    
  </ul>

  <div class="incubation-egg">
    <a href="https://www.eclipse.org/projects/what-is-incubation.php">
      <img src="https://www.eclipse.org/images/egg-incubation.png" alt="Incubation" />
    </a>
  </div>
</div>

</div>

    </div>
    <footer role="contentinfo" class="footer">
      <div class="container">
        <div class="row">
            <div class="col">
              <span class="hidden-print">
                <a href="https://www.eclipse.org"><img class="logo-eclipse-white img-responsive" alt="logo" src="../../../img/eclipse_foundation_logo.svg"/></a>
              </span>
            </div>
            <div class="col">
              
            </div>
          </div>
        <div class="row">
          <div class="col hidden-print">
            <a href="http://www.eclipse.org/">Eclipse Foundation</a><br/>
            <a href="http://www.eclipse.org/legal/privacy.php">Privacy Policy</a><br/>
            <a href="http://www.eclipse.org/legal/termsofuse.php">Website Terms of Use</a><br/>
            <a href="http://www.eclipse.org/legal/copyright.php">Copyright Agent</a><br/>
            <a href="http://www.eclipse.org/legal">Legal</a>
          </div>
          <div class="col">
              <p class="copyright-text">Copyright &copy; Eclipse Foundation, Inc. All Rights Reserved.</p>
          </div>
        </div>
      </div>

    </footer>

    <script src="https://code.jquery.com/jquery-3.1.1.slim.min.js" integrity="sha384-A7FZj7v+d/sdmMqp/nOQwliLvUsJfDHW+k9Omg/a/EheAdgtzNs3hpfag6Ed950n" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js" integrity="sha384-DztdAPBWPRXSA/3eYEEUWrWCy7G5KFbe8fFjk5JAIxUYHKkDx6Qin1DkWx51bBrb" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script src="https://www.eclipse.org/elk/js/prism.js"></script>

    
    <script>$(function() { $('table').addClass('table'); })</script>
  </body>
</html>
