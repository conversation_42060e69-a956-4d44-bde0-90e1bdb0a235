package com.pmw790.layout;

import java.lang.reflect.Method;
import java.util.Collection;
import static com.pmw790.functions.Utilities.Log;

/**
 * Specialized ELK layout engine for FID (Functional Interface Diagrams)
 * Configures ELK algorithms optimally for hierarchical component diagrams
 * with clean presentation and minimal edge crossings
 */
public class FIDLayouter {

    // ELK classes loaded via reflection to avoid compile-time dependencies
    private static Class<?> coreOptionsClass;
    private static Class<?> layeredOptionsClass;
    private static Class<?> directionEnum;
    private static Class<?> edgeRoutingEnum;
    private static Class<?> hierarchyHandlingEnum;
    private static Class<?> crossingMinimizationEnum;
    private static Class<?> layoutEngineClass;
    private static Class<?> progressMonitorClass;
    private static Class<?> portConstraintsEnum;
    private static Class<?> nodeLayeringEnum;
    private static boolean initialized = false;
    private static final Object initLock = new Object();
    
    // Algorithm selection thresholds
    private static final int SIMPLE_DIAGRAM_NODE_THRESHOLD = 10;
    private static final int COMPLEX_DIAGRAM_NODE_THRESHOLD = 50;
    private static final double HIGH_CONNECTIVITY_RATIO = 1.5; // edges/nodes ratio
    
    /**
     * Lazy initialization of ELK classes
     */
    private static void initializeELKClasses() {
        if (initialized) return;
        
        synchronized (initLock) {
            if (initialized) return;
            
            try {
                // Use custom ELK classloader with fallback to standard loading
                ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
                
                coreOptionsClass = elkClassLoader.loadClass("org.eclipse.elk.core.options.CoreOptions");
                layeredOptionsClass = elkClassLoader.loadClass("org.eclipse.elk.layered.options.LayeredOptions");
                directionEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.Direction");
                edgeRoutingEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.EdgeRouting");
                hierarchyHandlingEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.HierarchyHandling");
                crossingMinimizationEnum = elkClassLoader.loadClass("org.eclipse.elk.layered.options.CrossingMinimizationStrategy");
                portConstraintsEnum = elkClassLoader.loadClass("org.eclipse.elk.core.options.PortConstraints");
                nodeLayeringEnum = elkClassLoader.loadClass("org.eclipse.elk.layered.options.LayeringStrategy");
                layoutEngineClass = elkClassLoader.loadClass("org.eclipse.elk.core.RecursiveGraphLayoutEngine");
                progressMonitorClass = elkClassLoader.loadClass("org.eclipse.elk.core.util.BasicProgressMonitor");
                
                initialized = true;
                Log("FIDLayouter: ELK classes loaded via classloader");
            } catch (ClassNotFoundException e) {
                Log("FIDLayouter: ELK classes not available - " + e.getMessage());
            } catch (Exception e) {
                Log("FIDLayouter: Error loading ELK classes - " + e.getMessage());
            }
        }
    }

    /**
     * Apply ELK layout algorithm to a FID graph with automatic algorithm selection
     * Analyzes diagram characteristics to choose optimal layout algorithm
     * 
     * @param elkGraph The ELK graph to layout
     * @return The laid-out ELK graph
     */
    public Object layoutFID(Object elkGraph) throws Exception {
        return layoutFID(elkGraph, null);
    }
    
    /**
     * Apply ELK layout algorithm to a FID graph with specified or automatic algorithm selection
     * 
     * @param elkGraph The ELK graph to layout
     * @param algorithmHint Preferred algorithm ("layered", "force", "tree", "box") or null for auto-selection
     * @return The laid-out ELK graph
     */
    public Object layoutFID(Object elkGraph, String algorithmHint) throws Exception {
        initializeELKClasses();
        
        if (coreOptionsClass == null || layeredOptionsClass == null || 
            layoutEngineClass == null || progressMonitorClass == null) {
            throw new RuntimeException("ELK classes not available for FID layout");
        }

        // Select and configure the optimal algorithm
        String selectedAlgorithm = selectOptimalAlgorithm(elkGraph, algorithmHint);
        configureFIDLayout(elkGraph, selectedAlgorithm);

        // Create layout engine and apply layout
        Object layoutEngine = layoutEngineClass.newInstance();
        Object progressMonitor = progressMonitorClass.newInstance();
        
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
        Method layoutMethod = layoutEngineClass.getMethod("layout", 
            elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode"),
            elkClassLoader.loadClass("org.eclipse.elk.core.util.IElkProgressMonitor"));
            
        layoutMethod.invoke(layoutEngine, elkGraph, progressMonitor);
        
        Log("ELK FID layout completed successfully");
        return elkGraph;
    }

    /**
     * Select optimal ELK algorithm based on diagram characteristics
     */
    private String selectOptimalAlgorithm(Object elkGraph, String algorithmHint) throws Exception {
        if (algorithmHint != null && !algorithmHint.trim().isEmpty()) {
            Log("Using specified algorithm: " + algorithmHint);
            return algorithmHint.toLowerCase();
        }
        
        DiagramCharacteristics characteristics = analyzeDiagramCharacteristics(elkGraph);
        String selectedAlgorithm = "layered"; // default
        
        if (characteristics.isSimpleTree()) {
            selectedAlgorithm = "tree";
            Log("Selected ELK Tree algorithm for simple tree structure");
        } else if (characteristics.isHighlyConnected()) {
            selectedAlgorithm = "force";
            Log("Selected ELK Force algorithm for highly connected diagram");
        } else if (characteristics.isSimpleDiagram()) {
            selectedAlgorithm = "box";
            Log("Selected ELK Box algorithm for simple diagram");
        } else {
            Log("Selected ELK Layered algorithm for hierarchical FID structure");
        }
        
        Log(String.format("Diagram analysis: %d nodes, %d edges, connectivity ratio: %.2f, max depth: %d",
            characteristics.nodeCount, characteristics.edgeCount, 
            characteristics.connectivityRatio, characteristics.maxDepth));
        
        return selectedAlgorithm;
    }
    
    /**
     * Analyze diagram characteristics for algorithm selection
     */
    private DiagramCharacteristics analyzeDiagramCharacteristics(Object elkGraph) throws Exception {
        DiagramCharacteristics characteristics = new DiagramCharacteristics();
        
        // Count nodes and edges recursively
        countNodesAndEdges(elkGraph, characteristics, 0);
        
        // Calculate connectivity ratio
        characteristics.connectivityRatio = characteristics.nodeCount > 0 ? 
            (double) characteristics.edgeCount / characteristics.nodeCount : 0.0;
            
        return characteristics;
    }
    
    /**
     * Recursively count nodes and edges in the graph
     */
    @SuppressWarnings("unchecked")
    private void countNodesAndEdges(Object elkNode, DiagramCharacteristics characteristics, int depth) throws Exception {
        characteristics.nodeCount++;
        characteristics.maxDepth = Math.max(characteristics.maxDepth, depth);
        
        // Count edges from this node
        try {
            Collection<?> outgoingEdges = (Collection<?>) elkNode.getClass().getMethod("getOutgoingEdges").invoke(elkNode);
            if (outgoingEdges != null) {
                characteristics.edgeCount += outgoingEdges.size();
            }
        } catch (Exception e) {
            // Edge counting failed, continue without it
        }
        
        // Recursively process children
        try {
            Collection<?> children = (Collection<?>) elkNode.getClass().getMethod("getChildren").invoke(elkNode);
            if (children != null) {
                for (Object child : children) {
                    countNodesAndEdges(child, characteristics, depth + 1);
                }
            }
        } catch (Exception e) {
            // Child processing failed, continue without it
        }
    }
    
    /**
     * Configure ELK for FID layout with specified algorithm
     * Focuses on clean presentation, minimal edge crossings, and hierarchical organization
     */
    private void configureFIDLayout(Object elkGraph, String algorithm) throws Exception {
        // Set selected algorithm
        String elkAlgorithm = getELKAlgorithmName(algorithm);
        setProperty(elkGraph, "ALGORITHM", elkAlgorithm);
        
        // Configure hierarchy handling for nested components (parts within parts)
        setProperty(elkGraph, "HIERARCHY_HANDLING", getEnumValue(hierarchyHandlingEnum, "INCLUDE_CHILDREN"));
        
        // Set layout direction - LEFT_TO_RIGHT works well for FID flow
        setProperty(elkGraph, "DIRECTION", getEnumValue(directionEnum, "RIGHT"));
        
        // Enable orthogonal edge routing for clean connector lines in FID
        setProperty(elkGraph, "EDGE_ROUTING", getEnumValue(edgeRoutingEnum, "ORTHOGONAL"));
        
        // Minimize edge crossings - critical for FID readability
        setProperty(elkGraph, "CROSSING_MINIMIZATION_STRATEGY", 
                   getEnumValue(crossingMinimizationEnum, "LAYER_SWEEP"));
        
        // Configure node layering for logical FID arrangement
        setProperty(elkGraph, "LAYERING_STRATEGY", 
                   getEnumValue(nodeLayeringEnum, "NETWORK_SIMPLEX"));
        
        // Set spacing parameters optimized for FID components
        setProperty(elkGraph, "SPACING_NODE_NODE", 80.0);        // Space between parts
        setProperty(elkGraph, "SPACING_EDGE_NODE", 25.0);        // Space between connectors and parts
        setProperty(elkGraph, "SPACING_EDGE_EDGE", 20.0);        // Space between parallel connectors
        setProperty(elkGraph, "SPACING_COMPONENT_COMPONENT", 40.0); // Space between component groups
        
        // Configure ports for proper connector attachment in FID
        setProperty(elkGraph, "PORT_CONSTRAINTS", getEnumValue(portConstraintsEnum, "FIXED_ORDER"));
        
        // Additional FID-specific optimizations
        setProperty(elkGraph, "SEPARATE_CONNECTED_COMPONENTS", false); // Keep components together
        setProperty(elkGraph, "INTERACTIVE_REFERENCE_POINT", "CENTER"); // Center-based layout
        
        Log("Configured ELK layered layout for FID diagram");
    }

    /**
     * Configure ELK for FID with cabinet grouping integration
     * Preserves cabinet-based grouping while optimizing overall layout
     */
    public Object layoutFIDWithCabinetGrouping(Object elkGraph) throws Exception {
        initializeELKClasses();
        
        if (coreOptionsClass == null) {
            throw new RuntimeException("ELK classes not available for FID cabinet layout");
        }

        // Base FID configuration with layered algorithm for cabinet grouping
        configureFIDLayout(elkGraph, "layered");
        
        // Additional settings for cabinet-based grouping
        setProperty(elkGraph, "SPACING_NODE_NODE", 100.0);       // More space for cabinet groups
        setProperty(elkGraph, "SPACING_COMPONENT_COMPONENT", 60.0); // Space between cabinet groups
        
        // Prefer keeping related elements together (within cabinets)
        setProperty(elkGraph, "CONSIDER_MODEL_ORDER_STRATEGY", "PREFER_EDGES");
        
        // Create layout engine and apply layout
        Object layoutEngine = layoutEngineClass.newInstance();
        Object progressMonitor = progressMonitorClass.newInstance();
        
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
        Method layoutMethod = layoutEngineClass.getMethod("layout", 
            elkClassLoader.loadClass("org.eclipse.elk.graph.ElkNode"),
            elkClassLoader.loadClass("org.eclipse.elk.core.util.IElkProgressMonitor"));
            
        layoutMethod.invoke(layoutEngine, elkGraph, progressMonitor);
        
        Log("ELK FID layout with cabinet grouping completed successfully");
        return elkGraph;
    }

    /**
     * Set a property on an ELK graph using reflection
     */
    private void setProperty(Object elkGraph, String propertyName, Object value) throws Exception {
        // Get the property field from CoreOptions or LayeredOptions
        Object property = null;
        
        try {
            property = coreOptionsClass.getField(propertyName).get(null);
        } catch (NoSuchFieldException e) {
            try {
                property = layeredOptionsClass.getField(propertyName).get(null);
            } catch (NoSuchFieldException e2) {
                Log("Warning: ELK property not found: " + propertyName);
                return;
            }
        }
        
        // Set the property on the graph using ELK classloader
        ClassLoader elkClassLoader = ELKClassLoader.getELKClassLoader();
        Class<?> iPropertyClass = elkClassLoader.loadClass("org.eclipse.elk.graph.properties.IProperty");
        Method setPropertyMethod = elkGraph.getClass().getMethod("setProperty", 
            iPropertyClass, Object.class);
        setPropertyMethod.invoke(elkGraph, property, value);
    }
    
    /**
     * Map algorithm names to ELK algorithm identifiers
     */
    private String getELKAlgorithmName(String algorithm) {
        switch (algorithm.toLowerCase()) {
            case "force":
                return "org.eclipse.elk.force";
            case "tree":
                return "org.eclipse.elk.mrtree";
            case "box":
                return "org.eclipse.elk.box";
            case "layered":
            default:
                return "org.eclipse.elk.layered";
        }
    }
    
    /**
     * Inner class to hold diagram analysis results
     */
    private static class DiagramCharacteristics {
        int nodeCount = 0;
        int edgeCount = 0;
        int maxDepth = 0;
        double connectivityRatio = 0.0;
        
        boolean isSimpleTree() {
            return nodeCount <= SIMPLE_DIAGRAM_NODE_THRESHOLD && 
                   connectivityRatio <= 1.0 && 
                   maxDepth > 2;
        }
        
        boolean isHighlyConnected() {
            return connectivityRatio >= HIGH_CONNECTIVITY_RATIO;
        }
        
        boolean isSimpleDiagram() {
            return nodeCount <= SIMPLE_DIAGRAM_NODE_THRESHOLD && 
                   connectivityRatio < HIGH_CONNECTIVITY_RATIO;
        }
    }

    /**
     * Get an enum value by name using reflection
     */
    private Object getEnumValue(Class<?> enumClass, String valueName) throws Exception {
        if (enumClass == null) {
            return null;
        }
        return Enum.valueOf((Class<Enum>) enumClass, valueName);
    }
}