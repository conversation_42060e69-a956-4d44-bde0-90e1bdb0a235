<?xml version="1.0" standalone="no"?>

<svg 
     version="1.1"
     baseProfile="full"
     xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:ev="http://www.w3.org/2001/xml-events"
     xmlns:klighd="http://de.cau.cs.kieler/klighd"
     x="0px"
     y="0px"
     width="116px"
     height="86px"
     viewBox="0 0 116 86"
     >
<title></title>
<desc>Creator: FreeHEP Graphics2D Driver Producer: de.cau.cs.kieler.klighd.piccolo.freehep.SemanticSVGGraphics2D Revision Source:  Date: Friday, September 2, 2022 at 3:42:42 PM Central European Summer Time</desc>
<g stroke-dashoffset="0" stroke-linejoin="miter" stroke-dasharray="none" stroke-width="1" stroke-linecap="butt" stroke-miterlimit="10">
<g fill="#ffffff" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 116 0 L 116 85.5 L 0 85.5 L 0 0 z"/>
</g>
<g transform="matrix(1, 0, 0, 1, 0, 8.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 3, 12)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n1</text>
</g>
<g transform="matrix(1, 0, 0, 1, 61, 24.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 64, 28)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n2</text>
</g>
<g transform="matrix(1, 0, 0, 1, 96, 27.833332061767578)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 99, 31.333332061767578)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n3</text>
</g>
<g transform="matrix(1, 0, 0, 1, 61, 49.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 19.5 0.5 L 19.5 19.5 L 0.5 19.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 64, 53)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="14.0px" lengthAdjust="spacingAndGlyphs">n4</text>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -7.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 32 26 L 37 26 L 37 42 L 73 42"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 53, 31.5)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 53, 31.5)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 25.5)">
<g fill="#646464" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 25.5)">
<g stroke-opacity="1" fill="none" stroke="#646464">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 25.5)">
<g fill="#ffffff" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 25.5)">
<g stroke-opacity="0" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 37, 27.5)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="7.0px" lengthAdjust="spacingAndGlyphs">2</text>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -7.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 32 31 L 34.5 31 L 34.5 79.5 L 95.5 79.5 L 95.5 48.66666793823242 L 108 48.66666793823242"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 88, 38.16666793823242)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 88, 38.16666793823242)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 63)">
<g fill="#646464" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 63)">
<g stroke-opacity="1" fill="none" stroke="#646464">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 63)">
<g fill="#ffffff" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 63)">
<g stroke-opacity="0" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 37, 65)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="7.0px" lengthAdjust="spacingAndGlyphs">3</text>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -7.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 93 42 L 108 42"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 88, 31.5)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 88, 31.5)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, -12, -7.5)">
<g stroke-opacity="1" fill="none" stroke="#000000">
  <path d="M 32 21 L 60.5 21 L 60.5 67 L 73 67"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 53, 56.5)">
<g fill="#000000" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 53, 56.5)">
<g stroke-opacity="1" stroke-linejoin="round" fill="none" stroke="#000000">
  <path d="M 0 0 L 3.200000047683716 3 L 0 6 L 8 3 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 4.5)">
<g fill="#646464" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 46, 4.5)">
<g stroke-opacity="1" fill="none" stroke="#646464">
  <path d="M 0 5 L 0 13 L 4 9 L 0 5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 4.5)">
<g fill="#ffffff" fill-rule="nonzero" fill-opacity="1" stroke="none">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 35, 4.5)">
<g stroke-opacity="0" fill="none" stroke="#000000">
  <path d="M 0.5 0.5 L 10.5 0.5 L 10.5 17.5 L 0.5 17.5 L 0.5 0.5 z"/>
</g>
</g>
<g transform="matrix(1, 0, 0, 1, 37, 6.5)">
  <text x="0" y="10.0" font-weight="normal" font-size="10.666666984558105px" font-style="normal" fill="#000000" stroke="none" font-family="Helvetica" style="white-space: pre" fill-opacity="1" textLength="7.0px" lengthAdjust="spacingAndGlyphs">1</text>
</g>
</g>
</svg>
