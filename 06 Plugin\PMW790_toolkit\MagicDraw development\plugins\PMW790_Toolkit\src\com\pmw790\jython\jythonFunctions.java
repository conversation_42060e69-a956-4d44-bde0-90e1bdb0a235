package com.pmw790.jython;

import javax.swing.JFileChooser;
import javax.swing.JOptionPane;
import javax.swing.SwingWorker;
import java.awt.event.ActionEvent;
import java.io.File;
import org.python.util.PythonInterpreter;
import com.nomagic.magicdraw.actions.MDAction;
import com.nomagic.magicdraw.core.Application;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Element;
import com.pmw790.functions.Utilities;
import com.pmw790.layout.ELKLayoutService;
import com.pmw790.main.PMW790Plugin;

public class jythonFunctions {
	private final PythonInterpreter interp;
	private final String baseDir;
	private final ApiManager apiManager;

	public jythonFunctions(PythonInterpreter interp, String baseDir) {
		this.interp = interp;
		this.baseDir = baseDir;
		this.apiManager = new ApiManager(interp, baseDir);
		this.apiManager.setJythonFunctions(this);
	}

	public void importSystem(String jsonFolderPath) {
		showProgressDialogAndImport(jsonFolderPath);
	}

	private void showProgressDialogAndImport(String jsonFolderPath) {
		BaseProgressDialog progressHelper = new BaseProgressDialog("Importing System Data");
		
		// Set up cancellation callback for the progress dialog
		progressHelper.setCancellationCallback(() -> {
			if (interp != null) {
				interp.set("cancelled", true);
			}
		});
		
		final Exception[] exception = {null};
		final long[] startTime = {System.currentTimeMillis()};

		SwingWorker<Void, Object[]> importWorker = new SwingWorker<Void, Object[]>() {
			@Override
			protected Void doInBackground() throws Exception {
				if (isCancelled()) return null;
				
				startTime[0] = System.currentTimeMillis();
				publish(new Object[]{5, "Initializing import process..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_INIT);
				if (isCancelled()) return null;

				publish(new Object[]{15, "Setting up import parameters..."});
				ScriptExecutor.setupImportParams(interp, jsonFolderPath, baseDir);
				
				if (isCancelled()) return null;
				publish(new Object[]{25, "Scanning JSON files in: " + new File(jsonFolderPath).getName()});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_MEDIUM);
				if (isCancelled()) return null;

				publish(new Object[]{35, "Loading import script..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_SHORT);
				if (isCancelled()) return null;

				publish(new Object[]{45, "Executing import process for: " + new File(jsonFolderPath).getName()});
				
				// Set up progress callback and cancellation flag for Python script
				interp.set("progressCallback", new ProgressCallback() {
					@Override
					public void updateProgress(int percentage, String message) {
						if (!isCancelled()) {
							// Map Python progress (0-100) to our remaining range (45-90)
							int mappedProgress = 45 + (int)(percentage * 0.45);
							publish(new Object[]{mappedProgress, message});
						}
					}
				});
				
				// Initialize cancellation flag
				interp.set("cancelled", false);
				
				ScriptExecutor.executeImporterScript(interp, baseDir, JythonConstants.MAIN_SCRIPT);
				
				if (isCancelled()) return null;
				publish(new Object[]{95, "Finalizing import..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_MEDIUM);
				
				publish(new Object[]{100, "Import completed successfully!"});
				
				return null;
			}

			@Override
			protected void process(java.util.List<Object[]> chunks) {
				if (!chunks.isEmpty()) {
					Object[] lastChunk = chunks.get(chunks.size() - 1);
					if (lastChunk.length >= 2) {
						int percentage = (Integer) lastChunk[0];
						String message = (String) lastChunk[1];
						progressHelper.setProgressWithStatus(percentage, message);
					}
				}
			}

			@Override
			protected void done() {
				progressHelper.dispose();
				try {
					if (progressHelper.isCancelled()) {
						JOptionPane.showMessageDialog(null,
								"Import operation was cancelled by user.",
								"Import Cancelled",
								JOptionPane.INFORMATION_MESSAGE);
						return;
					}
					
					get();
					
					// Clear and reactivate caches after successful import
					clearAndReactivateCaches();
					
					long endTime = System.currentTimeMillis();
					double executionTime = (endTime - startTime[0]) / 1000.0;
					JOptionPane.showMessageDialog(null,
							"Import completed successfully!\n\n" +
									"Data imported from: " + jsonFolderPath + "\n" +
									"Total execution time: " + String.format("%.2f", executionTime) + " seconds\n",
							"Import Complete",
							JOptionPane.INFORMATION_MESSAGE);
					
				} catch (Exception e) {
					if (!progressHelper.isCancelled()) {
						exception[0] = e;
						JOptionPane.showMessageDialog(null,
								"Import failed: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()),
								"Import Error",
								JOptionPane.ERROR_MESSAGE);
						Utilities.Log("[PMW790Jython] Error in importSystem: " + e.getClass().getSimpleName() + " - " + e.getMessage());
						e.printStackTrace();
					}
				}
			}
		};

		progressHelper.executeTask(importWorker);
	}

	public void createFIDL2(Element selectedElement, int depth) {
		ScriptExecutor.setupFidL2Params(interp, selectedElement, depth, baseDir);
		ScriptExecutor.executeFidBuilderScript(interp, baseDir, JythonConstants.FID_BUILDER_SCRIPT);
	}

	public void updateElementImages() {
		ScriptExecutor.executeStandaloneScript(interp, baseDir, JythonConstants.UPDATE_IMAGES_SCRIPT);
		interp.exec("update_element_images()");
	}

	private void clearAndReactivateCaches() {
		try {
			com.nomagic.magicdraw.core.Project currentProject = PMW790Plugin.getCurrentProject();
			if (currentProject != null) {
				// Clear all caches first
				Utilities.clearCaches();
				com.pmw790.functions.SysMLStereotypes.clearCaches();
				com.pmw790.functions.ConnectionRegistry.getInstance().reset();
				com.pmw790.schema.BindingSchemaManager.getInstance().clearCache();
				com.pmw790.diagram.PowerConnectorManager.clearConnectorCache();
				Utilities.ModelElements.clearProjectCaches(currentProject);
				
				// Force garbage collection
				System.gc();
				
				// Reactivate caches with current project
				com.pmw790.functions.SysMLStereotypes.initialize(currentProject);
				Utilities.findPowerBlocks();
				com.pmw790.schema.BindingSchemaManager.getInstance().initialize(currentProject);
				com.pmw790.functions.ConnectionRegistry.getInstance().analyzeModelConnections(currentProject);
				Utilities.findAndCacheRoomBlocks(currentProject);
			} else {
				Utilities.Log("[PMW790Jython] Warning: No current project found for cache refresh");
			}
		} catch (Exception e) {
			Utilities.Log("[PMW790Jython] Error during cache refresh: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public void importSystemApi() {
		apiManager.importSystemApi();
	}

	public File exportJsonData() {
		try {
			File exportFolder = FileDialogHelper.chooseDirectory(null, "Select Export Folder");
			if (exportFolder == null) {
				return null;
			}

			String userExportFileName = FileDialogHelper.promptForExportFilename(null);
			if (userExportFileName == null) {
				return null;
			}

			// Create filenames
			String exportFileName = FileDialogHelper.ensureJsonExtension(userExportFileName);
			String catalogFileName = FileDialogHelper.ensureJsonExtension(FileDialogHelper.createCatalogFilename());

			File exportFile = new File(exportFolder, exportFileName);
			File catalogFile = new File(exportFolder, catalogFileName);

			// Check if export file already exists and ask for confirmation
			if (!FileDialogHelper.confirmOverwrite(null, exportFile)) {
				return null;
			}

			// Find the export script
			if (!ScriptExecutor.standaloneScriptExists(baseDir, JythonConstants.EXPORT_SCRIPT)) {
				throw new RuntimeException(JythonConstants.EXPORT_SCRIPT + " script not found. Expected location:\n" +
						ScriptExecutor.getStandaloneScriptPath(baseDir, JythonConstants.EXPORT_SCRIPT));
			}

			// Create and show progress dialog
			return showProgressDialogAndExport(exportFile, catalogFile);

		} catch (Exception e) {
			e.printStackTrace();
			JOptionPane.showMessageDialog(null,
					"Export failed: " + e.getMessage(),
					"Export Error",
					JOptionPane.ERROR_MESSAGE);
			return null;
		}
	}

	private File showProgressDialogAndExport(File exportFile, File catalogFile) {
		BaseProgressDialog progressHelper = new BaseProgressDialog("Exporting Model Data");
		
		final File[] result = {null};
		final Exception[] exception = {null};

		SwingWorker<File, Object[]> exportWorker = new SwingWorker<File, Object[]>() {
			@Override
			protected File doInBackground() throws Exception {
				if (isCancelled()) return null;
				
				publish(new Object[]{10, "Setting up export parameters..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_INIT);
				if (isCancelled()) return null;

				ScriptExecutor.setupExportParams(interp, exportFile.getAbsolutePath(), catalogFile.getAbsolutePath());

				if (isCancelled()) return null;
				publish(new Object[]{30, "Executing export script..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_SHORT);
				if (isCancelled()) return null;

				ScriptExecutor.executeStandaloneScript(interp, baseDir, JythonConstants.EXPORT_SCRIPT);

				if (isCancelled()) return null;
				publish(new Object[]{90, "Verifying export..."});
				Thread.sleep(JythonConstants.PROGRESS_DELAY_MEDIUM);
				if (isCancelled()) return null;

				if (exportFile.exists() && exportFile.length() > 0) {
					publish(new Object[]{100, "Export completed successfully!"});
					return exportFile;
				} else {
					throw new RuntimeException("Export file was not created or is empty");
				}
			}

			@Override
			protected void process(java.util.List<Object[]> chunks) {
				if (!chunks.isEmpty()) {
					Object[] lastChunk = chunks.get(chunks.size() - 1);
					if (lastChunk.length >= 2) {
						int percentage = (Integer) lastChunk[0];
						String message = (String) lastChunk[1];
						progressHelper.setProgressWithStatus(percentage, message);
					}
				}
			}

			@Override
			protected void done() {
				progressHelper.dispose();
				try {
					if (progressHelper.isCancelled()) {
						JOptionPane.showMessageDialog(null,
								"Export operation was cancelled by user.",
								"Export Cancelled",
								JOptionPane.INFORMATION_MESSAGE);
						return;
					}
					
					result[0] = get();
					if (result[0] != null) {
						JOptionPane.showMessageDialog(null,
								"Export completed successfully!\n\n" +
										"Model export: " + exportFile.getAbsolutePath() + "\n" +
										"Parts catalog: " + catalogFile.getAbsolutePath() + "\n" +
										"Size: " + exportFile.length() + " bytes",
								"Export Complete",
								JOptionPane.INFORMATION_MESSAGE);
					}
				} catch (Exception e) {
					if (!progressHelper.isCancelled()) {
						exception[0] = e;
						JOptionPane.showMessageDialog(null,
								"Export failed: " + (e.getCause() != null ? e.getCause().getMessage() : e.getMessage()),
								"Export Error",
								JOptionPane.ERROR_MESSAGE);
					}
				}
			}
		};

		progressHelper.executeTask(exportWorker);

		if (exception[0] != null) {
			return null;
		}
		return result[0];
	}

	public void exportOptions() {
		Object[] options = {"Save to File", "Send to API", "Cancel"};
		int choice = JOptionPane.showOptionDialog(null,
				"Choose action:\n\n",
				"Export Model Data",
				JOptionPane.DEFAULT_OPTION,
				JOptionPane.QUESTION_MESSAGE,
				null,
				options,
				options[0]); // Default to "Export to File"

		if (choice == 0) {
			// Export to file only
			exportJsonData();
		} else if (choice == 1) {
			// Send data to API
			apiManager.sendDataToApi();
		}
		// choice == 2 or dialog closed: Cancel, do nothing
	}

	public static class importSystemAction extends MDAction {
		private final jythonFunctions importer;

		public importSystemAction(jythonFunctions importer) {
			super("IMPORT SYSTEM", "Import System", null, null);
			this.importer = importer;
		}

		@Override
		public void actionPerformed(ActionEvent e) {
			Object[] options = {"Local File(s)", "API", "Cancel"};
			int choice = JOptionPane.showOptionDialog(null,
					"Select import source:",
					"Import System",
					JOptionPane.DEFAULT_OPTION,
					JOptionPane.QUESTION_MESSAGE,
					null,
					options,
					options[1]);

			if (choice == 0) {
				File folder = FileDialogHelper.chooseDirectory(null, "Select JSON Folder");
				if (folder != null) {
					importer.importSystem(folder.getAbsolutePath());
				}
			} else if (choice == 1) {
				importer.importSystemApi();
			}
		}
	}

	public static class exportJsonAction extends MDAction {
		private final jythonFunctions exporter;

		public exportJsonAction(jythonFunctions exporter) {
			super("EXPORT_JSON", "Export Model Data", null, null);
			this.exporter = exporter;
		}

		@Override
		public void actionPerformed(ActionEvent e) {
			exporter.exportOptions();
		}
	}

	public static class fidl2Action extends MDAction {
		private final jythonFunctions helper;
		private final Element selectedElement;
		private final int depth;

		public fidl2Action(jythonFunctions helper, Element selectedElement, int depth) {
			super("CREATE L2 FID", "Create L2 FID - IN MODEL", null, null);
			this.helper = helper;
			this.selectedElement = selectedElement;
			this.depth = depth;
		}

		@Override
		public void actionPerformed(ActionEvent e) {
			helper.createFIDL2(selectedElement, depth);
		}
	}

	public static class updateElementImages extends MDAction {
		private final jythonFunctions helper;

		public updateElementImages(jythonFunctions helper) {
			super("UPDATE ELEMENT IMAGES","Update Element Images (for FID Generation)", null, null);
			this.helper = helper;
		}
		@Override
		public void actionPerformed(ActionEvent e) {
			helper.updateElementImages();
		}
	}

	// ====== ELK LAYOUT INTEGRATION METHODS ======

	/**
	 * Check if ELK layout libraries are available
	 * @return true if ELK can be used, false otherwise
	 */
	public boolean isELKAvailable() {
		return true;
	}

	/**
	 * Debug ELK classpath issues - call this to get detailed diagnostics
	 * Improved for MagicDraw 2024x compatibility
	 */



	/**
	 * Apply ELK layout to a diagram from Jython code
	 * @param diagram The diagram presentation element to layout
	 * @param algorithmType The ELK algorithm type ("layered", "force", "orthogonal")
	 */
	public void applyELKLayout(Object diagram, String algorithmType) {
		if (diagram instanceof com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) {
			com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement dpe = 
				(com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) diagram;
			ELKLayoutService.layoutDiagramAsync(dpe, algorithmType);
		} else {
			Utilities.Log("Error: Invalid diagram object provided to applyELKLayout");
		}
	}

	/**
	 * Apply ELK layout optimized for FID (Functional Interface Diagrams)
	 * @param diagram The FID diagram presentation element to layout
	 */
	public void applyELKFIDLayout(Object diagram) {
		if (diagram instanceof com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) {
			com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement dpe = 
				(com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) diagram;
			ELKLayoutService.layoutFIDAsync(dpe);
		} else {
			Utilities.Log("Error: Invalid diagram object provided to applyELKFIDLayout");
		}
	}

	/**
	 * Apply ELK layout to FID with cabinet grouping integration
	 * Integrates with display_tools.py cabinet grouping logic
	 * @param diagram The FID diagram presentation element to layout
	 * @param systemCabinetMap Cabinet grouping data from rearrange_items()
	 */
	public void applyELKFIDLayoutWithGrouping(Object diagram, Object systemCabinetMap) {
		if (diagram instanceof com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) {
			com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement dpe = 
				(com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement) diagram;
			ELKLayoutService.layoutFIDWithGroupingAsync(dpe, systemCabinetMap);
		} else {
			Utilities.Log("Error: Invalid diagram object provided to applyELKFIDLayoutWithGrouping");
		}
	}

	// ====== END ELK LAYOUT INTEGRATION ======
}