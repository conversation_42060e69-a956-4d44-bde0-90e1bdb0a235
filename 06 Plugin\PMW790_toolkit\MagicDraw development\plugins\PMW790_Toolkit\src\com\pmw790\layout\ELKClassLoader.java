package com.pmw790.layout;

import com.pmw790.functions.Utilities;
import com.pmw790.main.PMW790Plugin;
import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;
public class ELKClassLoader {
    
    private static URLClassLoader elkClassLoader = null;
    private static boolean elkClassLoaderInitialized = false;
    private static final Object initLock = new Object();
    
    public static synchronized void initializeELKClassLoader() {
        if (elkClassLoaderInitialized) {
            return;
        }
        
        synchronized (initLock) {
            if (elkClassLoaderInitialized) {
                return;
            }
            
            elkClassLoaderInitialized = true;
            
            try {
                // Get the plugin directory from PMW790Plugin
                File pluginDir = PMW790Plugin.getPluginDirectory();
                if (pluginDir == null) {
                    Utilities.Log("Plugin directory not available from PMW790Plugin");
                    return;
                }
                
                File libDir = new File(pluginDir, "lib");
                
                if (!libDir.exists() || !libDir.isDirectory()) {
                    Utilities.Log("ELK lib directory not found at: " + libDir.getAbsolutePath());
                    return;
                }
                
                // Find ELK JAR files
                List<URL> elkJarUrls = new ArrayList<>();
                File[] jarFiles = libDir.listFiles((dir, name) -> 
                    name.toLowerCase().contains("elk") && name.endsWith(".jar"));
                
                if (jarFiles == null || jarFiles.length == 0) {
                    Utilities.Log("No ELK JAR files found in lib directory: " + libDir.getAbsolutePath());
                    return;
                }

                // Create custom classloader
                for (File jarFile : jarFiles) {
                    URL jarUrl = jarFile.toURI().toURL();
                    elkJarUrls.add(jarUrl);
                }

                URL[] urlArray = elkJarUrls.toArray(new URL[0]);
                ClassLoader parentClassLoader = Thread.currentThread().getContextClassLoader();
                if (parentClassLoader == null) {
                    parentClassLoader = ELKClassLoader.class.getClassLoader();
                }
                
                elkClassLoader = new URLClassLoader(urlArray, parentClassLoader) {
                    @Override
                    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
                        if (name.startsWith("org.eclipse.elk")) {
                            try {
                                Class<?> c = findClass(name);
                                if (resolve) {
                                    resolveClass(c);
                                }
                                return c;
                            } catch (ClassNotFoundException e) {
                                // Fall back
                            }
                        }
                        
                        // For all other classes, use standard parent-first delegation
                        return super.loadClass(name, resolve);
                    }
                };
                
            } catch (Exception e) {
                Utilities.Log("Error initializing ELK ClassLoader: " + e.getMessage());
                e.printStackTrace();
                elkClassLoader = null;
            }
        }
    }

    /**
     * Get the ELK classloader, initializing it if necessary
     */
    public static ClassLoader getELKClassLoader() {
        if (!elkClassLoaderInitialized) {
            initializeELKClassLoader();
        }

        return elkClassLoader != null ? elkClassLoader : ELKClassLoader.class.getClassLoader();
    }

    /**
     * Reset the classloader - useful for debugging or reinitialization
     */
    public static synchronized void reset() {
        elkClassLoaderInitialized = false;
        if (elkClassLoader != null) {
            try {
                elkClassLoader.close();
            } catch (Exception e) {
                Utilities.Log("Error closing ELK classloader: " + e.getMessage());
            }
        }
        elkClassLoader = null;
        Utilities.Log("ELK ClassLoader reset completed");
    }
}